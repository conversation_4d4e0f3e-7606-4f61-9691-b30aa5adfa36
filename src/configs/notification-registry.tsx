'use client';

import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { useEffect } from 'react';
import useNotificationsStore from 'store/useNotificationStore';
import { useUserStore } from 'store/useUserStore';

const NotificationRegistry = () => {
  const { getFirstTime, getNewNotiCount, setLoadedFirstTime } = useNotificationsStore();
  const user = useUserStore((state) => state.user);

  const refetchInterval = 3 * 60 * 1000;

  useQuery({
    queryKey: ['get-noti-first-time'],
    queryFn: getFirstTime,
    gcTime: 12 * 60 * 60,
    placeholderData: keepPreviousData,
    refetchInterval,
    enabled: !!user,
  });

  useQuery({
    queryKey: ['get-new-noti-count'],
    queryFn: getNewNotiCount,
    gcTime: 12 * 60 * 60,
    refetchInterval,
    placeholderData: keepPreviousData,
    enabled: !!user,
  });

  useEffect(() => {
    setLoadedFirstTime();
  }, [setLoadedFirstTime]);

  return null;
};

export default NotificationRegistry;
