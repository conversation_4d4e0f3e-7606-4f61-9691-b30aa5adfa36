// this config is used in both client and server side
export const basePathProd = '/fe/packaging-house';

// this config is used in client side
const assetDomain = typeof window !== 'undefined' ? window.__ENV__?.NEXT_PUBLIC_ASSET_DOMAIN : '';

// this config is used in server side
const publicHost = process?.env.NEXT_PUBLIC_HOST;

const controllerUrl = `${publicHost}/api/controller/api`;

const s3Proxy = `${publicHost}/api/s3-proxy`;

export const AppConfig = {
  CONTROLLER_URL: controllerUrl,
  ASSET_DOMAIN_URL: assetDomain,
  EXTERNAL_NEXT_ROUTE: `${basePathProd}/api/proxy`,
  S3_PROXY_URL: s3Proxy,
  PORTAL_URL: typeof window !== 'undefined' ? window.__ENV__?.NEXT_PUBLIC_HOST : publicHost,
  basePathProd,
};
