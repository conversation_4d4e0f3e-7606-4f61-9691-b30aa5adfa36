/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { CreateEvent } from '../create';
import { theme } from 'styles/theme';

// Mock all dependencies
jest.mock('next-intl', () => ({
  useTranslations: () => (key: string) => key
}));

jest.mock('next/navigation', () => ({
  useRouter: () => ({ push: jest.fn(), back: jest.fn() })
}));

jest.mock('@tanstack/react-query', () => ({
  useQuery: jest.fn(() => ({ data: { data: [] }, isLoading: false, error: null })),
}));

jest.mock('hooks/mutates/useCreateHarvestingMutate', () => ({
  useCreateHarvestingMutate: () => ({ mutateAsync: jest.fn() }),
}));

jest.mock('hooks/mutates/useUpdateHarvestingMutate', () => ({
  useUpdateHarvestingMutate: () => ({ mutateAsync: jest.fn() }),
}));

jest.mock('utils/cookie-client', () => ({ getCookieLocale: jest.fn(() => 'th') }));
jest.mock('utils', () => ({
  formatNumberWithCommas: jest.fn((value) => value?.toString() ?? '0'),
  getImageUrl: jest.fn((url) => (url ? `https://example.com/${url}` : null)),
}));
jest.mock('utils/event', () => ({
  convertModalDataToVarieties: jest.fn(),
  convertResponseToWeights: jest.fn(),
  reorderToOriginalOrder: jest.fn(),
  transformDurianDataEvent: jest.fn(() => []),
}));
jest.mock('utils/gtag', () => ({ sendEvent: jest.fn() }));
jest.mock('services/resource.service', () => ({ fetchProvinceService: jest.fn() }));

jest.mock('constant/common', () => ({
  DD_MMMM_YYYY_WITH_DASH: 'DD-MMMM-YYYY',
  FIXED_LOCATION: { SHIPMENT: { latitude: 13.7563, longitude: 100.5018 } },
  REGEX_INPUT_NAME_FIELD: /^.+$/,
}));

jest.mock('react-hook-form', () => ({
  ...jest.requireActual('react-hook-form'),
  useForm: jest.fn(() => ({
    control: {},
    handleSubmit: jest.fn((fn) => (e: any) => {
      e?.preventDefault?.();
      return fn({});
    }),
    reset: jest.fn(),
    watch: jest.fn(() => ''),
    setValue: jest.fn(),
    formState: { errors: {} },
  })),
  Controller: jest.fn(({ render: renderProp }: any) => {
    return renderProp({
      field: {
        onChange: jest.fn(),
        onBlur: jest.fn(),
        value: '',
      },
    });
  }),
}));

// Mock components
jest.mock('components', () => ({
  Breadcrumbs: () => <div data-testid="breadcrumbs" />,
  DetailRow: () => <div data-testid="detail-row" />,
  Dialog: () => <div data-testid="dialog" />,
  ImageReviewModal: () => <div data-testid="image-review-modal" />,
  PlotOptionBox: () => <div data-testid="plot-option-box" />,
  TextInput: ({ value, onChange }: any) => <input data-testid="text-input" value={value ?? ''} onChange={onChange} />,
  FormTextInput: ({ placeholder, name }: any) => (
    <input data-testid="form-text-input" placeholder={placeholder} name={name} />
  ),
}));

jest.mock('components/date-picker/custom-date-picker', () => ({
  CustomDatePicker: ({ value, onChange }: any) => (
    <input data-testid="custom-date-picker" type="date" value={value ?? ''} onChange={onChange} />
  ),
}));

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider theme={theme}>
    <LocalizationProvider dateAdapter={AdapterDayjs}>{children}</LocalizationProvider>
  </ThemeProvider>
);

export const mockEventDetail = {
  meta: {
    cutter: { name: 'John Doe', id: 'cutter-1' },
    picker: { name: 'Jane Smith', id: 'picker-1' },
  },
  summary: {
    totalWeight: 120,
    totalBoxes: 15,
  },
};

export const mockEventDetailWithCutterOnly = {
  meta: {
    cutter: { name: 'John Doe', id: 'cutter-1', cuttingDay: ********** },
  },
};

export const mockEventDetailWithPickerOnly = {
  meta: {
    picker: { name: 'Jane Smith', id: 'picker-1' },
  },
};

export const mockEventDetailWithSummaryOnly = {
  summary: {
    totalWeight: 120,
    totalBoxes: 15,
  },
  meta: {
    cuttingDay: **********,
  }
};

const renderCreateEvent = (eventDetail?: any) => {
  return render(
    <TestWrapper>
      <CreateEvent eventDetail={eventDetail} />
    </TestWrapper>
  );
};

describe('CreateEvent', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Default render', () => {
    beforeEach(() => {
      renderCreateEvent();
    });

    it('renders main UI components', () => {
      expect(screen.getByText('create-harvest-log')).toBeInTheDocument();
      expect(screen.getByText('durian-harvesting-summary')).toBeInTheDocument();
      expect(screen.getByText('save-as-draft')).toBeInTheDocument();
      expect(screen.getByText('receive')).toBeInTheDocument();
      expect(screen.getByTestId('breadcrumbs')).toBeInTheDocument();
    });

    it('renders all harvest form sections', () => {
      expect(screen.getByText('cutter-information')).toBeInTheDocument();
      expect(screen.getByText('no-variety-added')).toBeInTheDocument();
      // expect(screen.getByText('durian-summary')).toBeInTheDocument();
      // expect(screen.getByText('received-by')).toBeInTheDocument();
    });
  });

  describe('Render with eventDetail', () => {
    it('renders full detail correctly', () => {
      renderCreateEvent(mockEventDetail);
      expect(screen.getByText('create-harvest-log')).toBeInTheDocument();
    });

    it('renders with cutter only', () => {
      renderCreateEvent(mockEventDetailWithCutterOnly);
      expect(screen.getByText('create-harvest-log')).toBeInTheDocument();
    });

    it('renders with picker only', () => {
      renderCreateEvent(mockEventDetailWithPickerOnly);
      expect(screen.getByText('create-harvest-log')).toBeInTheDocument();
    });

    it('renders with summary only', () => {
      renderCreateEvent(mockEventDetailWithSummaryOnly);
      expect(screen.getByText('create-harvest-log')).toBeInTheDocument();
    });
  });
});
