'use client';
import { Add, BorderColor, DeleteOutlineOutlined, RemoveRedEyeOutlined } from '@mui/icons-material';
import { Box, Button, IconButton, Typography } from '@mui/material';
import { GridRenderCellParams, GridRowParams, type GridColDef } from '@mui/x-data-grid';
import { useDeviceHeight } from 'hooks/useDeviceHeight';
import { useGetEventStatus } from 'hooks/useGetEventStatus';
import { headerHeight } from 'layouts/main/constant';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { clientRoutes } from 'routes/client-routes';
import { useToastStore } from 'store/useToastStore';
import { EventStatusEnum, PackingHouse } from 'types';
import toastMessages from 'utils/toastMessages';
import { renderBatchname, renderRecordBy, renderRecordOn, renderStatus, renderUpdatedBy, renderUpdatedOn } from './_components';
import { queryKeys } from 'hooks/queries/_key';
import { theme } from 'styles/theme';
import { formatNumberWithCommas, getStatus } from 'utils';
import { capturePosthog } from 'utils/posthog';
import { Dialog, EventDataTable } from 'components';
import { useDeleteDraftMutate } from 'hooks/mutates/useDeleteDraftMutate';

const DEFAULT_VALUE = '--';

export const Event = () => {
  const { toast: toastNotification, resetToast } = useToastStore();
  const { getStatusLabel } = useGetEventStatus();
  const router = useRouter();
  const pathName = usePathname();
  const isIncoming = useMemo(() => {
    return pathName === clientRoutes.eventIncoming;
  }, [pathName]);

  const [isOpen, setIsOpen] = useState<string | null>(null);
  const receiveTranslation = useTranslations('receive');
  const commonTranslation = useTranslations('common');
  const deviceHeight = useDeviceHeight();
  const filterT = useTranslations('filter');

  useEffect(() => {
    if (isIncoming) {
      capturePosthog('view_incoming_batch_lot');
    } else {
      capturePosthog('view_receiving_batch_lot');
    }
  }, [isIncoming, receiveTranslation]);

  const { mutateAsync } = useDeleteDraftMutate();

  const filterStatusOptions = [
    {
      label: filterT('status-incoming-waiting'),
      value: 'waiting',
    },
    {
      label: filterT('status-received'),
      value: 'received',
    },
    {
      label: filterT('status-rejected'),
      value: 'rejected',
    },
    {
      label: filterT('status-draft'),
      value: 'draft',
    },
  ];

  useEffect(() => {
    if (toastNotification) {
      toastMessages[toastNotification.type](toastNotification.message);
      resetToast();
    }
  }, [resetToast, toastNotification]);

  const handleDelete = async (id: string) => {
    setIsOpen(null);
    await mutateAsync(id);
  };

  const renderAction = useCallback(
    (params: GridRenderCellParams<PackingHouse>) => {
      const handleViewClick = (event: React.MouseEvent) => {
        event.stopPropagation();
        event.preventDefault();
        router.push(`${clientRoutes.event}/${isIncoming ? 'incoming' : 'receiving'}/${params.row.productId}`);
      };

      const handleViewEditClick = (event: React.MouseEvent) => {
        event.stopPropagation();
        event.preventDefault();
        router.push(`${clientRoutes.eventCreate}/${params.row.productId}`);
      };

      const handleDeleteDraftHarvest = (event: React.MouseEvent) => {
        event.stopPropagation();
        event.preventDefault();
        setIsOpen(params.row.productId);
      };

      const productType = params.row.type;
      const productStatus = params.row.status;
      const status = getStatus(productType, productStatus);

      return (
        <Box>
          {status !== EventStatusEnum.DRAFT && (
            <IconButton
              component={Link}
              href={`${clientRoutes.event}/${isIncoming ? 'incoming' : 'receiving'}/${params.row.productId}`}
              size="small"
              onClick={handleViewClick}
            >
              <RemoveRedEyeOutlined fontSize="small" />
            </IconButton>
          )}

          {status === EventStatusEnum.DRAFT && (
            <>
              <IconButton size="small" onClick={handleViewEditClick}>
                <BorderColor fontSize="small" />
              </IconButton>
              <IconButton size="small" color="error" onClick={handleDeleteDraftHarvest}>
                <DeleteOutlineOutlined fontSize="small" />
              </IconButton>
            </>
          )}
        </Box>
      );
    },
    [isIncoming, router]
  );

  const columns = useMemo(() => {
    const incomingColumns: GridColDef[] = [
      {
        field: 'name',
        headerName: receiveTranslation('batch-name'),
        width: 250,
        sortable: false,
        renderCell: renderBatchname,
      },
      {
        field: 'batchlot',
        headerName: receiveTranslation('batch-lot'),
        width: 200,
        sortable: false,
      },
      {
        field: 'recordBy',
        headerName: receiveTranslation('recorded-by'),
        width: 150,
        renderCell: renderRecordBy,
        sortable: false,
      },
      {
        field: 'status',
        headerName: receiveTranslation('status'),
        width: 150,
        renderCell: (params: GridRenderCellParams<PackingHouse>) => {
          const { eventStatus, eventStatusLabel } = getStatusLabel(params.row.status, params.row.type);
          return renderStatus(eventStatus, eventStatusLabel);
        },
        sortable: true,
      },
      {
        field: 'totalVarietiesWeight',
        headerName: `${receiveTranslation('total-weight')} (${commonTranslation('kg')})`,
        width: 200,
        align: 'right',
        valueFormatter: (value) => {
          return formatNumberWithCommas(value) || DEFAULT_VALUE;
        },
        sortable: true,
      },
      {
        field: 'userUpdated',
        headerName: receiveTranslation('modified-by'),
        width: 150,
        renderCell: renderUpdatedBy,
        sortable: true,
      },
      {
        field: 'dateUpdated',
        headerName: receiveTranslation('modified-on'),
        width: 168,
        renderCell: renderUpdatedOn,
        sortable: true,
      },
      {
        field: 'dateCreated',
        headerName: receiveTranslation('recorded-on'),
        width: 168,
        renderCell: renderRecordOn,
        sortable: true,
      },
      {
        field: 'action',
        headerName: receiveTranslation('actions'),
        flex: 1,
        renderCell: renderAction,
        sortable: false,
        minWidth: 120,
      },
    ];

    const receivingColumns: GridColDef[] = [
      {
        field: 'batchlot',
        headerName: commonTranslation('receiving-batch/lot'),
        width: 200,
        sortable: false,
      },
      {
        field: 'originBatchlot',
        headerName: receiveTranslation('original-batch-lot'),
        width: 230,
        sortable: false,
      },
      {
        field: 'recordBy',
        headerName: receiveTranslation('recorded-by'),
        width: 180,
        renderCell: renderRecordBy,
        sortable: false,
      },
      {
        field: 'totalVarietiesWeight',
        headerName: `${receiveTranslation('total-weight')} (${commonTranslation('kg')})`,
        width: 200,
        align: 'right',
        valueFormatter: (value) => {
          return formatNumberWithCommas(value) || DEFAULT_VALUE;
        },
        sortable: true,
      },
      {
        field: 'userUpdated',
        headerName: receiveTranslation('modified-by'),
        width: 150,
        renderCell: renderUpdatedBy,
        sortable: true,
      },
      {
        field: 'dateUpdated',
        headerName: receiveTranslation('modified-on'),
        width: 168,
        renderCell: renderUpdatedOn,
        sortable: true,
      },
      {
        field: 'dateCreated',
        headerName: receiveTranslation('receiving-date'),
        width: 168,
        renderCell: renderRecordOn,
        sortable: true,
      },
      {
        field: 'action',
        headerName: receiveTranslation('actions'),
        renderCell: renderAction,
        sortable: false,
        flex: 1,
        filterable: false,
        minWidth: 120,
      },
    ];

    if (isIncoming) {
      return incomingColumns;
    }

    return receivingColumns;
  }, [commonTranslation, getStatusLabel, isIncoming, receiveTranslation, renderAction]);

  const customTableHeight = useMemo<number>(() => {
    if (deviceHeight) {
      const paddingHeight = 210;

      return deviceHeight - headerHeight - paddingHeight;
    }

    return 0;
  }, [deviceHeight]);

  const handleOnRowClick = (params: GridRowParams) => {
    const productType = params.row.type;
    const productStatus = params.row.status;
    const status = getStatus(productType, productStatus);
    if (status === EventStatusEnum.DRAFT) {
      return router.push(`${clientRoutes.eventCreate}/${params.row.productId}`);
    }
    return router.push(`${clientRoutes.event}/${isIncoming ? 'incoming' : 'receiving'}/${params.row.productId}`);
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        width: '100%',
        backgroundColor: theme.palette.customColors.lightGray,
      }}
    >
      <Box component="article" sx={{ flexGrow: 1, p: { xs: 2, sm: 3 } }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3, flexWrap: 'wrap' }}>
          <Typography variant="h5" component="h1" fontWeight="bold" sx={{ mb: { xs: 2, sm: 0 } }}>
            {isIncoming ? receiveTranslation('incomingBatchLot') : receiveTranslation('receivingBatchLot')}
          </Typography>
          {isIncoming && (
            <Button
              variant="contained"
              onClick={() => {
                router.push(clientRoutes.eventCreate);
              }}
              sx={{
                minWidth: '140px',
                px: 3,
                py: 1,
                fontSize: '0.875rem',
                fontWeight: 500,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: 1,
              }}
            >
              <Add />
              <Typography>{receiveTranslation('create-harvest-log')}</Typography>
            </Button>
          )}
        </Box>

        <EventDataTable
          defaultSortModel={[{ field: 'dateCreated', sort: 'desc' }]}
          hasFilterUserRole={true}
          filterStatusOptions={isIncoming ? filterStatusOptions : undefined}
          tableHeight={customTableHeight}
          onRowClick={handleOnRowClick}
          columns={columns}
          eventType={isIncoming ? 'harvesting' : 'receiving'}
          queryKey={isIncoming ? queryKeys.HARVEST : queryKeys.RECEIVING}
        />
      </Box>
      <Dialog
        isOpen={isOpen !== null}
        title={receiveTranslation('delete-harvest-title')}
        content={receiveTranslation('delete-harvest-content')}
        okButtonText={commonTranslation('delete-modal-btn')}
        onConfirm={() => {
          if (isOpen !== null) {
            handleDelete(isOpen);
          }
        }}
        onCancel={() => setIsOpen(null)}
        type="danger"
      />
    </Box>
  );
};
