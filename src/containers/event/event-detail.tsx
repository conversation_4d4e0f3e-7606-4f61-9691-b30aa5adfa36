'use client';
import { ArrowBackOutlined, InfoOutlined } from '@mui/icons-material';
import { Avatar, Box, Button, IconButton, Stack, Typography } from '@mui/material';
import { Breadcrumbs, DetailRow, ImageReviewModal, PlotOptionBox } from 'components';
import { DurianVarietyBox } from 'components/durian-variety';
import { useReceiveHarvestMutate } from 'hooks/mutates/useReceiveHarvestMutate';
import { useUpdateReceiveMutate } from 'hooks/mutates/useUpdateReceiveMutate';
import { useGetEventStatus } from 'hooks/useGetEventStatus';
import { flatMap, get } from 'lodash-es';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { clientRoutes } from 'routes/client-routes';
import { useUserStore } from 'store/useUserStore';
import { theme } from 'styles/theme';
import { DurianVariety, PackingHouseDetail, Province, ReceiveUpdatePayload } from 'types';
import { formatNumberWithCommas, getImageUrl } from 'utils';
import {
  compareVarieties,
  convertModalDataToVarieties,
  convertResponseToWeights,
  reorderToOriginalOrder,
  transformArray,
  transformDurianDataEvent,
} from 'utils/event';
import {
  OriginalDrawer,
  RejectDrawer,
  formatDateWithLocale,
  renderStatus,
  TotalWeightInfoDrawer,
  renderRecordByInDetail,
} from './_components';
import { ReceiveHarvestModalCompat } from './_components/harvest-variety-modal';
import { DD_MMMM_YYYY_WITH_DASH } from 'constant/common';
import { getCookieLocale } from 'utils/cookie-client';
import { fetchProvinceService } from 'services/resource.service';
import { useQuery } from '@tanstack/react-query';
import { sendEvent } from 'utils/gtag';
import { AdjustWeightInformation } from './_components/adjust-weight-information';

type EventDetailProps = {
  eventDetail: PackingHouseDetail;
  isReceiving?: boolean;
};

export const EventDetail: FC<EventDetailProps> = ({ eventDetail: data, isReceiving = false }) => {
  const { mutateAsync } = useReceiveHarvestMutate();
  const { mutateAsync: updateReceiveAsync } = useUpdateReceiveMutate();
  const receiveTranslation = useTranslations('receive');
  const commonTranslation = useTranslations('common');
  const [isDrawerOpen, setIsDrawerOpen] = useState<boolean>(false);
  const [isTotalWeightDrawerOpen, setIsTotalWeightDrawerOpen] = useState<boolean>(false);
  const [isOpenReceive, setIsOpenReceive] = useState<boolean>(false);
  const [isOriginDrawerOpen, setIsOriginDrawerOpen] = useState<boolean>(false);
  const [selectedVarietyForDrawer, setSelectedVarietyForDrawer] = useState<string | null>(null);
  const [currentVarieties, setCurrentVarieties] = useState<DurianVariety[] | null>();
  const [changedVarietyIds, setChangedVarietyIds] = useState<string[]>([]);
  const [clientName, setClientName] = useState<string | undefined>();

  const { data: provinces } = useQuery({
    queryKey: ['provinces'],
    queryFn: () => {
      return fetchProvinceService();
    },
  });

  const router = useRouter();
  const searchParams = useSearchParams();

  const isView = searchParams?.get('type') === 'view';

  const { getStatusLabel } = useGetEventStatus();

  const user = useUserStore((state) => state.user);

  const locale = getCookieLocale() ?? 'th';

  const originalVarieties = useMemo(() => {
    const varietyData = data?.varieties ?? [];
    return varietyData;
  }, [data?.varieties]);

  useEffect(() => {
    if (data?.varieties) {
      const otherVariety = data.varieties.find((v) => v.value === 'other');
      if (otherVariety?.name) {
        setClientName(otherVariety.name);
      }
      setCurrentVarieties(data.varieties);
    }
  }, [data?.varieties]);

  const { eventStatus, eventStatusLabel } = getStatusLabel(data.status, data.type);

  const handleCloseReceive = () => {
    setIsOpenReceive(false);
  };

  const handleConfirm = async (varietyData: DurianVariety[]) => {
    if (!data) {
      handleCloseReceive();
      return;
    }
    if (!Array.isArray(varietyData) || varietyData.length <= 0) return;
    const updateVariety = transformArray(varietyData);

    const reOrderVariety = reorderToOriginalOrder(data.varieties, updateVariety) as DurianVariety[];

    if (changedVarietyIds.length > 0) {
      sendEvent('receive_incoming_harvest_with_changes');
    } else {
      sendEvent('receive_incoming_harvest');
    }

    await mutateAsync({
      harvestProductId: data.id,
      latitude: data.positionLatitude,
      longitude: data.positionLongitude,
      update: {
        varieties: reOrderVariety,
      },
    });
  };

  const isWaitingStatus = eventStatus === 'waiting';
  const isRejectedStatus = eventStatus === 'rejected';

  const totalWeight = useMemo(() => {
    if (!originalVarieties?.length) return 0;

    const grades = flatMap(originalVarieties, (it) => it.grades);

    const total = grades.reduce((pre, current) => {
      const sumWeight = pre + (current?.weight ?? 0);

      return sumWeight;
    }, 0);

    return total;
  }, [originalVarieties]);

  const editableVarietyIds = useMemo(() => {
    const ids = new Set<string>();
    originalVarieties.forEach((v) => {
      if (v.id) {
        ids.add(v.id);
      }
    });
    return ids;
  }, [originalVarieties]);

  const varietyBloomDays = useMemo(() => {
    const bloomDays: Record<string, number> = {};
    originalVarieties?.forEach((v) => {
      if (v.id && v.flowerBloomingDay) {
        bloomDays[`${v.id}`] = v.flowerBloomingDay;
      }
    });
    return bloomDays;
  }, [originalVarieties]);

  const transformedVarieties = useMemo(() => {
    if (Array.isArray(currentVarieties) && currentVarieties.length > 0) {
      return transformDurianDataEvent(currentVarieties);
    }
    return [];
  }, [currentVarieties]);

  const showOriginalData = useCallback((varietyId: string) => {
    setSelectedVarietyForDrawer(varietyId);
    setIsOriginDrawerOpen(true);
  }, []);

  const modalInitialWeights = useMemo(() => {
    if (Array.isArray(currentVarieties) && currentVarieties.length > 0) {
      return convertResponseToWeights(currentVarieties);
    }
  }, [currentVarieties]);

  const modalInitialClientName = useMemo(() => {
    return clientName;
  }, [clientName]);

  const handleSave = async (_data: ReceiveUpdatePayload) => {
    const updatedVarieties = convertModalDataToVarieties(_data, originalVarieties);
    const comparison = compareVarieties(originalVarieties, updatedVarieties);
    sendEvent('enter_variety_grade_weight');

    if (isReceiving && !!_data) {
      const updateVariety = transformArray(updatedVarieties);
      await updateReceiveAsync({
        productId: data.id,
        update: {
          varieties: reorderToOriginalOrder(data.varieties, updateVariety) as DurianVariety[],
        },
      });
      handleCloseReceive();
      return;
    }
    setChangedVarietyIds(comparison.changedVarietyIds);
    await handleConfirm(reorderToOriginalOrder(data.varieties, updatedVarieties) as DurianVariety[]);
    handleCloseReceive();
  };

  const breadcrumbItems = useMemo(() => {
    return [
      {
        label: isReceiving ? receiveTranslation('receivingBatchLot') : receiveTranslation('incomingBatchLot'),
        href: isReceiving ? clientRoutes.eventReceiving : clientRoutes.eventIncoming,
        showConfirmation: Boolean(changedVarietyIds.length && isWaitingStatus),
      },
      {
        label: isReceiving ? data.batchlot : data.name,
        href: clientRoutes.event,
      },
    ];
  }, [isReceiving, receiveTranslation, changedVarietyIds.length, isWaitingStatus, data.batchlot, data.name]);

  const handleBack = () => {
    router.back();
  };

  const cuttingDay =
    typeof data?.meta.cuttingDay === 'number' ? (data?.meta.cuttingDay ?? 0) * 1000 : data?.meta.cuttingDay;

  const eventProvince = useMemo(() => {
    if (!provinces?.data || !data?.meta?.cutterVehicle?.provinceRegistrationNumber) {
      return '--';
    }
    return (
      provinces?.data.find(
        (item: Province) => item.provinceVehicleCode === data?.meta.cutterVehicle?.provinceRegistrationNumber
      )?.label?.[locale] ?? '--'
    );
  }, [provinces, data, locale]);

  return (
    <>
      <Box sx={{ padding: '20px', backgroundColor: theme.palette.customColors.lightGray }}>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Breadcrumbs items={breadcrumbItems} />
            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
              <IconButton onClick={handleBack}>
                <ArrowBackOutlined fontSize="small" />
              </IconButton>
              {isReceiving ? (
                <Typography variant="h3">{data?.batchlot}</Typography>
              ) : (
                <Typography variant="h3">{data?.name}</Typography>
              )}
              {isReceiving ? (
                <Typography sx={{ fontSize: 14, color: theme.palette.customColors.gray }}>
                  {`${receiveTranslation('received-on')} ${formatDateWithLocale(data?.dateCreated || '')}`}
                </Typography>
              ) : (
                renderStatus(eventStatus, eventStatusLabel)
              )}
            </Box>
          </Box>
          {isWaitingStatus && (
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button variant="outlined" sx={{ height: '40px', width: '200px' }} onClick={() => setIsDrawerOpen(true)}>
                {receiveTranslation('reject-this-batch')}
              </Button>
              <Button
                variant="contained"
                sx={{ height: '40px', width: '200px' }}
                onClick={() => setIsOpenReceive(true)}
              >
                {receiveTranslation('receive-this-batch')}
              </Button>
            </Box>
          )}
          {!isView && isReceiving && !data.immutable && (
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="contained"
                sx={{ height: '40px', width: '200px' }}
                onClick={() => setIsOpenReceive(true)}
              >
                {commonTranslation('edit')}
              </Button>
            </Box>
          )}
        </Box>
        {/* Detail */}

        <Stack flexDirection="row" gap={2} sx={{ marginTop: 2 }}>
          <Stack component="div" flexDirection="column" id="column-left" sx={{ gap: 2, flex: 1 }}>
            {!isReceiving && isRejectedStatus && (
              <Box
                id="reject-reason"
                sx={{
                  borderRadius: '8px',
                  border: '1px solid #0000001A',
                  background: 'white',
                  height: 'auto',
                  padding: 2,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 2,
                  maxWidth: '100%',
                  textWrap: 'wrap',
                  wordBreak: 'break-word',
                }}
                component={'div'}
              >
                <Typography sx={{ fontWeight: 600 }} variant="body1">
                  {receiveTranslation('reject-drawer-reason')}
                </Typography>
                <Box
                  sx={{
                    width: '100%',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    borderRadius: '12px',
                    flexDirection: 'row-reverse',
                    gap: 4,
                    padding: '20px',
                    bgcolor: theme.palette.customColors.primary50,
                  }}
                >
                  <DetailRow title={''} content={data?.rejectReason} noBorder noPadding />
                </Box>
              </Box>
            )}

            {isReceiving && data.meta.diffVarieties && (
              <Box
                id="diff-varieties"
                sx={{
                  borderRadius: '8px',
                  border: '1px solid #0000001A',
                  background: 'white',
                  height: 'auto',
                  padding: 2,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 2,
                  maxWidth: '100%',
                  textWrap: 'wrap',
                  wordBreak: 'break-word',
                }}
                component={'div'}
              >
                <Typography sx={{ fontWeight: 600 }} variant="body1">
                  {receiveTranslation('weight-adjusted-by-packing-house')}
                </Typography>
                <Box
                  sx={{
                    width: '100%',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    borderRadius: '12px',
                    flexDirection: 'row-reverse',
                    gap: 4,
                    padding: '20px',
                    bgcolor: theme.palette.customColors.primary50,
                  }}
                >
                  <AdjustWeightInformation data={data} />
                </Box>
              </Box>
            )}

            <Box
              id="durian-harvesting-summary"
              sx={{
                borderRadius: '8px',
                border: '1px solid #0000001A',
                background: 'white',
                height: 'auto',
                padding: 2,
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
              }}
              component={'div'}
            >
              <Typography sx={{ fontWeight: 600 }} variant="body1">
                {receiveTranslation('durian-harvesting-summary')}
              </Typography>
              <Box
                sx={{
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 1,
                  height: 'auto',
                  overflowY: 'auto',
                }}
              >
                <DetailRow
                  title={receiveTranslation('batch-lot')}
                  content={
                    isReceiving ? (
                      <Link href={`${clientRoutes.eventIncoming}/${data?.originBatchlotId}`} target="_blank">
                        {data?.originBatchlot}
                      </Link>
                    ) : (
                      data?.batchlot
                    )
                  }
                  noPadding
                  noBorder
                />
                <DetailRow
                  title={receiveTranslation('durian-cutting-day')}
                  content={formatDateWithLocale(cuttingDay ?? 0, DD_MMMM_YYYY_WITH_DASH)}
                  noBorder
                  noPadding
                />
                <DetailRow
                  title={receiveTranslation('total-weight')}
                  noPadding
                  content={
                    <Box sx={{ display: 'flex', gap: 3, alignItems: 'center' }}>
                      <Typography variant="body1">
                        {formatNumberWithCommas(totalWeight)} {commonTranslation('kg')}
                      </Typography>
                      <IconButton
                        size="small"
                        sx={{ color: theme.palette.info.main }}
                        onClick={() => setIsTotalWeightDrawerOpen(true)}
                      >
                        <InfoOutlined />
                      </IconButton>
                    </Box>
                  }
                  noBorder
                />
                <DetailRow
                  title={receiveTranslation('packing-house')}
                  content={data?.packingHouse.name}
                  noBorder
                  noPadding
                />
                <DetailRow
                  title={receiveTranslation('recorded-by')}
                  content={renderRecordByInDetail(isReceiving, data)}
                  noBorder
                  noPadding
                  isCapitalize
                />
                <Box sx={{ display: 'flex', gap: 2 }}>
                  {data?.images.map((image) => (
                    <ImageReviewModal key={image.id} imageUrl={getImageUrl(image.filenameDisk)} />
                  ))}
                </Box>
              </Box>
            </Box>
            <Box
              sx={{
                borderRadius: '8px',
                border: '1px solid #0000001A',
                background: 'white',
                height: 'auto',
                p: 2,
                width: '100%',
              }}
            >
              <Typography sx={{ fontWeight: 600 }} variant="body1">
                {receiveTranslation('cutter-information')}
              </Typography>
              <Box
                sx={{
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 1,
                  overflowY: 'auto',
                  height: 'auto',
                }}
              >
                <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                  <Avatar
                    src={getImageUrl(data?.meta.cutter?.avatar?.filenameDisk) ?? ''}
                    sx={{ height: 40, width: 40 }}
                  />
                  <DetailRow
                    title={receiveTranslation('cutter-name')}
                    content={
                      <Box display="flex" gap={1} alignItems="center">
                        <span>{data?.meta.cutter?.name}</span>
                      </Box>
                    }
                    noBorder
                  />
                </Box>
                <DetailRow
                  title={receiveTranslation('type')}
                  content={
                    data.meta?.cutter?.isCertified
                      ? receiveTranslation('registered-cutter')
                      : receiveTranslation('unregistered-cutter')
                  }
                  noBorder
                  noPadding
                />

                <DetailRow
                  title={receiveTranslation('vehicle-registration-number')}
                  content={data?.meta.cutterVehicle?.vehicleRegistrationNumber ?? ''}
                  noBorder
                  noPadding
                />
                <DetailRow title={receiveTranslation('province-vehicle')} content={eventProvince} noBorder noPadding />
                {get(data, 'meta.cutterVehicle.image.filenameDisk') && (
                  <ImageReviewModal imageUrl={getImageUrl(data?.meta.cutterVehicle?.image?.filenameDisk) ?? ''} />
                )}
              </Box>
            </Box>
          </Stack>

          <Stack component="div" flexDirection="column" id="column-right" sx={{ gap: 2, flex: 1 }}>
            <Box
              id="durian-variety-grade-weight"
              sx={{
                borderRadius: '8px',
                border: '1px solid #0000001A',
                background: 'white',
                height: 'auto',
                gap: 1,
                p: 2,
              }}
              component={'div'}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', pb: 2 }}>
                <Typography variant="body1" sx={{ fontWeight: 600 }}>
                  {receiveTranslation('durian-variety-grade-weight')}
                </Typography>
              </Box>
              <Box
                sx={{
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 1,
                  height: 'auto',
                  overflowY: 'auto',
                }}
              >
                {transformedVarieties.map((variety) => (
                  <DurianVarietyBox
                    key={variety.id}
                    isUpdated={changedVarietyIds.includes(variety.id)}
                    variety={variety}
                    sx={{ textAlign: 'left' }}
                    showGrades={true}
                    updatedBy={`${user?.firstName} ${user?.lastName}`}
                    onToggleView={() => showOriginalData(variety.id)}
                  />
                ))}
              </Box>
            </Box>

            <Box
              sx={{
                width: '100%',
                borderRadius: '8px',
                border: '1px solid #0000001A',
                background: 'white',
                height: 'auto',
                p: 2,
              }}
            >
              <Typography sx={{ fontWeight: 600 }} variant="body1">
                {receiveTranslation('farm-information')}
              </Typography>
              <Box
                sx={{
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 1,
                  overflowY: 'auto',
                  height: 'auto',
                }}
              >
                <DetailRow noPadding title={receiveTranslation('farm-name')} content={data?.farm.name} noBorder />
                <DetailRow noPadding title={receiveTranslation('farm-address')} content={data?.farm.address} noBorder />
                {data?.meta.farm?.plots.map((plot) => (
                  <PlotOptionBox
                    key={plot.id}
                    plotNumber={plot?.name ?? ''}
                    plotId={plot?.plotId ?? ''}
                    plotGap={plot?.gap ?? ''}
                    plotArea={plot?.area ?? 0}
                    plotAreaUnit={plot?.areaUnit?.[locale] ?? ''}
                    renderActionButtons={() => (
                      <Box sx={{ display: 'flex', gap: '8px' }}>
                        <ImageReviewModal imageUrl={getImageUrl(plot.image?.filenameDisk) ?? null} />
                      </Box>
                    )}
                  />
                ))}
              </Box>
            </Box>
          </Stack>
        </Stack>
      </Box>
      <ReceiveHarvestModalCompat
        isEditReceiving={isReceiving}
        open={isOpenReceive}
        onClose={handleCloseReceive}
        onSave={handleSave}
        initialWeights={modalInitialWeights}
        editableVarietyIds={editableVarietyIds}
        varietyBloomDays={varietyBloomDays}
        initialCustomName={modalInitialClientName}
      />
      <RejectDrawer data={data} open={isDrawerOpen} toggle={setIsDrawerOpen} />
      {data && (
        <TotalWeightInfoDrawer
          content={originalVarieties}
          total={totalWeight}
          open={isTotalWeightDrawerOpen}
          toggle={setIsTotalWeightDrawerOpen}
        />
      )}
      <OriginalDrawer
        open={isOriginDrawerOpen}
        onClose={() => setIsOriginDrawerOpen(false)}
        originalVariety={transformDurianDataEvent(originalVarieties).find((i) => i.id === selectedVarietyForDrawer)}
        currentVariety={
          !(!Array.isArray(currentVarieties) || currentVarieties.length <= 0)
            ? transformDurianDataEvent(currentVarieties).find((i) => i.id === selectedVarietyForDrawer) || undefined
            : undefined
        }
      />
    </>
  );
};
