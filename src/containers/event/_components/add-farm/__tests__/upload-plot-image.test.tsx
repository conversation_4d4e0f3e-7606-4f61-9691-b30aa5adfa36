import { render, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import UploadPlotImage from '../upload-plot-image';
import { uploadFileService } from 'services/internal.service';
import toastMessages from 'utils/toastMessages';

jest.mock('next-intl', () => ({
  useTranslations: () => (key: string) => key,
}));

jest.mock('services/internal.service', () => ({
  uploadFileService: jest.fn(),
}));

jest.mock('utils/gtag', () => ({
  sendEvent: jest.fn(),
}));

jest.mock('utils/toastMessages', () => ({
  __esModule: true,
  default: {
    error: jest.fn(),
  },
}));

// Mock file validation utility
jest.mock('utils', () => ({
  validateFileImage: jest.fn(),
}));

// Mock UUID
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'mock-uuid-123'),
}));

import { validateFileImage } from 'utils';
import { sendEvent } from 'utils/gtag';

const mockValidateFileImage = validateFileImage as jest.MockedFunction<typeof validateFileImage>;
const mockSendEvent = sendEvent as jest.MockedFunction<typeof sendEvent>;

describe('UploadPlotImage', () => {
  const mockOnChange = jest.fn();
  const validFile = new File(['dummy'], 'test.jpg', { type: 'image/jpeg' });
  const invalidFile = new File(['dummy'], 'test.txt', { type: 'text/plain' });
  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(() => {
    jest.clearAllMocks();
    user = userEvent.setup();

    // Setup default mock returns
    mockValidateFileImage.mockReturnValue({
      isValid: true,
      wrongFileType: false,
      wrongFileSize: false
    });

    (uploadFileService as jest.Mock).mockResolvedValue({
      data: { filenameDisk: 'test-upload.jpg', id: 'file-123' },
    });
  });

  const renderComponent = () => {
    const result = render(<UploadPlotImage onChange={mockOnChange} />);
    const fileInput = result.container.querySelector('input[type="file"]') as HTMLInputElement;
    return { ...result, fileInput };
  };

  it('renders upload button and file input', () => {
    const { container } = renderComponent();

    expect(container.querySelector('input[type="file"]')).toBeInTheDocument();
    expect(container.querySelector('label')).toBeInTheDocument();
  });

  it('uploads valid file successfully', async () => {
    const { fileInput } = renderComponent();

    await user.upload(fileInput, validFile);

    await waitFor(() => {
      expect(mockValidateFileImage).toHaveBeenCalledWith(validFile);
      expect(uploadFileService).toHaveBeenCalledWith(expect.any(FormData));
      expect(mockSendEvent).toHaveBeenCalledWith('uploaded_photos');
      expect(mockOnChange).toHaveBeenCalledWith('test-upload.jpg');
    });
  });

  it('shows error for invalid file type', async () => {
    mockValidateFileImage.mockReturnValue({
      isValid: false,
      wrongFileType: true,
      wrongFileSize: false
    });

    const { fileInput } = renderComponent();

    fireEvent.change(fileInput, { target: { files: [invalidFile] } });

    await waitFor(() => {
      expect(mockValidateFileImage).toHaveBeenCalledWith(invalidFile);
      expect(toastMessages.error).toHaveBeenCalledWith('invalid-image');
      expect(uploadFileService).not.toHaveBeenCalled();
      expect(mockOnChange).not.toHaveBeenCalled();
    });
  });

  it('handles upload failure', async () => {
    (uploadFileService as jest.Mock).mockRejectedValue(new Error('Upload failed'));

    const { fileInput } = renderComponent();

    await user.upload(fileInput, validFile);

    await waitFor(() => {
      expect(mockValidateFileImage).toHaveBeenCalledWith(validFile);
      expect(uploadFileService).toHaveBeenCalled();
      expect(toastMessages.error).toHaveBeenCalledWith('common-error');
      expect(mockOnChange).not.toHaveBeenCalled();
    });
  });

  it('handles empty file response', async () => {
    (uploadFileService as jest.Mock).mockResolvedValue({
      data: { filenameDisk: '', id: 'file-123' },
    });

    const { fileInput } = renderComponent();

    await user.upload(fileInput, validFile);

    await waitFor(() => {
      expect(uploadFileService).toHaveBeenCalled();
      expect(mockOnChange).not.toHaveBeenCalled();
    });
  });

  it('clears input value after processing', async () => {
    const { fileInput } = renderComponent();

    await user.upload(fileInput, validFile);

    await waitFor(() => {
      expect(fileInput.value).toBe('');
    });
  });

  it('handles empty file selection', () => {
    const { fileInput } = renderComponent();

    fireEvent.change(fileInput, { target: { files: [] } });

    expect(mockValidateFileImage).not.toHaveBeenCalled();
    expect(uploadFileService).not.toHaveBeenCalled();
  });
});
