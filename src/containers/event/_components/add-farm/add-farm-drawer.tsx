import { Box, Button, IconButton, InputAdornment, LinearProgress, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';
import { FC, useMemo, useState } from 'react';
import SearchIcon from '@mui/icons-material/Search';
import { DebounceSearchInput, Drawer } from 'components';
import { theme } from 'styles/theme';
import { Farm, Plot } from 'types';
import { FarmDetail } from './farm-detail';
import { useQuery } from '@tanstack/react-query';
import { fetchFarmService } from 'services/resource.service';
import { AddPlot } from './add-plot';
import { ArrowBackOutlined } from '@mui/icons-material';
import emptySearchIcon from 'assets/icons/empty-search.svg';
import Image from 'next/image';

interface AddFarmDrawerProps {
  open: boolean;
  toggle: () => void;
  onAddFarm: (farm: Farm, plot: Plot[]) => void;
}

const initialFarm: Farm = {
  id: '',
  name: '',
  address: '',
  gln: '',
  gap: '',
};

export const AddFarmDrawer: FC<AddFarmDrawerProps> = ({ open, toggle, onAddFarm }) => {
  const [searchKeyword, setSearchKeyword] = useState('');
  const commonT = useTranslations('common');
  const receivingTranslation = useTranslations('receive');

  const {
    data: farmResponse,
    isLoading: isLoadingFarm,
    isFetching: isFetchingFarm,
  } = useQuery({
    queryKey: ['farms', searchKeyword],
    queryFn: () => {
      return fetchFarmService(searchKeyword);
    },
  });

  const [selectedFarm, setSelectedFarm] = useState<Farm>(initialFarm);
  const [selectedPlots, setSelectedPlots] = useState<Plot[]>([]);
  const [tempSelectedPlots, setTempSelectedPlots] = useState<Plot[]>([]);

  const [mode, setMode] = useState<'list' | 'detail' | 'plot'>('list');

  const drawerTitle = `${receivingTranslation('add-farm-information')}`;

  const handleFarmSelect = (farm: Farm) => {
    setSelectedFarm(farm);
    setMode('detail');
  };

  const reset = () => {
    setMode('list');
    setSelectedFarm(initialFarm);
    setSelectedPlots([]);
    setTempSelectedPlots([]);
    setSearchKeyword('');
  };

  const handleClose = () => {
    reset();
    toggle();
  };

  const handleAddImageToPlot = (id: string, img: string) => {
    const newSelectedPlots = selectedPlots.map((plot) => {
      if (plot.id === id) {
        plot.image = img;
      }
      return plot;
    });
    setSelectedPlots(newSelectedPlots);
    setTempSelectedPlots(newSelectedPlots);
  };

  const handleRemoveImageFromPlot = (id: string) => {
    const newSelectedPlots = selectedPlots.map((plot) => {
      if (plot.id === id) {
        plot.image = '';
      }
      return plot;
    });
    setSelectedPlots(newSelectedPlots);
    setTempSelectedPlots(newSelectedPlots);
  };

  const handleRemovePlot = (id: string) => {
    const newSelectedPlots = selectedPlots.filter((plot) => {
      return plot.id !== id;
    });
    setSelectedPlots(newSelectedPlots);
    setTempSelectedPlots(newSelectedPlots);
  };

  const enabledSaveButton = useMemo(() => {
    return Boolean(selectedPlots.length > 0 && selectedFarm.name && selectedFarm.address);
  }, [selectedPlots, selectedFarm]);

  const handleAddFarm = () => {
    if (enabledSaveButton) {
      onAddFarm(selectedFarm, selectedPlots);
      handleClose();
    }
  };

  const detailFooterElement = (
    <Box
      sx={{
        px: 2,
        py: 1.5,
        borderTop: 1,
        borderColor: 'divider',
        display: 'flex',
        justifyContent: 'space-between',
        gap: 1,
      }}
    >
      <Button fullWidth variant="outlined" onClick={reset}>
        {commonT('cancel-modal-btn')}
      </Button>
      <Button fullWidth variant="contained" disabled={!enabledSaveButton} onClick={handleAddFarm}>
        {commonT('add-modal-btn')}
      </Button>
    </Box>
  );

  const plotFooterElement = (
    <Box
      sx={{
        px: 2,
        py: 1.5,
        borderTop: 1,
        borderColor: 'divider',
        display: 'flex',
        justifyContent: 'space-between',
        gap: 1,
      }}
    >
      <Button
        fullWidth
        variant="outlined"
        onClick={() => {
          setTempSelectedPlots(selectedPlots);
          setMode('detail');
        }}
      >
        {commonT('cancel-modal-btn')}
      </Button>
      <Button
        fullWidth
        variant="contained"
        onClick={() => {
          const newSelectedPlot = [
            ...selectedPlots.filter((itemA) => tempSelectedPlots.some((itemB) => itemB.id === itemA.id)),
            ...tempSelectedPlots.filter((itemB) => !selectedPlots.some((itemA) => itemA.id === itemB.id)),
          ];
          setSelectedPlots(newSelectedPlot);
          setTempSelectedPlots(newSelectedPlot);
          setMode('detail');
        }}
      >
        {commonT('confirm-modal-btn')}
      </Button>
    </Box>
  );

  const renderList = () => {
    if (farmResponse && farmResponse.data.length === 0) {
      if (searchKeyword.trim() === '') return <></>;
      return (
        <Box
          sx={{
            width: '100%',
            display: 'flex',
            alignItems: 'center',
            flexDirection: 'column',
          }}
        >
          <Image src={emptySearchIcon} width={160} height={160} unoptimized alt="empty-state-icon" />
          <Typography my="12px" fontSize="16px" color="text.secondary">
            {receivingTranslation('no-result-found')}
          </Typography>
        </Box>
      );
    }
    return farmResponse?.data.map((item: Farm) => {
      return (
        <Box
          key={item.id}
          sx={{
            display: 'flex',
            alignItems: 'start',
            paddingX: 2,
            gap: 0.5,
            flexDirection: 'column',
            borderRadius: 2,
            ':hover': {
              background: theme.palette.customColors.primary50,
            },
          }}
          onClick={() => {
            handleFarmSelect(item);
          }}
        >
          <Typography>{item.name}</Typography>
          <Typography sx={{ fontWeight: 300, color: theme.palette.customColors.gray, fontSize: 14 }}>
            {item.address}
          </Typography>
        </Box>
      );
    });
  };

  const renderTitleSearch = useMemo(() => {
    if (searchKeyword) return receivingTranslation('search-result');
    if (farmResponse && farmResponse.data.length === 0) return '';
    return receivingTranslation('suggested-farm');
  }, [searchKeyword, farmResponse, receivingTranslation]);

  const listContent = (
    <>
      <DebounceSearchInput
        placeholder={receivingTranslation('search-farm')}
        value={searchKeyword}
        onChange={setSearchKeyword}
        startAdornment={
          <InputAdornment position="start">
            <SearchIcon />
          </InputAdornment>
        }
        sx={{ height: '40px', width: '100%' }}
      />
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
        <Typography>{renderTitleSearch}</Typography>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          {isLoadingFarm && isFetchingFarm ? <LinearProgress /> : renderList()}
        </Box>
      </Box>
    </>
  );

  const renderFooter = () => {
    if (mode === 'list') return <></>;
    if (mode === 'detail') return detailFooterElement;
    return plotFooterElement;
  };

  const renderContent = () => {
    if (mode === 'list') return listContent;
    if (mode === 'detail') {
      return (
        <FarmDetail
          farm={selectedFarm}
          plots={selectedPlots}
          addPlot={() => {
            setMode('plot');
          }}
          addImageToPlot={handleAddImageToPlot}
          removePlot={handleRemovePlot}
          removeImageFromPlot={handleRemoveImageFromPlot}
        />
      );
    }
    return (
      <Box>
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          <IconButton
            onClick={() => {
              setMode('detail');
              setTempSelectedPlots(selectedPlots);
            }}
          >
            <ArrowBackOutlined fontSize="small" />
          </IconButton>
          <Typography variant="h3">{receivingTranslation('select-plot')}</Typography>
        </Box>
        <AddPlot
          farm={selectedFarm}
          selectedPlots={tempSelectedPlots}
          setSelectedPlots={(plots: Plot[]) => setTempSelectedPlots(plots)}
        />
      </Box>
    );
  };

  return (
    <Drawer
      anchor="right"
      drawerTitle={drawerTitle}
      open={open}
      onClose={() => handleClose()}
      hasActionBtn={false}
      footerElement={renderFooter()}
    >
      <Box
        component="div"
        sx={{ width: '100%', p: '24px 16px', display: 'flex', flexDirection: 'column', gap: '16px' }}
      >
        {renderContent()}
      </Box>
    </Drawer>
  );
};
