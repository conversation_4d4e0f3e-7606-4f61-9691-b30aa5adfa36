import { Box, Button, LinearProgress, Typography } from '@mui/material';
import { Farm, Plot } from 'types';
import { FC } from 'react';
import { theme } from 'styles/theme';
import { fetchFarmPlotService } from 'services/resource.service';
import { useQuery } from '@tanstack/react-query';
import { activeButtonSelectStyle, normalButtonSelectStyle } from 'components/filter-table/filter-table.styles';
import { formatGap, formatNumberWithCommas, formatPlotId } from 'utils';
import { useTranslations } from 'next-intl';
import emptySearchIcon from 'assets/icons/empty-search.svg';
import Image from 'next/image';

interface AddPlotProps {
  farm: Farm;
  selectedPlots: Plot[];
  setSelectedPlots: (plots: Plot[]) => void;
}

export const AddPlot: FC<AddPlotProps> = ({ farm, selectedPlots, setSelectedPlots }) => {
  const receiveTranslation = useTranslations('receive');
  const commonTranslation = useTranslations('common');

  const {
    data: farmPlotResponse,
    isLoading: isLoadingFarmPlot,
    isFetching: isFetchingFarmPlot,
  } = useQuery({
    queryKey: ['farm', farm.id],
    queryFn: () => {
      return fetchFarmPlotService(farm.id);
    },
  });

  const togglePlot = (plot: Plot) => {
    const checkExist = selectedPlots.some((item) => item.id === plot.id);
    if (checkExist) {
      const newSelectedPlots = selectedPlots.filter((item) => item.id !== plot.id);
      setSelectedPlots(newSelectedPlots);
      return;
    }
    setSelectedPlots([...selectedPlots, plot]);
  };

  const renderList = () => {
    if (farmPlotResponse && farmPlotResponse.data.length === 0) {
      return (
        <Box
          sx={{
            width: '100%',
            display: 'flex',
            alignItems: 'center',
            flexDirection: 'column',
          }}
        >
          <Image src={emptySearchIcon} width={160} height={160} unoptimized alt="empty-state-icon" />
          <Typography my="12px" fontSize="16px" color="text.secondary">
            {receiveTranslation('no-plot')}
          </Typography>
        </Box>
      );
    }
    return farmPlotResponse?.data.map((item: Plot) => {
      return (
        <Button
          key={item.id}
          sx={
            selectedPlots.some((selected) => selected.id === item.id)
              ? { ...activeButtonSelectStyle, justifyContent: 'start' }
              : { ...normalButtonSelectStyle, justifyContent: 'start' }
          }
          onClick={() => togglePlot(item)}
        >
          <Box
            key={item.id}
            sx={{
              display: 'flex',
              alignItems: 'start',
              padding: 2,
              gap: 0.5,
              flexDirection: 'column',
              borderRadius: 2,
            }}
          >
            <Typography>{item.name}</Typography>
            <Typography sx={{ fontWeight: 300, color: theme.palette.customColors.gray, fontSize: 14 }}>
              {commonTranslation('id')} - {item.plotId ? formatPlotId(item.plotId) : '--'}
            </Typography>
            <Typography sx={{ fontWeight: 300, color: theme.palette.customColors.gray, fontSize: 14 }}>
              {commonTranslation('area')} - {formatNumberWithCommas(item.area) ?? '--'} {item.areaUnit}
            </Typography>
            <Typography sx={{ fontWeight: 300, color: theme.palette.customColors.gray, fontSize: 14 }}>
              {commonTranslation('gap')} -{' '}
              {item.gap ? `${commonTranslation('prefix-plot')} ${formatGap(item.gap)}` : '--'}
            </Typography>
          </Box>
        </Button>
      );
    });
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      <Typography sx={{ color: theme.palette.customColors.gray, fontSize: '16px' }}>
        {receiveTranslation('plot-require')}
      </Typography>
      {isFetchingFarmPlot && isLoadingFarmPlot ? <LinearProgress /> : renderList()}
    </Box>
  );
};
