import { Box, Button } from '@mui/material';
import { useTranslations } from 'next-intl';
import { FC, useEffect, useMemo, useState } from 'react';
import { Drawer } from 'components';
import { Farm, Plot } from 'types';
import { AddPlot } from './add-plot';

interface AddPlotDrawerProps {
  open: boolean;
  toggle: () => void;
  farm: Farm;
  plot: Plot[];
  onAddPlot: (plot: Plot[]) => void;
}

export const AddPlotDrawer: FC<AddPlotDrawerProps> = ({ open, toggle, farm, plot, onAddPlot }) => {
  const commonT = useTranslations('common');
  const receivingTranslation = useTranslations('receive');

  const [selectedPlots, setSelectedPlots] = useState<Plot[]>([]);

  const drawerTitle = `${receivingTranslation('select-plot')}`;

  useEffect(() => {
    if (open && plot.length > 0) {
      setSelectedPlots(plot);
    }
  }, [open, plot]);

  const reset = () => {
    setSelectedPlots([]);
  };

  const handleClose = () => {
    reset();
    toggle();
  };

  const enabledSaveButton = useMemo(() => {
    return selectedPlots.length > 0;
  }, [selectedPlots]);

  const handleAddPlot = () => {
    if (enabledSaveButton) {
      onAddPlot(selectedPlots);
      handleClose();
    }
  };

  const plotFooterElement = (
    <Box
      sx={{
        px: 2,
        py: 1.5,
        borderTop: 1,
        borderColor: 'divider',
        display: 'flex',
        justifyContent: 'space-between',
        gap: 1,
      }}
    >
      <Button fullWidth variant="outlined" onClick={handleClose}>
        {commonT('cancel-modal-btn')}
      </Button>
      <Button fullWidth variant="contained" onClick={handleAddPlot} disabled={!enabledSaveButton}>
        {commonT('confirm-modal-btn')}
      </Button>
    </Box>
  );

  return (
    <Drawer
      anchor="right"
      drawerTitle={drawerTitle}
      open={open}
      onClose={() => handleClose()}
      hasActionBtn={false}
      footerElement={plotFooterElement}
    >
      <Box
        component="div"
        sx={{ width: '100%', p: '24px 16px', display: 'flex', flexDirection: 'column', gap: '16px' }}
      >
        <AddPlot
          farm={farm}
          selectedPlots={selectedPlots}
          setSelectedPlots={(plots: Plot[]) => setSelectedPlots(plots)}
        />
      </Box>
    </Drawer>
  );
};
