import { Box, Chip, IconButton, Typography } from '@mui/material';
import { GridRenderCellParams } from '@mui/x-data-grid';
import { DIFFERENCE_THAI_YEAR } from 'components/date-picker/custom-date-picker';
import { DD_MMMM_YYYY_HH_mm_WITH_DASH, DD_MMMM_YYYY_WITH_DASH } from 'constant/common';
import dayjs, { Dayjs } from 'dayjs';
import 'dayjs/locale/th';
import { flatMap, get } from 'lodash-es';
import Image from 'next/image';

import { EventStatusEnum, EventType, PackingHouse, PackingHouseDetail, Plot, RecordedByEnum } from 'types';
import { formatNumberWithCommas, getImageUrl, getStatusBgColor, getStatusBorderColor, getStatusColor } from 'utils';
import { getCookieLocale } from 'utils/cookie-client';
import farmerSrc from 'assets/icons/user.svg';
import cutterSrc from 'assets/icons/knife.svg';
import packingSrc from 'assets/icons/box.svg';
import { EllipsisTypography, ImageReviewModal } from 'components';
import { ClearRounded } from '@mui/icons-material';
import { colors } from 'styles/colors';

const DEFAULT_VALUE = '--';
const FALSY_NAME = '-- --';

export const renderTotalWeight = (params: GridRenderCellParams<PackingHouse>) => {
  const grades = flatMap(params.row?.varieties, (it) => it.grades) ?? [];

  return (
    <Typography fontWeight={400} fontSize="16px" sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>
      {formatNumberWithCommas(grades?.reduce((total: number, item) => total + item.weight, 0))}
    </Typography>
  );
};

export const renderFarmName = (params: GridRenderCellParams<PackingHouse>) => {
  return (
    <Box title={params.row.farm.name || DEFAULT_VALUE} sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>
      <Typography
        variant="body2"
        sx={{
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
          width: '100%',
          cursor: 'default',
          fontSize: '16px',
        }}
      >
        {params.row.farm.name || DEFAULT_VALUE}
      </Typography>
    </Box>
  );
};

export const renderRecordBy = (params: GridRenderCellParams<PackingHouse>) => {
  const eventType = params.row.type as EventType;
  const isReceiving = eventType === 'receiving';

  const userCreated = isReceiving ? get(params.row, 'originUserCreated', null) : get(params.row, 'userCreated', null);

  if (!userCreated) {
    return (
      <Typography fontWeight={400} fontSize="16px" sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>
        {DEFAULT_VALUE}
      </Typography>
    );
  }

  const userRole = userCreated.role as RecordedByEnum;

  let iconSrc: string;
  switch (userRole) {
    case RecordedByEnum.PACKING_HOUSE:
      iconSrc = packingSrc;
      break;
    case RecordedByEnum.CUTTER:
      iconSrc = cutterSrc;
      break;
    case RecordedByEnum.FARMER:
      iconSrc = farmerSrc;
      break;
    default:
      iconSrc = '';
      break;
  }

  const fullName = `${userCreated?.firstName ?? ''} ${userCreated?.lastName ?? ''}`?.trim();

  const nickname = userCreated?.profile?.nickname ?? '';

  let displayName = fullName;

  if (nickname) {
    displayName = `${nickname} / ${fullName}`;
  }

  return (
    <Typography
      fontWeight={400}
      fontSize="16px"
      component={'div'}
      sx={{
        display: 'flex',
        alignItems: 'center',
        height: '100%',
        gap: '8px',
        fontSize: '16px',
      }}
    >
      {iconSrc && <Image src={iconSrc} alt={userRole} width={20} height={20} unoptimized />}
      <EllipsisTypography text={displayName} />
    </Typography>
  );
};

export const renderRecordByInDetailContent = (
  userRole: RecordedByEnum,
  firstName: string,
  lastName: string,
  nickname: string
) => {
  let iconSrc: string;
  let recordByColor: string;
  switch (userRole) {
    case RecordedByEnum.PACKING_HOUSE:
      iconSrc = packingSrc;
      recordByColor = colors.recordByPackingHouse;
      break;
    case RecordedByEnum.CUTTER:
      iconSrc = cutterSrc;
      recordByColor = colors.recordByCutter;
      break;
    case RecordedByEnum.FARMER:
      iconSrc = farmerSrc;
      recordByColor = colors.recordByFarmer;
      break;
    default:
      iconSrc = '';
      recordByColor = colors.recordByPackingHouse;
      break;
  }

  const fullName = `${firstName} ${lastName}`.trim();

  let displayName = fullName;

  if (nickname) {
    displayName = `${nickname} / ${fullName}`;
  }
  return (
    <Box sx={{ display: 'flex', gap: 1, color: recordByColor, alignItems: 'center' }}>
      {iconSrc && <Image src={iconSrc} alt={userRole} width={20} height={20} unoptimized />}
      <Typography>{displayName}</Typography>
    </Box>
  );
};

export const renderRecordByInDetail = (isReceiving: boolean, data: PackingHouseDetail) => {
  if (!isReceiving) {
    return renderRecordByInDetailContent(
      data.userCreated.role as RecordedByEnum,
      data.userCreated.firstName ?? '',
      data.userCreated.lastName ?? '',
      data.userCreated.profile?.nickname ?? ''
    );
  }
  if (data.originUserCreated) {
    return renderRecordByInDetailContent(
      data.originUserCreated.role as RecordedByEnum,
      data.originUserCreated.firstName ?? '',
      data.originUserCreated.lastName ?? '',
      data.originUserCreated.profile?.nickname ?? ''
    );
  }
  return DEFAULT_VALUE;
};

export const renderRecordOn = (params: GridRenderCellParams<PackingHouse>, format?: string) => {
  return (
    <Typography fontWeight={400} fontSize="16px" sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>
      {params.row.dateCreated
        ? formatDateWithLocale(params.row.dateCreated, format ?? DD_MMMM_YYYY_WITH_DASH)
        : DEFAULT_VALUE}
    </Typography>
  );
};

export const renderUpdatedOn = (params: GridRenderCellParams<PackingHouse>, format?: string) => {
  return (
    <Typography fontWeight={400} fontSize="16px" sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>
      {params.row.dateUpdated
        ? formatDateWithLocale(params.row.dateUpdated, format ?? DD_MMMM_YYYY_WITH_DASH)
        : DEFAULT_VALUE}
    </Typography>
  );
};

export const renderUpdatedBy = (params: GridRenderCellParams<PackingHouse>) => {
  const updatedBy = `${params.row.userUpdated?.firstName ?? DEFAULT_VALUE} ${params.row.userUpdated?.lastName ?? DEFAULT_VALUE}`;
  return (
    <Typography
      fontWeight={400}
      fontSize="16px"
      sx={{ display: 'flex', alignItems: 'center', height: '100%', textTransform: 'capitalize' }}
      component={'div'}
    >
      <EllipsisTypography
        text={updatedBy === FALSY_NAME ? DEFAULT_VALUE : updatedBy.replace(DEFAULT_VALUE, '').trim()}
      />
    </Typography>
  );
};

export const renderBatchname = (params: GridRenderCellParams<PackingHouse>) => {
  return (
    <Typography
      fontWeight={400}
      fontSize="16px"
      sx={{ display: 'flex', alignItems: 'center', height: '100%', textTransform: 'capitalize' }}
      component={'div'}
    >
      <EllipsisTypography text={params.row.name} />
    </Typography>
  );
};

const formatDateWithLocaleBase = (value: string | number | Dayjs, format: string) => {
  if (!value) return '';

  const isThai = getCookieLocale() === 'th';

  const thaiAddYear = isThai ? DIFFERENCE_THAI_YEAR : 0;

  return dayjs(value).add(thaiAddYear, 'year').format(format);
};

export const formatDateWithLocale = (value: string | number | Dayjs, format: string = DD_MMMM_YYYY_WITH_DASH) => {
  return formatDateWithLocaleBase(value, format);
};

export const formatDateTimeWithLocale = (
  value: string | number | Dayjs,
  format: string = DD_MMMM_YYYY_HH_mm_WITH_DASH
) => {
  return formatDateWithLocaleBase(value, format);
};

export const renderStatus = (status: EventStatusEnum, statusLabel: string) => {
  return (
    <Chip
      label={statusLabel}
      // size="medium"
      sx={{
        borderRadius: '9999px',
        backgroundColor: getStatusBgColor(status),
        color: getStatusColor(status),
        borderColor: getStatusBorderColor(status),
        borderWidth: '1px',
        borderStyle: 'solid',
        textTransform: 'capitalize',
        fontWeight: 'medium',
        height: 'fit-content',
        verticalAlign: 'unset',
        '.MuiChip-label': {
          fontSize: '14px',
          fontWeight: 400,
          lineHeight: '150%' /* 18px */,
        },
      }}
    />
  );
};

export const renderImageContent = (plot: Plot, removeImageFromPlot: (id: string) => void) => {
  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, position: 'relative' }}>
      <ImageReviewModal imageUrl={getImageUrl(plot.image) ?? null} imageSize={120} />
      <IconButton
        size="small"
        sx={{ position: 'absolute', right: 0, top: 0, p: 0 }}
        color="error"
        onClick={() => removeImageFromPlot(plot.id)}
      >
        <ClearRounded fontSize="inherit" color="inherit" />
      </IconButton>
    </Box>
  );
};
