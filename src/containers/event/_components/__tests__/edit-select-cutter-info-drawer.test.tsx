/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * Unit tests for EditSelectCutterInfoDrawer component
 *
 * This test suite covers:
 * - Basic rendering with cutter and vehicle data
 * - Conditional rendering based on profile ID (ExistedCutter vs CreateCutter)
 * - Save and Cancel functionality
 * - State management and prop updates
 * - Footer button states and interactions
 *
 * To run these tests:
 * - Single test file: yarn test --testPathPatterns=edit-select-cutter-info-drawer.test.tsx
 * - With coverage: yarn test --testPathPatterns=edit-select-cutter-info-drawer.test.tsx --coverage
 * - Watch mode: yarn test --testPathPatterns=edit-select-cutter-info-drawer.test.tsx --watch
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { EditSelectCutterInfoDrawer } from '../edit-select-cutter-info-drawer';
import { Cutter, CutterVehicle } from 'types';

// Mock the Drawer component
jest.mock('components', () => ({
  Drawer: ({ children, open, drawerTitle, footerElement, onClose }: any) => (
    open ? (
      <div data-testid="drawer">
        <div data-testid="drawer-title">{drawerTitle}</div>
        <div data-testid="drawer-content">{children}</div>
        <div data-testid="drawer-footer">{footerElement}</div>
        <button data-testid="drawer-close" onClick={onClose}>Close</button>
      </div>
    ) : null
  ),
}));

// Mock child components
jest.mock('../create-cutter/create-cutter', () => ({
  CreateCutter: ({ cutter, setCutter }: any) => (
    <div data-testid="create-cutter">
      <input
        data-testid="cutter-name-input"
        value={cutter.name}
        onChange={(e) => setCutter({ ...cutter, name: e.target.value })}
        placeholder="Cutter Name"
      />
      <input
        data-testid="cutter-phone-input"
        value={cutter.phoneNumber ?? ''}
        onChange={(e) => setCutter({ ...cutter, phoneNumber: e.target.value })}
        placeholder="Phone Number"
      />
    </div>
  ),
}));

jest.mock('../create-cutter/exist-cutter', () => ({
  ExistedCutter: ({ cutter }: any) => (
    <div data-testid="existed-cutter">
      <span data-testid="existed-cutter-name">{cutter.name}</span>
      <span data-testid="existed-cutter-phone">{cutter.phoneNumber}</span>
    </div>
  ),
}));

jest.mock('../create-cutter/create-cutter-vehicle', () => ({
  CreateCutterVehicle: ({ vehicle, setVehicle }: any) => (
    <div data-testid="create-cutter-vehicle">
      <input
        data-testid="vehicle-registration-input"
        value={vehicle.vehicleRegistrationNumber}
        onChange={(e) => setVehicle({ ...vehicle, vehicleRegistrationNumber: e.target.value })}
        placeholder="Vehicle Registration"
      />
      <input
        data-testid="province-registration-input"
        value={vehicle.provinceRegistrationNumber}
        onChange={(e) => setVehicle({ ...vehicle, provinceRegistrationNumber: e.target.value })}
        placeholder="Province Registration"
      />
    </div>
  ),
}));

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: (namespace: string) => (key: string) => `${namespace}.${key}`,
}));

describe('EditSelectCutterInfoDrawer', () => {
  const mockCutterWithProfile: Cutter = {
    id: 'cutter-1',
    name: 'John Doe',
    licenseNumber: 'LIC123',
    isCertified: true,
    phoneNumber: '**********',
    profileId: 'profile-1',
    existingCutterProfileId: 'profile-1',
    existingCutterId: 'cutter-1',
    avatar: {
      id: 'avatar-1',
      filenameDisk: 'avatar.jpg',
      filenameDownload: 'avatar.jpg',
    },
  };

  const mockCutterWithoutProfile: Cutter = {
    id: 'cutter-2',
    name: 'Jane Smith',
    licenseNumber: 'LIC456',
    isCertified: false,
    phoneNumber: '**********',
    profileId: null,
    existingCutterProfileId: null,
    existingCutterId: null,
    avatar: {
      id: 'avatar-2',
      filenameDisk: 'avatar2.jpg',
      filenameDownload: 'avatar2.jpg',
    },
  };

  const mockCutterVehicle: CutterVehicle = {
    image: {
      id: 'vehicle-img-1',
      filenameDisk: 'vehicle.jpg',
      filenameDownload: 'vehicle.jpg',
    },
    provinceRegistrationNumber: 'BKK',
    vehicleRegistrationNumber: 'ABC123',
  };

  const defaultProps = {
    open: true,
    toggle: jest.fn(),
    currentCutter: mockCutterWithProfile,
    currentCutterVehicle: mockCutterVehicle,
    onSave: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('should render drawer when open is true', () => {
      render(<EditSelectCutterInfoDrawer {...defaultProps} />);

      expect(screen.getByTestId('drawer')).toBeInTheDocument();
      expect(screen.getByTestId('drawer-title')).toHaveTextContent('common.edit receive.cutter-information');
    });

    it('should not render drawer when open is false', () => {
      render(<EditSelectCutterInfoDrawer {...defaultProps} open={false} />);

      expect(screen.queryByTestId('drawer')).not.toBeInTheDocument();
    });

    it('should render footer with Save and Cancel buttons', () => {
      render(<EditSelectCutterInfoDrawer {...defaultProps} />);

      expect(screen.getByText('common.save-modal-btn')).toBeInTheDocument();
      expect(screen.getByText('common.cancel-modal-btn')).toBeInTheDocument();
    });
  });

  describe('Conditional Component Rendering', () => {
    it('should render ExistedCutter when cutter has profileId', () => {
      render(<EditSelectCutterInfoDrawer {...defaultProps} currentCutter={mockCutterWithProfile} />);

      expect(screen.getByTestId('existed-cutter')).toBeInTheDocument();
      expect(screen.queryByTestId('create-cutter')).not.toBeInTheDocument();
    });

    it('should render CreateCutter when cutter has no profileId', () => {
      render(<EditSelectCutterInfoDrawer {...defaultProps} currentCutter={mockCutterWithoutProfile} />);

      expect(screen.getByTestId('create-cutter')).toBeInTheDocument();
      expect(screen.queryByTestId('existed-cutter')).not.toBeInTheDocument();
    });

    it('should always render CreateCutterVehicle component', () => {
      render(<EditSelectCutterInfoDrawer {...defaultProps} />);

      expect(screen.getByTestId('create-cutter-vehicle')).toBeInTheDocument();
    });
  });

  describe('Save Button State', () => {
    it('should enable save button when all required fields are filled', () => {
      render(<EditSelectCutterInfoDrawer {...defaultProps} />);

      const saveButton = screen.getByText('common.save-modal-btn');
      expect(saveButton).not.toBeDisabled();
    });

    it('should disable save button when cutter name is empty', () => {
      const cutterWithEmptyName = { ...mockCutterWithoutProfile, name: '' };
      render(<EditSelectCutterInfoDrawer {...defaultProps} currentCutter={cutterWithEmptyName} />);

      const saveButton = screen.getByText('common.save-modal-btn');
      expect(saveButton).toBeDisabled();
    });

    it('should disable save button when phone number is missing', () => {
      const cutterWithoutPhone = { ...mockCutterWithoutProfile, phoneNumber: undefined };
      render(<EditSelectCutterInfoDrawer {...defaultProps} currentCutter={cutterWithoutPhone} />);

      const saveButton = screen.getByText('common.save-modal-btn');
      expect(saveButton).toBeDisabled();
    });
  });

  describe('User Interactions', () => {
    it('should call onSave with updated data when save button is clicked', async () => {
      const user = userEvent.setup();
      const mockOnSave = jest.fn();

      render(<EditSelectCutterInfoDrawer {...defaultProps} onSave={mockOnSave} />);

      const saveButton = screen.getByText('common.save-modal-btn');
      await user.click(saveButton);

      expect(mockOnSave).toHaveBeenCalledWith(mockCutterWithProfile, mockCutterVehicle);
    });

    it('should call toggle when cancel button is clicked', async () => {
      const user = userEvent.setup();
      const mockToggle = jest.fn();

      render(<EditSelectCutterInfoDrawer {...defaultProps} toggle={mockToggle} />);

      const cancelButton = screen.getByText('common.cancel-modal-btn');
      await user.click(cancelButton);

      expect(mockToggle).toHaveBeenCalled();
    });

    it('should call toggle when drawer close is triggered', async () => {
      const user = userEvent.setup();
      const mockToggle = jest.fn();

      render(<EditSelectCutterInfoDrawer {...defaultProps} toggle={mockToggle} />);

      const closeButton = screen.getByTestId('drawer-close');
      await user.click(closeButton);

      expect(mockToggle).toHaveBeenCalled();
    });

    it('should update cutter data when CreateCutter setCutter is called', async () => {
      const user = userEvent.setup();
      const mockOnSave = jest.fn();

      render(<EditSelectCutterInfoDrawer {...defaultProps} currentCutter={mockCutterWithoutProfile} onSave={mockOnSave} />);

      // Update cutter name through CreateCutter component
      const nameInput = screen.getByTestId('cutter-name-input');
      await user.clear(nameInput);
      await user.type(nameInput, 'Updated Name');

      // Save to verify the updated data
      const saveButton = screen.getByText('common.save-modal-btn');
      await user.click(saveButton);

      expect(mockOnSave).toHaveBeenCalledWith(
        expect.objectContaining({
          ...mockCutterWithoutProfile,
          name: 'Updated Name',
        }),
        mockCutterVehicle
      );
    });

    it('should update vehicle data when CreateCutterVehicle setVehicle is called', async () => {
      const user = userEvent.setup();
      const mockOnSave = jest.fn();

      render(<EditSelectCutterInfoDrawer {...defaultProps} onSave={mockOnSave} />);

      // Update vehicle registration through CreateCutterVehicle component
      const vehicleInput = screen.getByTestId('vehicle-registration-input');
      await user.clear(vehicleInput);
      await user.type(vehicleInput, 'XYZ789');

      // Save to verify the updated data
      const saveButton = screen.getByText('common.save-modal-btn');
      await user.click(saveButton);

      expect(mockOnSave).toHaveBeenCalledWith(
        mockCutterWithProfile,
        expect.objectContaining({
          ...mockCutterVehicle,
          vehicleRegistrationNumber: 'XYZ789',
        })
      );
    });
  });
});
