/* eslint-disable @next/next/no-img-element */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-require-imports */
import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { CreateVarietiesModal } from '../create-variety-modal';
import { ThemeProvider } from '@mui/material/styles';
import { theme } from 'styles/theme';
import dayjs from 'dayjs';
import { FormTextInputProps } from 'components';

// Mock dependencies
jest.mock('@tanstack/react-query', () => ({
  useQuery: jest.fn(),
}));

jest.mock('services/resource.service', () => ({
  fetchVarietiesService: jest.fn(),
}));

jest.mock('utils/cookie-client', () => ({
  getCookieLocale: jest.fn(() => 'en'),
}));

jest.mock('components/date-picker/custom-date-picker', () => ({
  CustomDatePicker: () => <input data-testid="custom-date-picker" />,
}));

jest.mock('utils', () => ({
  formatNumberWithCommas: jest.fn((value) => value),
  isValidNumberInput: jest.fn(() => true),
}));

jest.mock('utils/input-grade', () => ({
  cleanDecimalInput: jest.fn((value) => value),
  ensureVarietyInMap: jest.fn(),
  findVarietyAndGrade: jest.fn(),
  hasLeadingZero: jest.fn(() => false),
  isValidValue: jest.fn(() => true),
  normalizeValue: jest.fn((value) => value),
  processDecimalValue: jest.fn((value) => value),
  processIntegerValue: jest.fn((value) => value),
}));

jest.mock('store/useMaterDataStore', () => ({
  useMasterDataStore: () => ({
    getVarietyLabel: jest.fn((id) => `Variety ${id}`),
    getGradeLabel: jest.fn((id) => `Grade ${id}`),
  }),
}));

jest.mock('react-hook-form', () => ({
  ...jest.requireActual('react-hook-form'),
  useForm: () => ({
    control: {},
    handleSubmit: jest.fn((fn) => (e: any) => {
      e?.preventDefault?.();
      return fn({});
    }),
    reset: jest.fn(),
    watch: jest.fn(() => ({ weights: {}, varietyBloomDays: {} })),
    formState: { errors: {} },
  }),
  useWatch: jest.fn(() => ({ weights: {}, varietyBloomDays: {} })),
  Controller: ({ render: renderProp }: any) =>
    renderProp({
      field: {
        onChange: jest.fn(),
        onBlur: jest.fn(),
        value: '',
      },
    }),
}));

jest.mock('next-intl', () => ({
  useTranslations: () => (key: string) => key,
}));

jest.mock('assets/icons/empty-state.svg', () => 'empty-state-icon.svg');

jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ src, alt }: { src: string; alt: string }) => <img src={src} alt={alt} data-testid="next-image" />,
}));

jest.mock('components', () => ({
  FormTextInput: ({ placeholder, name }: FormTextInputProps<any>) => (
    <input data-testid="form-text-input" placeholder={placeholder} name={name} />
  ),
}));

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider theme={theme}>{children}</ThemeProvider>
);

const mockVarietiesData = [
  {
    id: '1',
    value: 'mon-thong',
    label: { th: 'หมอนทอง', en: 'Monthong' },
    grades: [
      { id: 'A', value: 'A', label: { th: 'เกรด A', en: 'Grade A' }, weight: 0 },
      { id: 'B', value: 'B', label: { th: 'เกรด B', en: 'Grade B' }, weight: 0 },
    ],
    flowerBloomingDuration: 100,
    flowerBloomingDay: 90,
  },
  {
    id: '2',
    value: 'other',
    label: { th: 'อื่นๆ', en: 'Other' },
    grades: [{ id: 'D', value: 'D', label: { th: 'เกรด D', en: 'Grade D' }, weight: 0 }],
    flowerBloomingDuration: 90,
    flowerBloomingDay: 80,
  },
];

describe('CreateVarietiesModal', () => {
  const mockOnClose = jest.fn();
  const mockOnSave = jest.fn();

  const defaultProps = {
    open: true,
    onClose: mockOnClose,
    onSave: mockOnSave,
    initialWeights: {},
    initialCustomName: '',
    varietyBloomDays: {},
    cuttingDay: dayjs('2024-01-15'),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    const { useQuery } = require('@tanstack/react-query');
    useQuery.mockReturnValue({
      data: {
        data: mockVarietiesData,
      },
      isLoading: false,
      error: null,
    });
  });

  describe('Modal Rendering', () => {
    it('renders modal when open is true', () => {
      render(
        <TestWrapper>
          <CreateVarietiesModal {...defaultProps} />
        </TestWrapper>
      );

      expect(screen.getByText('specify-durian-weight-by-variety')).toBeInTheDocument();
    });

    it('does not render modal when open is false', () => {
      render(
        <TestWrapper>
          <CreateVarietiesModal {...defaultProps} open={false} />
        </TestWrapper>
      );

      expect(screen.queryByText('specify-durian-weight-by-variety')).not.toBeInTheDocument();
    });

    it('displays loading state', () => {
      const { useQuery } = require('@tanstack/react-query');
      useQuery.mockReturnValue({
        data: null,
        isLoading: true,
        error: null,
      });

      render(
        <TestWrapper>
          <CreateVarietiesModal {...defaultProps} />
        </TestWrapper>
      );

      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('calls onClose when close button is clicked', async () => {
      const user = userEvent.setup();
      render(
        <TestWrapper>
          <CreateVarietiesModal {...defaultProps} />
        </TestWrapper>
      );

      const closeButton = screen.getByLabelText('close');
      await user.click(closeButton);

      expect(mockOnClose).toHaveBeenCalled();
    });

    it('calls onClose when cancel button is clicked', async () => {
      const user = userEvent.setup();
      render(
        <TestWrapper>
          <CreateVarietiesModal {...defaultProps} />
        </TestWrapper>
      );

      const cancelButton = screen.getByText('cancel-modal-btn');
      await user.click(cancelButton);

      expect(mockOnClose).toHaveBeenCalled();
    });
  });

  describe('Varieties List', () => {
    it('renders varieties list', () => {
      render(
        <TestWrapper>
          <CreateVarietiesModal {...defaultProps} />
        </TestWrapper>
      );

      expect(screen.getByText('Variety 1')).toBeInTheDocument();
      expect(screen.getByText('Variety 2')).toBeInTheDocument();
    });

    it('selects variety when clicked', async () => {
      const user = userEvent.setup();
      render(
        <TestWrapper>
          <CreateVarietiesModal {...defaultProps} />
        </TestWrapper>
      );

      const varietyItem = screen.getByText('Variety 1');
      await user.click(varietyItem);

      // Check if the variety is selected by looking for grade inputs
      await waitFor(() => {
        expect(screen.getByText('Grade A')).toBeInTheDocument();
        expect(screen.getByText('Grade B')).toBeInTheDocument();
      });
    });

    it('shows empty state when no variety is selected', () => {
      render(
        <TestWrapper>
          <CreateVarietiesModal {...defaultProps} />
        </TestWrapper>
      );

      expect(screen.getByText('varieties-help-text')).toBeInTheDocument();
      expect(screen.getByTestId('next-image')).toBeInTheDocument();
    });

    it('shows check icon for varieties with data', () => {
      render(
        <TestWrapper>
          <CreateVarietiesModal
            {...defaultProps}
            initialWeights={{ '1-A': '100' }}
            varietyBloomDays={{ '1': dayjs().unix() }}
            initialCustomName="Test Custom"
          />
        </TestWrapper>
      );

      // Component should render with initial data
      expect(screen.getByText('specify-durian-weight-by-variety')).toBeInTheDocument();
    });
  });

  describe('Form Inputs', () => {
    it('shows custom variety name input for other variety', async () => {
      const user = userEvent.setup();
      render(
        <TestWrapper>
          <CreateVarietiesModal {...defaultProps} />
        </TestWrapper>
      );

      // Click on "other" variety
      const otherVariety = screen.getByText('Variety 2');
      await user.click(otherVariety);

      await waitFor(() => {
        expect(screen.getByPlaceholderText('enter-variety-name')).toBeInTheDocument();
      });
    });

    it('shows grade inputs when variety is selected', async () => {
      const user = userEvent.setup();
      render(
        <TestWrapper>
          <CreateVarietiesModal {...defaultProps} />
        </TestWrapper>
      );

      const varietyItem = screen.getByText('Variety 1');
      await user.click(varietyItem);

      await waitFor(() => {
        expect(screen.getByText('Grade A')).toBeInTheDocument();
        expect(screen.getByText('Grade B')).toBeInTheDocument();
      });
    });

    it('shows blooming day input when variety is selected', async () => {
      const user = userEvent.setup();
      render(
        <TestWrapper>
          <CreateVarietiesModal {...defaultProps} />
        </TestWrapper>
      );

      const varietyItem = screen.getByText('Variety 1');
      await user.click(varietyItem);

      await waitFor(() => {
        expect(screen.getByText('flower-blooming-day')).toBeInTheDocument();
        expect(screen.getByTestId('custom-date-picker')).toBeInTheDocument();
      });
    });

    it('handles weight input changes', () => {
      const { formatNumberWithCommas } = require('utils');

      render(
        <TestWrapper>
          <CreateVarietiesModal {...defaultProps} />
        </TestWrapper>
      );

      // Test that formatNumberWithCommas is available
      expect(formatNumberWithCommas).toBeDefined();
    });

    it('validates number input correctly', () => {
      const { isValidNumberInput } = require('utils');

      render(
        <TestWrapper>
          <CreateVarietiesModal {...defaultProps} />
        </TestWrapper>
      );

      // Test that isValidNumberInput is available
      expect(isValidNumberInput).toBeDefined();
    });
  });

  describe('Form Submission', () => {
    it('disables save button when no data is entered', () => {
      render(
        <TestWrapper>
          <CreateVarietiesModal {...defaultProps} />
        </TestWrapper>
      );

      const saveButton = screen.getByText('save-modal-btn');
      expect(saveButton).toBeDisabled();
    });

    it('calls onSave with correct payload when save button is clicked', () => {
      const { findVarietyAndGrade, ensureVarietyInMap } = require('utils/input-grade');

      render(
        <TestWrapper>
          <CreateVarietiesModal
            {...defaultProps}
            initialWeights={{ '1-A': '100' }}
            varietyBloomDays={{ '1': dayjs().unix() }}
          />
        </TestWrapper>
      );

      // Check that utility functions are available
      expect(findVarietyAndGrade).toBeDefined();
      expect(ensureVarietyInMap).toBeDefined();
    });

    it('handles form submission with empty varieties', () => {
      render(
        <TestWrapper>
          <CreateVarietiesModal {...defaultProps} />
        </TestWrapper>
      );

      // Check that save button exists
      const saveButton = screen.getByText('save-modal-btn');
      expect(saveButton).toBeInTheDocument();
    });

    it('handles error during form submission', () => {
      // Mock onSave to throw an error
      const mockOnSaveWithError = jest.fn();

      render(
        <TestWrapper>
          <CreateVarietiesModal
            {...defaultProps}
            onSave={mockOnSaveWithError}
            initialWeights={{ '1-A': '100' }}
            varietyBloomDays={{ '1': dayjs().unix() }}
          />
        </TestWrapper>
      );

      // Component should render without errors
      expect(screen.getByText('specify-durian-weight-by-variety')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles no varieties available', () => {
      const { useQuery } = require('@tanstack/react-query');
      useQuery.mockReturnValue({
        data: { data: [] },
        isLoading: false,
        error: null,
      });

      render(
        <TestWrapper>
          <CreateVarietiesModal {...defaultProps} />
        </TestWrapper>
      );

      // Component should handle empty varieties gracefully
      expect(screen.getByText('specify-durian-weight-by-variety')).toBeInTheDocument();
    });

    it('handles initial data population', () => {
      render(
        <TestWrapper>
          <CreateVarietiesModal
            {...defaultProps}
            initialWeights={{ '1-A': '500' }}
            initialCustomName="Custom Variety"
          />
        </TestWrapper>
      );

      // Component should render with initial data
      expect(screen.getByText('specify-durian-weight-by-variety')).toBeInTheDocument();
    });
  });
});
