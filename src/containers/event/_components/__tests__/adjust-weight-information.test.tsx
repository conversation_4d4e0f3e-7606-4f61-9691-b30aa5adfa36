/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-require-imports */
import React from 'react';
import { render, screen } from '@testing-library/react';
import { AdjustWeightInformation } from '../adjust-weight-information';
import { PackingHouseDetail } from 'types';
import { TestWrapper } from 'tests/utils/test-wrapper';

// Mock dependencies
jest.mock('next-intl', () => ({
  useTranslations: jest.fn(() => (key: string) => {
    const translations: Record<string, string> = {
      'weight-adjusted-help-text': 'Weight adjusted help text',
      'durian-varieties': 'Durian Varieties',
      'origin-weight': 'Origin Weight',
      'received-weight': 'Received Weight',
    };
    return translations[key] || key;
  }),
}));

jest.mock('utils/cookie-client', () => ({
  getCookieLocale: jest.fn(() => 'en'),
}));

const mockPackingHouseDetail: PackingHouseDetail = {
  id: 'test-id',
  status: 'waiting',
  type: 'receiving',
  name: 'Test Event',
  description: 'Test Description',
  address: 'Test Address',
  country: 'Thailand',
  dateCreated: '2023-12-25T10:00:00Z',
  images: [],
  logo: { id: 'logo1', filenameDisk: 'logo.jpg', filenameDownload: 'logo.jpg' },
  farm: {
    address: 'Farm Address',
    id: 'farm-1',
    name: 'Test Farm',
  },
  gln: '1234567890123',
  positionLongitude: 100.5,
  positionLatitude: 13.7,
  role: 'packing_house_staff',
  batchlot: 'BATCH-001',
  packingHouse: {} as any,
  userCreated: {
    id: 'user-1',
    firstName: 'John',
    lastName: 'Doe',
    role: 'farmer',
    profile: {
      nickname: 'Johnny',
    },
  },
  packing: [],
  meta: {
    diffVarieties: [
      {
        value: 'variety1',
        label: {
          th: 'ทุเรียนพันธุ์ 1',
          en: 'Durian Variety 1',
        },
        grades: [
          {
            value: 'grade1',
            label: {
              th: 'เกรด A',
              en: 'Grade A',
            },
            diff: {
              origin: 100,
              current: 95,
            },
          },
          {
            value: 'grade2',
            label: {
              th: 'เกรด B',
              en: 'Grade B',
            },
            diff: {
              origin: 50,
              current: 55,
            },
          },
        ],
      },
      {
        value: 'variety2',
        label: {
          th: 'ทุเรียนพันธุ์ 2',
          en: 'Durian Variety 2',
        },
        grades: [
          {
            value: 'grade3',
            label: {
              th: 'เกรด A',
              en: 'Grade A',
            },
            diff: {
              origin: 75,
              current: 80,
            },
          },
        ],
      },
    ],
  } as any,
  varieties: [],
  immutable: false,
};

describe('AdjustWeightInformation', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the component with correct structure', () => {
    render(
      <TestWrapper>
        <AdjustWeightInformation data={mockPackingHouseDetail} />
      </TestWrapper>
    );

    // Check if help text is rendered
    expect(screen.getByText('Weight adjusted help text')).toBeInTheDocument();

    // Check if table headers are rendered
    expect(screen.getByText('Durian Varieties')).toBeInTheDocument();
    expect(screen.getByText('Origin Weight')).toBeInTheDocument();
    expect(screen.getByText('Received Weight')).toBeInTheDocument();
  });

  it('displays variety and grade data correctly in English locale', () => {
    render(
      <TestWrapper>
        <AdjustWeightInformation data={mockPackingHouseDetail} />
      </TestWrapper>
    );

    // Check if variety and grade combinations are displayed
    expect(screen.getByText('Durian Variety 1 Grade A')).toBeInTheDocument();
    expect(screen.getByText('Durian Variety 1 Grade B')).toBeInTheDocument();
    expect(screen.getByText('Durian Variety 2 Grade A')).toBeInTheDocument();

    // Check if weight values are displayed with proper formatting
    expect(screen.getByText('100')).toBeInTheDocument(); // origin weight for variety1 grade1
    expect(screen.getByText('95')).toBeInTheDocument(); // current weight for variety1 grade1
    expect(screen.getByText('50')).toBeInTheDocument(); // origin weight for variety1 grade2
    expect(screen.getByText('55')).toBeInTheDocument(); // current weight for variety1 grade2
    expect(screen.getByText('75')).toBeInTheDocument(); // origin weight for variety2 grade1
    expect(screen.getByText('80')).toBeInTheDocument(); // current weight for variety2 grade1
  });

  it('displays variety and grade data correctly in Thai locale', () => {
    const { getCookieLocale } = require('utils/cookie-client');
    getCookieLocale.mockReturnValue('th');

    render(
      <TestWrapper>
        <AdjustWeightInformation data={mockPackingHouseDetail} />
      </TestWrapper>
    );

    // Check if Thai variety and grade combinations are displayed
    expect(screen.getByText('ทุเรียนพันธุ์ 1 เกรด A')).toBeInTheDocument();
    expect(screen.getByText('ทุเรียนพันธุ์ 1 เกรด B')).toBeInTheDocument();
    expect(screen.getByText('ทุเรียนพันธุ์ 2 เกรด A')).toBeInTheDocument();
  });

  it('handles null origin and current values gracefully', () => {
    const dataWithNullValues = {
      ...mockPackingHouseDetail,
      meta: {
        ...mockPackingHouseDetail.meta,
        diffVarieties: [
          {
            value: 'variety1',
            label: {
              th: 'ทุเรียนพันธุ์ 1',
              en: 'Durian Variety 1',
            },
            grades: [
              {
                value: 'grade1',
                label: {
                  th: 'เกรด A',
                  en: 'Grade A',
                },
                diff: {
                  origin: null,
                  current: null,
                },
              },
            ],
          },
        ],
      },
    };

    render(
      <TestWrapper>
        <AdjustWeightInformation data={dataWithNullValues} />
      </TestWrapper>
    );

    // Should display 0 for null values
    expect(screen.getAllByText('0')).toHaveLength(2); // origin and current both show 0
  });

  it('handles empty diffVarieties data', () => {
    const dataWithEmptyDiffVarieties = {
      ...mockPackingHouseDetail,
      meta: {
        ...mockPackingHouseDetail.meta,
        diffVarieties: [],
      },
    };

    render(
      <TestWrapper>
        <AdjustWeightInformation data={dataWithEmptyDiffVarieties} />
      </TestWrapper>
    );

    // Should still render headers but no data rows
    expect(screen.getByText('Durian Varieties')).toBeInTheDocument();
    expect(screen.getByText('Origin Weight')).toBeInTheDocument();
    expect(screen.getByText('Received Weight')).toBeInTheDocument();

    // Should not have any variety data
    expect(screen.queryByText('Durian Variety 1 Grade A')).not.toBeInTheDocument();
  });

  it('handles undefined diffVarieties data', () => {
    const dataWithUndefinedDiffVarieties = {
      ...mockPackingHouseDetail,
      meta: {
        ...mockPackingHouseDetail.meta,
        diffVarieties: undefined,
      },
    };

    render(
      <TestWrapper>
        <AdjustWeightInformation data={dataWithUndefinedDiffVarieties} />
      </TestWrapper>
    );

    // Should still render headers but no data rows
    expect(screen.getByText('Durian Varieties')).toBeInTheDocument();
    expect(screen.getByText('Origin Weight')).toBeInTheDocument();
    expect(screen.getByText('Received Weight')).toBeInTheDocument();

    // Should not have any variety data
    expect(screen.queryByText('Durian Variety 1 Grade A')).not.toBeInTheDocument();
  });

  it('formats numbers with locale string correctly', () => {
    const dataWithLargeNumbers = {
      ...mockPackingHouseDetail,
      meta: {
        ...mockPackingHouseDetail.meta,
        diffVarieties: [
          {
            value: 'variety1',
            label: {
              th: 'ทุเรียนพันธุ์ 1',
              en: 'Durian Variety 1',
            },
            grades: [
              {
                value: 'grade1',
                label: {
                  th: 'เกรด A',
                  en: 'Grade A',
                },
                diff: {
                  origin: 1000,
                  current: 2500,
                },
              },
            ],
          },
        ],
      },
    };

    render(
      <TestWrapper>
        <AdjustWeightInformation data={dataWithLargeNumbers} />
      </TestWrapper>
    );

    // Check if numbers are formatted with locale string (comma separators)
    expect(screen.getByText('1,000')).toBeInTheDocument();
    expect(screen.getByText('2,500')).toBeInTheDocument();
  });

  it('renders table with correct accessibility attributes', () => {
    render(
      <TestWrapper>
        <AdjustWeightInformation data={mockPackingHouseDetail} />
      </TestWrapper>
    );

    // Check if table has correct aria-label
    const table = screen.getByRole('table');
    expect(table).toHaveAttribute('aria-label', 'data table');

    // Check if table headers are properly structured
    const columnHeaders = screen.getAllByRole('columnheader');
    expect(columnHeaders).toHaveLength(3);
    expect(columnHeaders[0]).toHaveTextContent('Durian Varieties');
    expect(columnHeaders[1]).toHaveTextContent('Origin Weight');
    expect(columnHeaders[2]).toHaveTextContent('Received Weight');
  });

  it('applies correct styling to table elements', () => {
    render(
      <TestWrapper>
        <AdjustWeightInformation data={mockPackingHouseDetail} />
      </TestWrapper>
    );

    // Check if help text has correct styling
    const helpText = screen.getByText('Weight adjusted help text');
    expect(helpText).toHaveStyle({
      fontSize: '14px',
    });

    // Check if table container exists (styling is applied via sx prop)
    const tableContainer = screen.getByRole('table').closest('.MuiPaper-root');
    expect(tableContainer).toBeInTheDocument();
  });

  it('generates correct index keys for table rows', () => {
    // Reset locale to English for this test
    const { getCookieLocale } = require('utils/cookie-client');
    getCookieLocale.mockReturnValue('en');

    render(
      <TestWrapper>
        <AdjustWeightInformation data={mockPackingHouseDetail} />
      </TestWrapper>
    );

    // Check if all table rows are rendered (3 grades total)
    const tableRows = screen.getAllByRole('row');
    // 1 header row + 3 data rows
    expect(tableRows).toHaveLength(4);

    // Check if data rows contain the expected content
    const dataRows = tableRows.slice(1); // Skip header row
    expect(dataRows[0]).toHaveTextContent('Durian Variety 1 Grade A');
    expect(dataRows[1]).toHaveTextContent('Durian Variety 1 Grade B');
    expect(dataRows[2]).toHaveTextContent('Durian Variety 2 Grade A');
  });

  it('handles flattenData function edge cases', () => {
    // Test with variety that has no grades
    const dataWithEmptyGrades = {
      ...mockPackingHouseDetail,
      meta: {
        ...mockPackingHouseDetail.meta,
        diffVarieties: [
          {
            value: 'variety1',
            label: {
              th: 'ทุเรียนพันธุ์ 1',
              en: 'Durian Variety 1',
            },
            grades: [],
          },
        ],
      },
    };

    render(
      <TestWrapper>
        <AdjustWeightInformation data={dataWithEmptyGrades} />
      </TestWrapper>
    );

    // Should render headers but no data rows
    expect(screen.getByText('Durian Varieties')).toBeInTheDocument();
    expect(screen.queryByText('Durian Variety 1')).not.toBeInTheDocument();
  });
});
