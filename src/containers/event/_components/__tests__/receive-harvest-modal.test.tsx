/* eslint-disable @typescript-eslint/no-require-imports */
import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ReceiveHarvestModal } from '../receive-harvest-modal';
import { ThemeProvider } from '@mui/material/styles';
import { theme } from 'styles/theme';
import dayjs from 'dayjs';

// Mock dependencies
jest.mock('@tanstack/react-query', () => ({
  useQuery: jest.fn(),
}));

jest.mock('services/resource.service', () => ({
  fetchVarietiesService: jest.fn(),
}));

jest.mock('utils/cookie-client', () => ({
  getCookieLocale: jest.fn(() => 'en'),
}));

jest.mock('components/date-picker/custom-date-picker', () => ({
  CustomDatePicker: () => <input data-testid="custom-date-picker" />,
}));

jest.mock('utils', () => ({
  formatNumberWithCommas: jest.fn((value) => value),
  isValidNumberInput: jest.fn(() => true),
}));

jest.mock('utils/input-grade', () => ({
  cleanDecimalInput: jest.fn((value) => value),
  ensureVarietyInMap: jest.fn(),
  findVarietyAndGrade: jest.fn(),
  hasLeadingZero: jest.fn(() => false),
  isValidValue: jest.fn(() => true),
  normalizeValue: jest.fn((value) => value),
  processDecimalValue: jest.fn((value) => value),
  processIntegerValue: jest.fn((value) => value),
}));

jest.mock('dayjs', () => {
  const originalDayjs = jest.requireActual('dayjs');
  return jest.fn((date) => originalDayjs(date));
});

jest.mock('store/useMaterDataStore', () => ({
  useMasterDataStore: jest.fn(() => ({
    getVarietyLabel: jest.fn((id) => `Variety ${id}`),
    getGradeLabel: jest.fn((id) => `Grade ${id}`),
  })),
}));

jest.mock('next-intl', () => ({
  useTranslations: jest.fn(() => (key: string) => key),
}));

// Mock MUI components that might cause issues
jest.mock('@mui/material', () => ({
  ...jest.requireActual('@mui/material'),
  CircularProgress: () => <div data-testid="circular-progress">Loading...</div>,
}));

const { useQuery } = require('@tanstack/react-query');

const mockVarieties = [
  {
    id: '1',
    value: 'mon-thong',
    label: { th: 'หมอนทอง', en: 'Mon Thong' },
    flowerBloomingDay: 90,
    grades: [
      { id: 'A', value: 'A', label: { th: 'เกรด A', en: 'Grade A' }, weight: 0 },
      { id: 'B', value: 'B', label: { th: 'เกรด B', en: 'Grade B' }, weight: 0 },
    ],
  },
  {
    id: '2',
    value: 'chanee',
    label: { th: 'ชะนี', en: 'Chanee' },
    flowerBloomingDay: 85,
    grades: [
      { id: 'A', value: 'A', label: { th: 'เกรด A', en: 'Grade A' }, weight: 0 },
      { id: 'C', value: 'C', label: { th: 'เกรด C', en: 'Grade C' }, weight: 0 },
    ],
  },
  {
    id: '3',
    value: 'other',
    label: { th: 'อื่นๆ', en: 'Other' },
    flowerBloomingDay: 80,
    grades: [{ id: 'D', value: 'D', label: { th: 'เกรด D', en: 'Grade D' }, weight: 0 }],
  },
];

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider theme={theme}>{children}</ThemeProvider>
);

const mockOnClose = jest.fn();
const mockOnSave = jest.fn();

const defaultProps = {
  open: true,
  onClose: mockOnClose,
  onSave: mockOnSave,
  varieties: mockVarieties,
  initialWeights: {},
  initialCustomName: '',
  useProvidedVarieties: true,
  editableVarietyIds: new Set(['1', '2', '3']),
  varietyBloomDays: {
    '1': dayjs().unix(),
    '2': dayjs().unix(),
    '3': dayjs().unix(),
  },
};



describe('ReceiveHarvestModal', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock useQuery to return varieties data
    useQuery.mockImplementation(({ queryKey }: { queryKey: unknown[] }) => {
      if (queryKey[0] === 'varieties') {
        return {
          data: { data: mockVarieties },
          isLoading: false,
          error: null,
        };
      }
      return { data: undefined, isLoading: false, error: null };
    });
  });

  describe('Edit Mode (isEditReceiving = true)', () => {
    const editModeProps = { ...defaultProps, isEditReceiving: true };

    describe('Modal Rendering', () => {
      it('renders modal when open is true in edit mode', () => {
        render(
          <TestWrapper>
            <ReceiveHarvestModal {...editModeProps} />
          </TestWrapper>
        );

        expect(screen.getByText('varieties-update')).toBeInTheDocument();
      });

      it('does not render modal when open is false in edit mode', () => {
        render(
          <TestWrapper>
            <ReceiveHarvestModal {...editModeProps} open={false} />
          </TestWrapper>
        );

        expect(screen.queryByText('varieties-update')).not.toBeInTheDocument();
      });

      it('renders loading state when fetching varieties in edit mode', () => {
        useQuery.mockImplementation(() => ({
          data: undefined,
          isLoading: true,
          error: null,
        }));

        render(
          <TestWrapper>
            <ReceiveHarvestModal {...editModeProps} useProvidedVarieties={false} />
          </TestWrapper>
        );

        expect(screen.getByTestId('circular-progress')).toBeInTheDocument();
      });

      it('renders error state when varieties fetch fails in edit mode', () => {
        const mockError = new Error('Failed to fetch varieties');
        useQuery.mockImplementation(() => ({
          data: undefined,
          isLoading: false,
          error: mockError,
        }));

        render(
          <TestWrapper>
            <ReceiveHarvestModal {...editModeProps} useProvidedVarieties={false} />
          </TestWrapper>
        );

        expect(screen.getByText(/Error loading varieties/)).toBeInTheDocument();
      });
    });

    describe('Button Configuration in Edit Mode', () => {
      it('renders save button with correct text, id, and class in edit mode', () => {
        render(
          <TestWrapper>
            <ReceiveHarvestModal {...editModeProps} />
          </TestWrapper>
        );

        const saveButton = screen.getByText('save-modal-btn');
        expect(saveButton).toBeInTheDocument();
        expect(saveButton).toHaveAttribute('id', 'save-modal-btn');
        expect(saveButton).toHaveClass('save-modal-btn');
      });

      it('save button is enabled when conditions are met in edit mode', () => {
        render(
          <TestWrapper>
            <ReceiveHarvestModal {...editModeProps} />
          </TestWrapper>
        );

        const saveButton = screen.getByText('save-modal-btn');
        expect(saveButton).not.toBeDisabled();
      });

      it('save button is disabled when loading in edit mode', () => {
        useQuery.mockImplementation(() => ({
          data: undefined,
          isLoading: true,
          error: null,
        }));

        render(
          <TestWrapper>
            <ReceiveHarvestModal {...editModeProps} useProvidedVarieties={false} />
          </TestWrapper>
        );

        // Button won't be visible during loading, but we can test the loading state
        expect(screen.getByTestId('circular-progress')).toBeInTheDocument();
      });
    });

    describe('Checkboxes in Edit Mode', () => {
      it('does not render checkboxes in edit mode', () => {
        render(
          <TestWrapper>
            <ReceiveHarvestModal {...editModeProps} />
          </TestWrapper>
        );

        expect(screen.queryByRole('checkbox', { name: /confirm-receive-harvest/ })).not.toBeInTheDocument();
        expect(screen.queryByRole('checkbox', { name: /aware-undo-action/ })).not.toBeInTheDocument();
      });
    });

    describe('Form Functionality in Edit Mode', () => {
      it('handles form submission in edit mode', async () => {
        const user = userEvent.setup();
        render(
          <TestWrapper>
            <ReceiveHarvestModal {...editModeProps} initialWeights={{ '1-A': '100' }} />
          </TestWrapper>
        );

        const saveButton = screen.getByText('save-modal-btn');
        await user.click(saveButton);

        // The save button should be clickable and not throw errors
        await waitFor(() => {
          expect(saveButton).toBeInTheDocument();
        });
      });
    });
  });

  describe('Confirm Mode (isEditReceiving = false)', () => {
    const confirmModeProps = { ...defaultProps, isEditReceiving: false };

    describe('Modal Rendering', () => {
      it('renders modal when open is true in confirm mode', () => {
        render(
          <TestWrapper>
            <ReceiveHarvestModal {...confirmModeProps} />
          </TestWrapper>
        );

        expect(screen.getByText('confirm-harvest-data')).toBeInTheDocument();
      });

      it('does not render modal when open is false in confirm mode', () => {
        render(
          <TestWrapper>
            <ReceiveHarvestModal {...confirmModeProps} open={false} />
          </TestWrapper>
        );

        expect(screen.queryByText('confirm-harvest-data')).not.toBeInTheDocument();
      });

      it('renders loading state when fetching varieties in confirm mode', () => {
        useQuery.mockImplementation(() => ({
          data: undefined,
          isLoading: true,
          error: null,
        }));

        render(
          <TestWrapper>
            <ReceiveHarvestModal {...confirmModeProps} useProvidedVarieties={false} />
          </TestWrapper>
        );

        expect(screen.getByTestId('circular-progress')).toBeInTheDocument();
      });

      it('renders error state when varieties fetch fails in confirm mode', () => {
        const mockError = new Error('Failed to fetch varieties');
        useQuery.mockImplementation(() => ({
          data: undefined,
          isLoading: false,
          error: mockError,
        }));

        render(
          <TestWrapper>
            <ReceiveHarvestModal {...confirmModeProps} useProvidedVarieties={false} />
          </TestWrapper>
        );

        expect(screen.getByText(/Error loading varieties/)).toBeInTheDocument();
      });
    });

    describe('Button Configuration in Confirm Mode', () => {
      it('renders confirm button with correct text, id, and class in confirm mode', () => {
        render(
          <TestWrapper>
            <ReceiveHarvestModal {...confirmModeProps} />
          </TestWrapper>
        );

        const confirmButton = screen.getByText('confirm-modal-btn');
        expect(confirmButton).toBeInTheDocument();
        expect(confirmButton).toHaveAttribute('id', 'confirm-modal-btn');
        expect(confirmButton).toHaveClass('confirm-modal-btn');
      });

      it('confirm button is disabled when checkboxes are unchecked in confirm mode', () => {
        render(
          <TestWrapper>
            <ReceiveHarvestModal {...confirmModeProps} />
          </TestWrapper>
        );

        const confirmButton = screen.getByText('confirm-modal-btn');
        expect(confirmButton).toBeDisabled();
      });

      it('confirm button is enabled when both checkboxes are checked in confirm mode', async () => {
        const user = userEvent.setup();
        render(
          <TestWrapper>
            <ReceiveHarvestModal {...confirmModeProps} />
          </TestWrapper>
        );

        const confirmCheckbox = screen.getByRole('checkbox', { name: /confirm-receive-harvest/ });
        const awareCheckbox = screen.getByRole('checkbox', { name: /aware-undo-action/ });
        const confirmButton = screen.getByText('confirm-modal-btn');

        await user.click(confirmCheckbox);
        await user.click(awareCheckbox);

        expect(confirmButton).not.toBeDisabled();
      });
    });

    describe('Checkboxes in Confirm Mode', () => {
      it('renders both required checkboxes in confirm mode', () => {
        render(
          <TestWrapper>
            <ReceiveHarvestModal {...confirmModeProps} />
          </TestWrapper>
        );

        expect(screen.getByRole('checkbox', { name: /confirm-receive-harvest/ })).toBeInTheDocument();
        expect(screen.getByRole('checkbox', { name: /aware-undo-action/ })).toBeInTheDocument();
      });

      it('checkboxes are initially unchecked in confirm mode', () => {
        render(
          <TestWrapper>
            <ReceiveHarvestModal {...confirmModeProps} />
          </TestWrapper>
        );

        const confirmCheckbox = screen.getByRole('checkbox', { name: /confirm-receive-harvest/ });
        const awareCheckbox = screen.getByRole('checkbox', { name: /aware-undo-action/ });

        expect(confirmCheckbox).not.toBeChecked();
        expect(awareCheckbox).not.toBeChecked();
      });

      it('can check and uncheck both checkboxes in confirm mode', async () => {
        const user = userEvent.setup();
        render(
          <TestWrapper>
            <ReceiveHarvestModal {...confirmModeProps} />
          </TestWrapper>
        );

        const confirmCheckbox = screen.getByRole('checkbox', { name: /confirm-receive-harvest/ });
        const awareCheckbox = screen.getByRole('checkbox', { name: /aware-undo-action/ });

        // Check both checkboxes
        await user.click(confirmCheckbox);
        await user.click(awareCheckbox);

        expect(confirmCheckbox).toBeChecked();
        expect(awareCheckbox).toBeChecked();

        // Uncheck both checkboxes
        await user.click(confirmCheckbox);
        await user.click(awareCheckbox);

        expect(confirmCheckbox).not.toBeChecked();
        expect(awareCheckbox).not.toBeChecked();
      });

      it('renders checkboxes in the dialog actions area in confirm mode', () => {
        render(
          <TestWrapper>
            <ReceiveHarvestModal {...confirmModeProps} />
          </TestWrapper>
        );

        const confirmCheckbox = screen.getByRole('checkbox', { name: /confirm-receive-harvest/ });
        const awareCheckbox = screen.getByRole('checkbox', { name: /aware-undo-action/ });
        const cancelButton = screen.getByText('cancel-modal-btn');
        const confirmButton = screen.getByText('confirm-modal-btn');

        // Verify checkboxes and buttons are in the same container (DialogActions)
        const dialogActions = cancelButton.closest('[role="dialog"]')?.querySelector('[class*="MuiDialogActions"]');
        expect(dialogActions).toContainElement(confirmCheckbox);
        expect(dialogActions).toContainElement(awareCheckbox);
        expect(dialogActions).toContainElement(cancelButton);
        expect(dialogActions).toContainElement(confirmButton);
      });
    });

    describe('Form Functionality in Confirm Mode', () => {
      it('handles form submission in confirm mode when checkboxes are checked', async () => {
        const user = userEvent.setup();
        render(
          <TestWrapper>
            <ReceiveHarvestModal {...confirmModeProps} initialWeights={{ '1-A': '100' }} />
          </TestWrapper>
        );

        const confirmCheckbox = screen.getByRole('checkbox', { name: /confirm-receive-harvest/ });
        const awareCheckbox = screen.getByRole('checkbox', { name: /aware-undo-action/ });
        const confirmButton = screen.getByText('confirm-modal-btn');

        // Check both checkboxes
        await user.click(confirmCheckbox);
        await user.click(awareCheckbox);

        await user.click(confirmButton);

        // The confirm button should be clickable and not throw errors
        await waitFor(() => {
          expect(confirmButton).toBeInTheDocument();
        });
      });

      it('resets checkboxes when modal reopens in confirm mode', async () => {
        const user = userEvent.setup();
        const { rerender } = render(
          <TestWrapper>
            <ReceiveHarvestModal {...confirmModeProps} open={false} />
          </TestWrapper>
        );

        rerender(
          <TestWrapper>
            <ReceiveHarvestModal {...confirmModeProps} open={true} initialWeights={{ '1-A': '150' }} />
          </TestWrapper>
        );

        // Select variety to see the inputs
        const varietyItem = screen.getByText('Variety 1');
        await user.click(varietyItem);

        // Form should render with new props and checkboxes should be reset
        await waitFor(() => {
          expect(screen.getByText('Grade A')).toBeInTheDocument();
          expect(screen.getByText('Grade B')).toBeInTheDocument();

          const confirmCheckbox = screen.getByRole('checkbox', { name: /confirm-receive-harvest/ });
          const awareCheckbox = screen.getByRole('checkbox', { name: /aware-undo-action/ });
          expect(confirmCheckbox).not.toBeChecked();
          expect(awareCheckbox).not.toBeChecked();
        });
      });
    });
  });

  describe('Shared Functionality (Both Modes)', () => {
    describe('User Interactions', () => {
      it('calls onClose when close button is clicked in edit mode', async () => {
        const user = userEvent.setup();
        render(
          <TestWrapper>
            <ReceiveHarvestModal {...defaultProps} isEditReceiving={true} />
          </TestWrapper>
        );

        const closeButton = screen.getByLabelText('close');
        await user.click(closeButton);

        expect(mockOnClose).toHaveBeenCalled();
      });

      it('calls onClose when close button is clicked in confirm mode', async () => {
        const user = userEvent.setup();
        render(
          <TestWrapper>
            <ReceiveHarvestModal {...defaultProps} isEditReceiving={false} />
          </TestWrapper>
        );

        const closeButton = screen.getByLabelText('close');
        await user.click(closeButton);

        expect(mockOnClose).toHaveBeenCalled();
      });

      it('calls onClose when cancel button is clicked in edit mode', async () => {
        const user = userEvent.setup();
        render(
          <TestWrapper>
            <ReceiveHarvestModal {...defaultProps} isEditReceiving={true} />
          </TestWrapper>
        );

        const cancelButton = screen.getByText('cancel-modal-btn');
        await user.click(cancelButton);

        expect(mockOnClose).toHaveBeenCalled();
      });

      it('calls onClose when cancel button is clicked in confirm mode', async () => {
        const user = userEvent.setup();
        render(
          <TestWrapper>
            <ReceiveHarvestModal {...defaultProps} isEditReceiving={false} />
          </TestWrapper>
        );

        const cancelButton = screen.getByText('cancel-modal-btn');
        await user.click(cancelButton);

        expect(mockOnClose).toHaveBeenCalled();
      });
    });

    describe('Varieties List', () => {
      it('renders varieties list in both modes', () => {
        const { rerender } = render(
          <TestWrapper>
            <ReceiveHarvestModal {...defaultProps} isEditReceiving={true} />
          </TestWrapper>
        );

        expect(screen.getByText('Variety 1')).toBeInTheDocument();
        expect(screen.getByText('Variety 2')).toBeInTheDocument();
        expect(screen.getByText('Variety 3')).toBeInTheDocument();

        rerender(
          <TestWrapper>
            <ReceiveHarvestModal {...defaultProps} isEditReceiving={false} />
          </TestWrapper>
        );

        expect(screen.getByText('Variety 1')).toBeInTheDocument();
        expect(screen.getByText('Variety 2')).toBeInTheDocument();
        expect(screen.getByText('Variety 3')).toBeInTheDocument();
      });

      it('selects variety when clicked in both modes', async () => {
        const user = userEvent.setup();
        const { rerender } = render(
          <TestWrapper>
            <ReceiveHarvestModal {...defaultProps} isEditReceiving={true} />
          </TestWrapper>
        );

        const varietyItem = screen.getByText('Variety 1');
        await user.click(varietyItem);

        // Check if the variety is selected by looking for grade inputs
        await waitFor(() => {
          expect(screen.getByText('Grade A')).toBeInTheDocument();
          expect(screen.getByText('Grade B')).toBeInTheDocument();
        });

        rerender(
          <TestWrapper>
            <ReceiveHarvestModal {...defaultProps} isEditReceiving={false} />
          </TestWrapper>
        );

        const varietyItem2 = screen.getByText('Variety 1');
        await user.click(varietyItem2);

        await waitFor(() => {
          expect(screen.getByText('Grade A')).toBeInTheDocument();
          expect(screen.getByText('Grade B')).toBeInTheDocument();
        });
      });
    });

    describe('Edge Cases', () => {
      it('handles empty varieties list in both modes', () => {
        const { rerender } = render(
          <TestWrapper>
            <ReceiveHarvestModal {...defaultProps} varieties={[]} isEditReceiving={true} />
          </TestWrapper>
        );

        expect(screen.getByText('varieties-update')).toBeInTheDocument();
        expect(screen.queryByText('Variety 1')).not.toBeInTheDocument();

        rerender(
          <TestWrapper>
            <ReceiveHarvestModal {...defaultProps} varieties={[]} isEditReceiving={false} />
          </TestWrapper>
        );

        expect(screen.getByText('confirm-harvest-data')).toBeInTheDocument();
        expect(screen.queryByText('Variety 1')).not.toBeInTheDocument();
      });

      it('handles missing onSave prop in both modes', async () => {
        const user = userEvent.setup();
        const { rerender } = render(
          <TestWrapper>
            <ReceiveHarvestModal {...defaultProps} onSave={undefined} initialWeights={{ '1-A': '100' }} isEditReceiving={true} />
          </TestWrapper>
        );

        const saveButton = screen.getByText('save-modal-btn');
        await user.click(saveButton);

        // Should not throw error when onSave is undefined
        await waitFor(() => {
          expect(saveButton).toBeInTheDocument();
        });

        rerender(
          <TestWrapper>
            <ReceiveHarvestModal {...defaultProps} onSave={undefined} initialWeights={{ '1-A': '100' }} isEditReceiving={false} />
          </TestWrapper>
        );

        const confirmCheckbox = screen.getByRole('checkbox', { name: /confirm-receive-harvest/ });
        const awareCheckbox = screen.getByRole('checkbox', { name: /aware-undo-action/ });
        const confirmButton = screen.getByText('confirm-modal-btn');

        // Check both checkboxes
        await user.click(confirmCheckbox);
        await user.click(awareCheckbox);

        await user.click(confirmButton);

        // Should not throw error when onSave is undefined
        await waitFor(() => {
          expect(confirmButton).toBeInTheDocument();
        });
      });
    });
  });
});
