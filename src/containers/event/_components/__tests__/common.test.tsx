/* eslint-disable @next/next/no-img-element */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { render, screen } from '@testing-library/react';
import dayjs from 'dayjs';

import {
  renderTotalWeight,
  renderFarmName,
  renderRecordBy,
  renderRecordByInDetail,
  renderRecordByInDetailContent,
  renderRecordOn,
  renderUpdatedOn,
  renderUpdatedBy,
  formatDateWithLocale,
  renderStatus,
  renderImageContent,
} from '../common';
import { EventStatusEnum, EventType, Plot, RecordedByEnum } from 'types';

// Mock dependencies
jest.mock('utils', () => ({
  formatNumberWithCommas: jest.fn((value) => value?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')),
  getImageUrl: jest.fn((url) => url ? `https://example.com/${url}` : null),
  getStatusBgColor: jest.fn((status) => {
    if (status === 'waiting') return '#FFF8E1';
    if (status === 'rejected') return '#FFEBEE';
    if (status === 'draft') return '#F5F5F5';
    return '#E8F5E9';
  }),
  getStatusColor: jest.fn((status) => {
    if (status === 'waiting') return '#F57F17';
    if (status === 'rejected') return '#D32F2F';
    if (status === 'draft') return '#616161';
    return '#2E7D32';
  }),
  getStatusBorderColor: jest.fn((status) => {
    if (status === 'waiting') return '#FFB300';
    if (status === 'rejected') return '#D32F2F';
    if (status === 'draft') return '#E0E0E0';
    return '#4CAF50';
  }),
}));

jest.mock('utils/cookie-client', () => ({
  getCookieLocale: jest.fn(() => 'th'),
}));

jest.mock('components', () => ({
  EllipsisTypography: ({ text }: { text: string }) => <span data-testid="ellipsis-typography">{text}</span>,
  ImageReviewModal: ({ imageUrl }: { imageUrl: string | null }) => (
    <div data-testid="image-review-modal">{imageUrl}</div>
  ),
}));

jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ src, alt, width, height }: { src: string; alt: string; width: number; height: number }) => (
    <img src={src} alt={alt} width={width} height={height} data-testid="next-image" />
  ),
}));

jest.mock('assets/icons/user.svg', () => 'user-icon.svg');
jest.mock('assets/icons/knife.svg', () => 'knife-icon.svg');
jest.mock('assets/icons/box.svg', () => 'box-icon.svg');

jest.mock('styles/colors', () => ({
  colors: {
    recordByPackingHouse: '#16A34A',
    recordByCutter: '#2266D9',
    recordByFarmer: '#16A34A',
  },
}));

// Mock constants
jest.mock('components/date-picker/custom-date-picker', () => ({
  DIFFERENCE_THAI_YEAR: 543,
}));

jest.mock('constant/common', () => ({
  DD_MMMM_YYYY_WITH_DASH: 'DD/MM/YYYY',
  FORMAT_DATE_DD_MMM_YYYY: 'DD MMM YYYY',
}));

describe('common.tsx functions', () => {
  // Mock data
  const mockPackingHouse = {
    id: '1',
    eventStatus: 'waiting',
    eventType: 'receiving',
    name: 'Test Packing House',
    description: 'Test Description',
    dateCreated: '2023-12-25T10:00:00Z',
    dateUpdated: '2023-12-26T10:00:00Z',
    eventTimeUnix: '1703505600',
    eventTime: '2023-12-25T10:00:00Z',
    address: 'Test Address',
    country: 'Thailand',
    images: [],
    logo: { id: '1', filenameDisk: 'logo.jpg', filenameDownload: 'logo.jpg' },
    gln: '1234567890123',
    productId: 'prod-1',
    status: 'waiting',
    type: 'receiving',
    positionLongitude: 100.5,
    positionLatitude: 13.7,
    farm: {
      address: 'Farm Address',
      gln: '9876543210987',
      id: 'farm-1',
      name: 'Test Farm',
      gap: 'GAP123',
    },
    role: 'packing_house_staff',
    varieties: [
      {
        id: 'variety-1',
        grades: [
          { id: 'grade-1', weight: 100, value: 'A', label: { th: 'เกรด A', en: 'Grade A' } },
          { id: 'grade-2', weight: 200, value: 'B', label: { th: 'เกรด B', en: 'Grade B' } },
        ],
      },
      {
        id: 'variety-2',
        grades: [
          { id: 'grade-3', weight: 150, value: 'C', label: { th: 'เกรด C', en: 'Grade C' } },
        ],
      },
    ],
    userCreated: {
      id: 'user-1',
      firstName: 'John',
      lastName: 'Doe',
      role: 'farmer',
      roleLabel: {
        en: 'Farmer',
        th: 'เกษตรกร',
      },
      profile: {
        nickname: 'Johnny',
      },
    },
    userUpdated: {
      id: 'user-2',
      firstName: 'Jane',
      lastName: 'Smith',
    },
    originUserCreated: {
      id: 'user-3',
      firstName: 'Bob',
      lastName: 'Wilson',
      role: 'cutter',
      roleLabel: {
        en: 'Cutter',
        th: 'คนตัด',
      },
      profile: {
        nickname: 'Bobby',
      },
    },
  } as any;

  const mockGridParams = {
    row: mockPackingHouse,
    id: '1',
    field: 'test',
    value: 'test',
    formattedValue: 'test',
    api: {} as any,
    hasFocus: false,
    isEditable: false,
    cellMode: 'view',
    colDef: {} as any,
    rowNode: {} as any,
    tabIndex: 0,
  } as any;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('renderTotalWeight', () => {
    it('should render total weight correctly with varieties and grades', () => {
      const result = renderTotalWeight(mockGridParams);
      render(result);

      // Total weight should be 100 + 200 + 150 = 450
      expect(screen.getByText('450')).toBeInTheDocument();
    });

    it('should render 0 when no varieties', () => {
      const paramsWithoutVarieties = {
        ...mockGridParams,
        row: { ...mockPackingHouse, varieties: [] },
      };

      const result = renderTotalWeight(paramsWithoutVarieties);
      render(result);

      expect(screen.getByText('0')).toBeInTheDocument();
    });

    it('should render 0 when varieties is null', () => {
      const paramsWithNullVarieties = {
        ...mockGridParams,
        row: { ...mockPackingHouse, varieties: null as any },
      };

      const result = renderTotalWeight(paramsWithNullVarieties);
      render(result);

      expect(screen.getByText('0')).toBeInTheDocument();
    });
  });

  describe('renderFarmName', () => {
    it('should render farm name correctly', () => {
      const result = renderFarmName(mockGridParams);
      render(result);

      expect(screen.getByText('Test Farm')).toBeInTheDocument();
    });

    it('should render default value when farm name is empty', () => {
      const paramsWithoutFarmName = {
        ...mockGridParams,
        row: { ...mockPackingHouse, farm: { ...mockPackingHouse.farm, name: '' } },
      };

      const result = renderFarmName(paramsWithoutFarmName);
      render(result);

      expect(screen.getByText('--')).toBeInTheDocument();
    });

    it('should render default value when farm name is null', () => {
      const paramsWithNullFarmName = {
        ...mockGridParams,
        row: { ...mockPackingHouse, farm: { ...mockPackingHouse.farm, name: null as any } },
      };

      const result = renderFarmName(paramsWithNullFarmName);
      render(result);

      expect(screen.getByText('--')).toBeInTheDocument();
    });
  });

  describe('renderRecordBy', () => {
    it('should render record by with farmer role and nickname for non-receiving type', () => {
      const nonReceivingParams = {
        ...mockGridParams,
        row: { ...mockPackingHouse, type: 'harvesting' as EventType },
      };

      const result = renderRecordBy(nonReceivingParams);
      render(result);

      expect(screen.getByText('Johnny / John Doe')).toBeInTheDocument();
      expect(screen.getByTestId('next-image')).toBeInTheDocument();
      expect(screen.getByTestId('next-image')).toHaveAttribute('alt', 'farmer');
    });

    it('should render record by for receiving type with originUserCreated', () => {
      // For receiving type, it uses originUserCreated
      const result = renderRecordBy(mockGridParams);
      render(result);

      expect(screen.getByText('Bobby / Bob Wilson')).toBeInTheDocument();
      expect(screen.getByTestId('next-image')).toBeInTheDocument();
      expect(screen.getByTestId('next-image')).toHaveAttribute('alt', 'cutter');
    });

    it('should render record by without nickname for receiving type', () => {
      const paramsWithoutNickname = {
        ...mockGridParams,
        row: {
          ...mockPackingHouse,
          originUserCreated: {
            ...mockPackingHouse.originUserCreated,
            profile: { nickname: '' },
          },
        },
      };

      const result = renderRecordBy(paramsWithoutNickname);
      render(result);

      expect(screen.getByText('Bob Wilson')).toBeInTheDocument();
    });

    it('should render default value when no originUserCreated for receiving type', () => {
      const paramsWithoutUser = {
        ...mockGridParams,
        row: { ...mockPackingHouse, originUserCreated: null as any },
      };

      const result = renderRecordBy(paramsWithoutUser);
      render(result);

      expect(screen.getByText('--')).toBeInTheDocument();
    });

    it('should render packing house icon for packing house role', () => {
      const packingHouseParams = {
        ...mockGridParams,
        row: {
          ...mockPackingHouse,
          originUserCreated: {
            ...mockPackingHouse.originUserCreated,
            role: RecordedByEnum.PACKING_HOUSE,
          },
        },
      };

      const result = renderRecordBy(packingHouseParams);
      render(result);

      expect(screen.getByTestId('next-image')).toBeInTheDocument();
      expect(screen.getByTestId('next-image')).toHaveAttribute('alt', 'packing_house_staff');
    });

    it('should render farmer icon for farmer role in non-receiving type', () => {
      const farmerParams = {
        ...mockGridParams,
        row: {
          ...mockPackingHouse,
          type: 'harvesting' as EventType,
          userCreated: {
            ...mockPackingHouse.userCreated,
            role: RecordedByEnum.FARMER,
          },
        },
      };

      const result = renderRecordBy(farmerParams);
      render(result);

      expect(screen.getByTestId('next-image')).toBeInTheDocument();
      expect(screen.getByTestId('next-image')).toHaveAttribute('alt', 'farmer');
    });

    it('should handle unknown role without icon', () => {
      const unknownRoleParams = {
        ...mockGridParams,
        row: {
          ...mockPackingHouse,
          type: 'harvesting' as EventType,
          userCreated: {
            ...mockPackingHouse.userCreated,
            role: 'unknown_role' as RecordedByEnum,
          },
        },
      };

      const result = renderRecordBy(unknownRoleParams);
      render(result);

      expect(screen.getByText('Johnny / John Doe')).toBeInTheDocument();
      expect(screen.queryByTestId('next-image')).not.toBeInTheDocument();
    });

    it('should handle null firstName and lastName in userCreated', () => {
      const nullNamesParams = {
        ...mockGridParams,
        row: {
          ...mockPackingHouse,
          type: 'harvesting' as EventType,
          userCreated: {
            ...mockPackingHouse.userCreated,
            firstName: null,
            lastName: null,
            profile: undefined,
          },
        },
      };

      const result = renderRecordBy(nullNamesParams);
      render(result);

      expect(screen.getByTestId('ellipsis-typography')).toHaveTextContent('');
    });
  });

  describe('renderRecordByInDetailContent', () => {
    it('should render farmer role with icon and color', () => {
      const result = renderRecordByInDetailContent(RecordedByEnum.FARMER, 'John', 'Doe', '');
      render(result);

      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByTestId('next-image')).toBeInTheDocument();
      expect(screen.getByTestId('next-image')).toHaveAttribute('alt', 'farmer');
    });

    it('should render cutter role with icon and color', () => {
      const result = renderRecordByInDetailContent(RecordedByEnum.CUTTER, 'Bob', 'Wilson', '');
      render(result);

      expect(screen.getByText('Bob Wilson')).toBeInTheDocument();
      expect(screen.getByTestId('next-image')).toBeInTheDocument();
      expect(screen.getByTestId('next-image')).toHaveAttribute('alt', 'cutter');
    });

    it('should render packing house role with icon and color', () => {
      const result = renderRecordByInDetailContent(RecordedByEnum.PACKING_HOUSE, 'Jane', 'Smith', '');
      render(result);

      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      expect(screen.getByTestId('next-image')).toBeInTheDocument();
      expect(screen.getByTestId('next-image')).toHaveAttribute('alt', 'packing_house_staff');
    });

    it('should render with nickname when provided', () => {
      const result = renderRecordByInDetailContent(RecordedByEnum.FARMER, 'John', 'Doe', 'Johnny');
      render(result);

      expect(screen.getByText('Johnny / John Doe')).toBeInTheDocument();
    });

    it('should handle empty nickname', () => {
      const result = renderRecordByInDetailContent(RecordedByEnum.FARMER, 'John', 'Doe', '');
      render(result);

      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    it('should handle default role with no icon', () => {
      const result = renderRecordByInDetailContent('unknown' as RecordedByEnum, 'Test', 'User', '');
      render(result);

      expect(screen.getByText('Test User')).toBeInTheDocument();
      expect(screen.queryByTestId('next-image')).not.toBeInTheDocument();
    });
  });

  describe('renderRecordByInDetail', () => {
    const mockPackingHouseDetail = {
      id: '1',
      status: 'waiting',
      type: 'receiving',
      name: 'Test Detail',
      description: 'Test Description',
      address: 'Test Address',
      country: 'Thailand',
      dateCreated: '2023-12-25T10:00:00Z',
      images: [],
      logo: { id: '1', filenameDisk: 'logo.jpg', filenameDownload: 'logo.jpg' },
      farm: {
        address: 'Farm Address',
        id: 'farm-1',
        name: 'Test Farm',
      },
      gln: '1234567890123',
      positionLongitude: 100.5,
      positionLatitude: 13.7,
      role: 'packing_house_staff',
      batchlot: 'batch-123',
      packingHouse: mockPackingHouse,
      userCreated: {
        id: 'user-1',
        firstName: 'John',
        lastName: 'Doe',
        role: 'farmer',
        roleLabel: {
          en: 'Farmer',
          th: 'เกษตรกร',
        },
        profile: {
          nickname: 'Johnny',
        },
      },
      originUserCreated: {
        id: 'user-3',
        firstName: 'Bob',
        lastName: 'Wilson',
        role: 'cutter',
        roleLabel: {
          en: 'Cutter',
          th: 'คนตัด',
        },
        profile: {
          nickname: 'Bobby',
        },
      },
    } as any;

    it('should render record by in detail for non-receiving type', () => {
      const result = renderRecordByInDetail(false, mockPackingHouseDetail);
      render(result);

      expect(screen.getByText('Johnny / John Doe')).toBeInTheDocument();
      expect(screen.getByTestId('next-image')).toBeInTheDocument();
      expect(screen.getByTestId('next-image')).toHaveAttribute('alt', 'farmer');
    });

    it('should render record by in detail for receiving type with originUserCreated', () => {
      const result = renderRecordByInDetail(true, mockPackingHouseDetail);
      render(result);

      expect(screen.getByText('Bobby / Bob Wilson')).toBeInTheDocument();
      expect(screen.getByTestId('next-image')).toBeInTheDocument();
      expect(screen.getByTestId('next-image')).toHaveAttribute('alt', 'cutter');
    });

    it('should render default value for receiving type without originUserCreated', () => {
      const detailWithoutOriginUser = {
        ...mockPackingHouseDetail,
        originUserCreated: undefined,
      };

      const result = renderRecordByInDetail(true, detailWithoutOriginUser);
      expect(result).toBe('--');
    });

    it('should render without nickname when profile is not provided', () => {
      const detailWithoutNickname = {
        ...mockPackingHouseDetail,
        userCreated: {
          ...mockPackingHouseDetail.userCreated,
          profile: undefined,
        },
      };

      const result = renderRecordByInDetail(false, detailWithoutNickname);
      render(result);

      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });


  });

  describe('renderRecordOn', () => {
    it('should render record on date correctly', () => {
      const result = renderRecordOn(mockGridParams);
      render(result);

      // Should format the date using formatDateWithLocale
      expect(screen.getByText(/25/)).toBeInTheDocument();
    });

    it('should render default value when no dateCreated', () => {
      const paramsWithoutDate = {
        ...mockGridParams,
        row: { ...mockPackingHouse, dateCreated: '' },
      };

      const result = renderRecordOn(paramsWithoutDate);
      render(result);

      expect(screen.getByText('--')).toBeInTheDocument();
    });

    it('should render with custom format', () => {
      const result = renderRecordOn(mockGridParams, 'YYYY-MM-DD');
      render(result);

      // Should use the custom format
      expect(screen.getByText(/2566/)).toBeInTheDocument(); // Thai year
    });
  });

  describe('renderUpdatedOn', () => {
    it('should render updated on date correctly', () => {
      const result = renderUpdatedOn(mockGridParams);
      render(result);

      expect(screen.getByText(/26/)).toBeInTheDocument();
    });

    it('should render default value when no dateUpdated', () => {
      const paramsWithoutDate = {
        ...mockGridParams,
        row: { ...mockPackingHouse, dateUpdated: undefined },
      };

      const result = renderUpdatedOn(paramsWithoutDate);
      render(result);

      expect(screen.getByText('--')).toBeInTheDocument();
    });
  });

  describe('renderUpdatedBy', () => {
    it('should render updated by user correctly', () => {
      const result = renderUpdatedBy(mockGridParams);
      render(result);

      expect(screen.getByTestId('ellipsis-typography')).toHaveTextContent('Jane Smith');
    });

    it('should render default value when no userUpdated', () => {
      const paramsWithoutUser = {
        ...mockGridParams,
        row: { ...mockPackingHouse, userUpdated: undefined },
      };

      const result = renderUpdatedBy(paramsWithoutUser);
      render(result);

      expect(screen.getByTestId('ellipsis-typography')).toHaveTextContent('--');
    });

    it('should handle partial user data', () => {
      const paramsWithPartialUser = {
        ...mockGridParams,
        row: {
          ...mockPackingHouse,
          userUpdated: { id: 'user-2', firstName: 'Jane', lastName: '' },
        },
      };

      const result = renderUpdatedBy(paramsWithPartialUser);
      render(result);

      expect(screen.getByTestId('ellipsis-typography')).toHaveTextContent('Jane');
    });
  });

  describe('formatDateWithLocale', () => {
    it('should format date with Thai locale', () => {
      const result = formatDateWithLocale('2023-12-25T10:00:00Z');
      expect(result).toContain('2566'); // Thai year (2023 + 543)
    });

    it('should format date with English locale', () => {
      // eslint-disable-next-line @typescript-eslint/no-require-imports
      const mockGetCookieLocale = require('utils/cookie-client').getCookieLocale;
      mockGetCookieLocale.mockReturnValue('en');

      const result = formatDateWithLocale('2023-12-25T10:00:00Z');
      expect(result).toContain('2023'); // English year

      // Reset mock
      mockGetCookieLocale.mockReturnValue('th');
    });

    it('should return empty string for invalid date', () => {
      const result = formatDateWithLocale('');
      expect(result).toBe('');
    });

    it('should handle dayjs object input', () => {
      const dayjsDate = dayjs('2023-12-25T10:00:00Z');
      const result = formatDateWithLocale(dayjsDate);
      expect(result).toContain('2566'); // Thai year
    });

    it('should handle number input (timestamp)', () => {
      const timestamp = 1703505600000; // 2023-12-25T10:00:00Z
      const result = formatDateWithLocale(timestamp);
      expect(result).toContain('2566'); // Thai year
    });

    it('should use custom format', () => {
      const result = formatDateWithLocale('2023-12-25T10:00:00Z', 'YYYY-MM-DD');
      expect(result).toBe('2566-12-25');
    });

    it('should replace BBBB with YYYY for Thai locale', () => {
      const result = formatDateWithLocale('2023-12-25T10:00:00Z', 'DD MMM BBBB');
      expect(result).toContain('25 Dec BBBB');
    });
  });

  describe('renderStatus', () => {
    it('should render status chip with correct styling for WAITING status', () => {
      const result = renderStatus(EventStatusEnum.WAITING, 'Waiting');
      render(result);

      const chip = screen.getByText('Waiting');
      expect(chip).toBeInTheDocument();
      expect(chip).toHaveClass('MuiChip-label');
    });

    it('should render status chip with correct styling for REJECTED status', () => {
      const result = renderStatus(EventStatusEnum.REJECTED, 'Rejected');
      render(result);

      const chip = screen.getByText('Rejected');
      expect(chip).toBeInTheDocument();
    });

    it('should render status chip with correct styling for DRAFT status', () => {
      const result = renderStatus(EventStatusEnum.DRAFT, 'Draft');
      render(result);

      const chip = screen.getByText('Draft');
      expect(chip).toBeInTheDocument();
    });

    it('should render status chip with correct styling for RECEIVED status', () => {
      const result = renderStatus(EventStatusEnum.RECEIVED, 'Received');
      render(result);

      const chip = screen.getByText('Received');
      expect(chip).toBeInTheDocument();
    });

    it('should render status chip with correct styling for SEALED status', () => {
      const result = renderStatus(EventStatusEnum.SEALED, 'Sealed');
      render(result);

      const chip = screen.getByText('Sealed');
      expect(chip).toBeInTheDocument();
    });
  });

  describe('renderImageContent', () => {
    const mockPlot: Plot = {
      id: 'plot-1',
      gap: 'GAP123',
      area: 100,
      areaUnit: 'rai',
      name: 'Test Plot',
      plotId: 'plot-123',
      image: 'test-image.jpg',
    };

    const mockRemoveImageFromPlot = jest.fn();

    beforeEach(() => {
      mockRemoveImageFromPlot.mockClear();
    });

    it('should render image content with ImageReviewModal and remove button', () => {
      const result = renderImageContent(mockPlot, mockRemoveImageFromPlot);
      render(result);

      expect(screen.getByTestId('image-review-modal')).toBeInTheDocument();
      expect(screen.getByTestId('image-review-modal')).toHaveTextContent('https://example.com/test-image.jpg');

      const removeButton = screen.getByRole('button');
      expect(removeButton).toBeInTheDocument();
    });

    it('should call removeImageFromPlot when remove button is clicked', () => {
      const result = renderImageContent(mockPlot, mockRemoveImageFromPlot);
      render(result);

      const removeButton = screen.getByRole('button');
      removeButton.click();

      expect(mockRemoveImageFromPlot).toHaveBeenCalledWith('plot-1');
    });

    it('should render with null image', () => {
      const plotWithoutImage = { ...mockPlot, image: undefined };
      const result = renderImageContent(plotWithoutImage, mockRemoveImageFromPlot);
      render(result);

      expect(screen.getByTestId('image-review-modal')).toBeInTheDocument();
      expect(screen.getByTestId('image-review-modal')).toHaveTextContent('');
    });
  });

  // Integration tests
  describe('Integration tests', () => {
    it('should work with real dayjs formatting', () => {
      // Test that dayjs formatting works as expected
      const testDate = '2023-12-25T10:00:00Z';
      const result = formatDateWithLocale(testDate, 'DD/MM/YYYY');

      expect(result).toBe('25/12/2566'); // Thai year
    });

    it('should handle edge cases in renderTotalWeight', () => {
      const edgeCaseParams = {
        ...mockGridParams,
        row: {
          ...mockPackingHouse,
          varieties: [
            {
              id: 'variety-1',
              grades: [
                { id: 'grade-1', weight: 0 },
                { id: 'grade-2', weight: 0.5 },
              ],
            },
          ],
        },
      };

      const result = renderTotalWeight(edgeCaseParams);
      render(result);

      expect(screen.getByText('0.5')).toBeInTheDocument();
    });

    it('should handle complex user names in renderRecordBy for non-receiving type', () => {
      const complexUserParams = {
        ...mockGridParams,
        row: {
          ...mockPackingHouse,
          type: 'harvesting' as EventType,
          userCreated: {
            ...mockPackingHouse.userCreated,
            firstName: 'Jean-Claude',
            lastName: 'Van Damme',
            profile: { nickname: 'JCVD' },
          },
        },
      };

      const result = renderRecordBy(complexUserParams);
      render(result);

      expect(screen.getByText('JCVD / Jean-Claude Van Damme')).toBeInTheDocument();
    });
  });
});
