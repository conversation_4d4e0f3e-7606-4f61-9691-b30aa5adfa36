/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-require-imports */
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { TotalWeightInfoDrawer } from '../total-weight-info-drawer';
import { theme } from 'styles/theme';

// Mock dependencies
jest.mock('utils', () => ({
  formatNumberWithCommas: jest.fn((value) => value?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')),
}));

jest.mock('next-intl', () => ({
  useTranslations: jest.fn(() => (key: string) => {
    const translations: Record<string, string> = {
      'total-weight-calculation': 'Total Weight Calculation',
      total: 'Total',
      'close-modal-btn': 'Close',
      kg: 'kg',
    };
    return translations[key] || key;
  }),
}));

// Create mock functions that can be tracked
const mockGetGradeLabel = jest.fn((id: string) => `Grade ${id}`);
const mockGetVarietyLabel = jest.fn((id: string) => `Variety ${id}`);

jest.mock('store/useMaterDataStore', () => ({
  useMasterDataStore: jest.fn(() => ({
    getGradeLabel: mockGetGradeLabel,
    getVarietyLabel: mockGetVarietyLabel,
  })),
}));

jest.mock('components', () => ({
  Drawer: ({ children, open, onClose, drawerTitle, hasActionBtn, footerElement }: any) => (
    <div data-testid="drawer" data-open={open} data-title={drawerTitle} data-has-action-btn={hasActionBtn}>
      {open && (
        <div>
          <div data-testid="drawer-title">{drawerTitle}</div>
          <div data-testid="drawer-content">{children}</div>
          <button data-testid="drawer-close" onClick={onClose}>
            Close Drawer
          </button>
          {footerElement && <div data-testid="drawer-footer">{footerElement}</div>}
        </div>
      )}
    </div>
  ),
  DetailRow: ({ title, content, noBorder }: any) => (
    <div data-testid="detail-row" data-no-border={noBorder}>
      <div data-testid="detail-row-title">{title}</div>
      <div data-testid="detail-row-content">{content}</div>
    </div>
  ),
}));

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider theme={theme}>{children}</ThemeProvider>
);

const mockContent = [
  {
    id: 'variety-1',
    name: 'Custom Variety Name',
    grades: [
      { id: 'grade-1', weight: 1000 },
      { id: 'grade-2', weight: 2500 },
    ],
  },
  {
    id: 'variety-2',
    grades: [
      { id: 'grade-3', weight: 1500 },
      { id: 'grade-4', weight: 3000 },
    ],
  },
];

describe('TotalWeightInfoDrawer', () => {
  const defaultProps = {
    open: true,
    toggle: jest.fn(),
    content: mockContent,
    total: 8000,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockGetGradeLabel.mockClear();
    mockGetVarietyLabel.mockClear();
  });

  it('renders correctly when open', () => {
    render(
      <TestWrapper>
        <TotalWeightInfoDrawer {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByTestId('drawer')).toBeInTheDocument();
    expect(screen.getByTestId('drawer-title')).toHaveTextContent('Total Weight Calculation');
    expect(screen.getByTestId('drawer')).toHaveAttribute('data-open', 'true');
  });

  it('does not render when closed', () => {
    render(
      <TestWrapper>
        <TotalWeightInfoDrawer {...defaultProps} open={false} />
      </TestWrapper>
    );

    expect(screen.getByTestId('drawer')).toHaveAttribute('data-open', 'false');
    expect(screen.queryByTestId('drawer-content')).not.toBeInTheDocument();
  });

  it('displays content with custom variety names', () => {
    render(
      <TestWrapper>
        <TotalWeightInfoDrawer {...defaultProps} />
      </TestWrapper>
    );

    // Check if custom variety name is displayed
    expect(screen.getByText('Custom Variety Name')).toBeInTheDocument();

    // Check if grades are displayed with formatted weights
    expect(screen.getByText('Grade grade-1')).toBeInTheDocument();
    expect(screen.getByText('1,000 kg')).toBeInTheDocument();
    expect(screen.getByText('Grade grade-2')).toBeInTheDocument();
    expect(screen.getByText('2,500 kg')).toBeInTheDocument();
  });

  it('displays content with variety labels from store when name is not provided', () => {
    render(
      <TestWrapper>
        <TotalWeightInfoDrawer {...defaultProps} />
      </TestWrapper>
    );

    // Check if variety label from store is displayed for variety-2 (no custom name)
    expect(screen.getByText('Variety variety-2')).toBeInTheDocument();

    // Check if grades are displayed
    expect(screen.getByText('Grade grade-3')).toBeInTheDocument();
    expect(screen.getByText('1,500 kg')).toBeInTheDocument();
    expect(screen.getByText('Grade grade-4')).toBeInTheDocument();
    expect(screen.getByText('3,000 kg')).toBeInTheDocument();
  });

  it('displays total weight correctly', () => {
    render(
      <TestWrapper>
        <TotalWeightInfoDrawer {...defaultProps} />
      </TestWrapper>
    );

    const totalDetailRows = screen.getAllByTestId('detail-row');
    const totalRow = totalDetailRows.find(
      (row) => row.querySelector('[data-testid="detail-row-content"]')?.textContent === '8,000 kg'
    );

    expect(totalRow).toBeInTheDocument();
    expect(totalRow?.querySelector('[data-testid="detail-row-title"]')).toHaveTextContent('Total');
  });

  it('handles empty content gracefully', () => {
    render(
      <TestWrapper>
        <TotalWeightInfoDrawer {...defaultProps} content={[]} />
      </TestWrapper>
    );

    // Should still show total
    expect(screen.getByText('8,000 kg')).toBeInTheDocument();
    expect(screen.getByText('Total')).toBeInTheDocument();
  });

  it('handles undefined content gracefully', () => {
    render(
      <TestWrapper>
        <TotalWeightInfoDrawer {...defaultProps} content={undefined} />
      </TestWrapper>
    );

    // Should still show total
    expect(screen.getByText('8,000 kg')).toBeInTheDocument();
    expect(screen.getByText('Total')).toBeInTheDocument();
  });

  it('handles undefined total gracefully', () => {
    render(
      <TestWrapper>
        <TotalWeightInfoDrawer {...defaultProps} total={undefined} />
      </TestWrapper>
    );

    // Should show "undefined kg" when total is undefined
    expect(screen.getByText('undefined kg')).toBeInTheDocument();
  });

  it('calls toggle function when close button is clicked', () => {
    const mockToggle = jest.fn();

    render(
      <TestWrapper>
        <TotalWeightInfoDrawer {...defaultProps} toggle={mockToggle} />
      </TestWrapper>
    );

    const closeButton = screen.getByTestId('drawer-close');
    fireEvent.click(closeButton);

    expect(mockToggle).toHaveBeenCalledWith(false);
  });

  it('calls toggle function when footer close button is clicked', () => {
    const mockToggle = jest.fn();

    render(
      <TestWrapper>
        <TotalWeightInfoDrawer {...defaultProps} toggle={mockToggle} />
      </TestWrapper>
    );

    // The footer contains a close button
    const footerCloseButton = screen.getByText('Close');
    fireEvent.click(footerCloseButton);

    expect(mockToggle).toHaveBeenCalledWith(false);
  });

  it('passes correct props to Drawer component', () => {
    render(
      <TestWrapper>
        <TotalWeightInfoDrawer {...defaultProps} />
      </TestWrapper>
    );

    const drawer = screen.getByTestId('drawer');
    expect(drawer).toHaveAttribute('data-title', 'Total Weight Calculation');
    expect(drawer).toHaveAttribute('data-has-action-btn', 'false');
    expect(drawer).toHaveAttribute('data-open', 'true');
  });

  it('renders DetailRow components with correct props', () => {
    render(
      <TestWrapper>
        <TotalWeightInfoDrawer {...defaultProps} />
      </TestWrapper>
    );

    const detailRows = screen.getAllByTestId('detail-row');

    // Check that detail rows have noBorder prop set to true
    detailRows.forEach((row) => {
      expect(row).toHaveAttribute('data-no-border', 'true');
    });
  });

  it('formats numbers correctly using formatNumberWithCommas', () => {
    const { formatNumberWithCommas } = require('utils');

    render(
      <TestWrapper>
        <TotalWeightInfoDrawer {...defaultProps} />
      </TestWrapper>
    );

    // Verify that formatNumberWithCommas was called with the correct values
    expect(formatNumberWithCommas).toHaveBeenCalledWith(1000);
    expect(formatNumberWithCommas).toHaveBeenCalledWith(2500);
    expect(formatNumberWithCommas).toHaveBeenCalledWith(1500);
    expect(formatNumberWithCommas).toHaveBeenCalledWith(3000);
    expect(formatNumberWithCommas).toHaveBeenCalledWith(8000);
  });

  it('uses correct translation keys', () => {
    const { useTranslations } = require('next-intl');

    render(
      <TestWrapper>
        <TotalWeightInfoDrawer {...defaultProps} />
      </TestWrapper>
    );

    // Verify that useTranslations was called with correct namespaces
    expect(useTranslations).toHaveBeenCalledWith('common');
    expect(useTranslations).toHaveBeenCalledWith('receive');
  });

  it('uses master data store functions correctly', () => {
    render(
      <TestWrapper>
        <TotalWeightInfoDrawer {...defaultProps} />
      </TestWrapper>
    );

    // Verify that store functions were called with correct IDs
    expect(mockGetVarietyLabel).toHaveBeenCalledWith('variety-2');
    expect(mockGetGradeLabel).toHaveBeenCalledWith('grade-1');
    expect(mockGetGradeLabel).toHaveBeenCalledWith('grade-2');
    expect(mockGetGradeLabel).toHaveBeenCalledWith('grade-3');
    expect(mockGetGradeLabel).toHaveBeenCalledWith('grade-4');
  });
});
