import { Avatar, Box, Typography } from '@mui/material';
import { DetailRow } from 'components';
import { Cutter } from 'types';
import { FC } from 'react';
import { useTranslations } from 'next-intl';
import { getImageUrl } from 'utils';

interface ExistedCutterProps {
  cutter: Cutter;
}

export const ExistedCutter: FC<ExistedCutterProps> = ({ cutter }) => {
  const receivingTranslation = useTranslations('receive');

 const formatPhoneNumber = (phoneNumber: string): string => {
    return phoneNumber.startsWith('0') ? `${phoneNumber}` : `0${phoneNumber}`;
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        mt: 3,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flex: 1,
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          gap: 2,
        }}
      >
        <Avatar
          sx={{ width: 180, height: 180, objectFit: 'cover' }}
          src={getImageUrl(cutter?.avatar?.filenameDisk) ?? ''}
          alt="user-image"
        />
        <Typography textAlign="center" sx={{ fontSize: '16px' }}>
          {receivingTranslation('support-line')}
        </Typography>
      </Box>
      <Box>
        <Typography fontWeight="bold" sx={{ fontSize: '18px', mb: { xs: 2, sm: 0 } }}>
          {receivingTranslation('general-information')}
        </Typography>
        <DetailRow
          title={receivingTranslation('cutter-phone-number')}
          content={cutter.phoneNumber ? formatPhoneNumber(String(cutter.phoneNumber)) : null}
          sx={{ flexDirection: 'row' }}
        />
        <DetailRow
          title={receivingTranslation('cutter-name')}
          content={cutter.name ?? '--'}
          sx={{ flexDirection: 'row' }}
        />
        <DetailRow
          title={receivingTranslation('have-license')}
          content={
            cutter?.isCertified
              ? receivingTranslation('registered-cutter')
              : receivingTranslation('unregistered-cutter')
          }
          sx={{ flexDirection: 'row' }}
        />
      </Box>
    </Box>
  );
};
