import { Avatar, Box, Button, InputAdornment, LinearProgress, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';
import { FC, useMemo, useState } from 'react';
import SearchIcon from '@mui/icons-material/Search';
import { DebounceSearchInput, Drawer } from 'components';
import { theme } from 'styles/theme';
import { Cutter, CutterVehicle } from 'types';
import { CreateCutter } from './create-cutter';
import { ExistedCutter } from './exist-cutter';
import { CreateCutterVehicle } from './create-cutter-vehicle';
import { useQuery } from '@tanstack/react-query';
import { fetchCutterService } from 'services/resource.service';
import emptySearchIcon from 'assets/icons/empty-search.svg';
import Image from 'next/image';
import { getImageUrl } from 'utils';

interface CreateCutterDrawerProps {
  open: boolean;
  toggle: () => void;
  onAddCutter: (cutter: Cutter, cutterVehicle: CutterVehicle) => void;
}

const initialCutter: Cutter = {
  id: '',
  name: '',
  licenseNumber: '',
  isCertified: false,
  profileId: null,
  avatar: {
    id: '',
    filenameDisk: '',
    filenameDownload: '',
  },
  existingCutterId: null,
  existingCutterProfileId: null,
  vehicleNumber: null,
  provinceVehicleCode: null,
  vehicleImage: null,
};

const initialCutterVehicle: CutterVehicle = {
  image: {
    id: '',
    filenameDisk: '',
    filenameDownload: '',
  },
  provinceRegistrationNumber: '',
  vehicleRegistrationNumber: '',
};

export const CreateCutterDrawer: FC<CreateCutterDrawerProps> = ({ open, toggle, onAddCutter }) => {
  const [searchKeyword, setSearchKeyword] = useState('');
  const commonT = useTranslations('common');
  const receivingTranslation = useTranslations('receive');

  const [mode, setMode] = useState<'list' | 'detail' | 'create'>('list');

  const [selectedCutter, setSelectedCutter] = useState<Cutter>(initialCutter);
  const [selectedCutterVehicle, setSelectedCutterVehicle] = useState<CutterVehicle>(initialCutterVehicle);
  const [hasPhoneNumberError, setHasPhoneNumberError] = useState<boolean>(false);

  const {
    data: cutterResponse,
    isLoading: isLoadingCutter,
    isFetching: isFetchingCutter,
  } = useQuery({
    queryKey: ['cutters', searchKeyword],
    queryFn: () => {
      return fetchCutterService(searchKeyword);
    },
  });

  const handleCutterSelect = (cutter: Cutter) => {
    setSelectedCutter(cutter);
    setSelectedCutterVehicle((prev) => {
      return {
        ...prev,
        vehicleRegistrationNumber: cutter.vehicleNumber ?? '',
        provinceRegistrationNumber: cutter.provinceVehicleCode ?? '',
        image: {
          id: cutter.vehicleImage ?? '',
          filenameDisk: cutter.vehicleImage ?? '',
          filenameDownload: cutter.vehicleImage ?? '',
        },
      };
    });
    setMode('detail');
  };

  const handleBack = () => {
    setSelectedCutter(initialCutter);
    setSelectedCutterVehicle(initialCutterVehicle);
    setHasPhoneNumberError(false);
    setMode('list');
  };

  const onClose = () => {
    handleBack();
    toggle();
  };

  const onAdd = () => {
    if (enableSaveButton) {
      onAddCutter(selectedCutter, selectedCutterVehicle);
      onClose();
    }
  };

  const onCreateCutter = () => {
    setMode('create');
    setSelectedCutter(initialCutter);
    setSelectedCutterVehicle(initialCutterVehicle);
    setHasPhoneNumberError(false);
    setSearchKeyword('');
  };

  const drawerTitle = `${receivingTranslation('add-cutter-information')}`;

  const enableSaveButton = useMemo(() => {
    return Boolean(
      selectedCutter.name.trim() &&
        selectedCutter.phoneNumber &&
        !hasPhoneNumberError &&
        selectedCutterVehicle.vehicleRegistrationNumber.trim() &&
        selectedCutterVehicle.provinceRegistrationNumber
    );
  }, [selectedCutter, selectedCutterVehicle, hasPhoneNumberError]);

  const footerElement = (
    <Box
      sx={{
        px: 2,
        py: 1.5,
        borderTop: 1,
        borderColor: 'divider',
        display: 'flex',
        justifyContent: 'space-between',
        gap: 1,
      }}
    >
      <Button fullWidth variant="outlined" onClick={handleBack}>
        {commonT('back')}
      </Button>
      <Button fullWidth variant="contained" onClick={onAdd} disabled={!enableSaveButton}>
        {commonT('add-modal-btn')}
      </Button>
    </Box>
  );

  const listFooterElement = (
    <Box
      sx={{
        px: 2,
        py: 1.5,
        borderTop: 1,
        borderColor: 'divider',
        display: 'flex',
        justifyContent: 'space-between',
        gap: 1,
      }}
    >
      <Button fullWidth variant="outlined" onClick={onCreateCutter}>
        {receivingTranslation('add-walk-in-cutter')}
      </Button>
    </Box>
  );

  const renderList = () => {
    if (
      cutterResponse &&
      cutterResponse.data.length === 0 &&
      cutterResponse.data.filter(
        (cutter) => cutter.name && cutter.name.trim() !== '' && cutter.phoneNumber && cutter.phoneNumber.trim() !== ''
      ).length === 0
    ) {
      if (searchKeyword.trim() === '') return <></>;
      return (
        <Box
          sx={{
            width: '100%',
            display: 'flex',
            alignItems: 'center',
            flexDirection: 'column',
          }}
        >
          <Image src={emptySearchIcon} width={160} height={160} unoptimized alt="empty-state-icon" />
          <Typography my="12px" fontSize="16px" color="text.secondary">
            {receivingTranslation('no-result-found')}
          </Typography>
        </Box>
      );
    }
    return cutterResponse?.data
      .filter(
        (cutter) => cutter.name && cutter.name.trim() !== '' && cutter.phoneNumber && cutter.phoneNumber.trim() !== ''
      )
      .map((item: Cutter) => {
        return (
          <Box
            key={item.id}
            sx={{
              display: 'flex',
              alignItems: 'center',
              paddingX: 2,
              gap: 2,
              borderRadius: 2,
              ':hover': {
                background: theme.palette.customColors.primary50,
              },
            }}
            onClick={() => {
              handleCutterSelect(item);
            }}
          >
            <Avatar
              sx={{ width: 40, height: 40, objectFit: 'cover' }}
              src={getImageUrl(item?.avatar?.filenameDisk ?? '') ?? ''}
              alt="user-image"
            />
            <Box>
              <Typography>{item.name ? item.name : `${item.firstName} ${item.lastName}`}</Typography>
              <Typography>{item.phoneNumber?.startsWith('0') ? item.phoneNumber : `0${item.phoneNumber}`}</Typography>
            </Box>
          </Box>
        );
      });
  };

  const renderTitleSearch = useMemo(() => {
    if (searchKeyword) return receivingTranslation('search-result');
    if (cutterResponse && cutterResponse.data.length === 0) return '';
    return receivingTranslation('suggested-cutter');
  }, [searchKeyword, cutterResponse, receivingTranslation]);

  const listContent = (
    <>
      <DebounceSearchInput
        placeholder={receivingTranslation('search-cutter')}
        value={searchKeyword}
        onChange={setSearchKeyword}
        startAdornment={
          <InputAdornment position="start">
            <SearchIcon />
          </InputAdornment>
        }
        sx={{ height: '40px', width: '100%' }}
      />
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
        <Typography>{renderTitleSearch}</Typography>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          {isLoadingCutter && isFetchingCutter ? <LinearProgress /> : renderList()}
        </Box>
      </Box>
    </>
  );

  return (
    <Drawer
      anchor="right"
      drawerTitle={drawerTitle}
      open={open}
      onClose={onClose}
      hasActionBtn={false}
      footerElement={mode === 'list' ? listFooterElement : footerElement}
    >
      <Box
        component="div"
        sx={{ width: '100%', p: '24px 16px', display: 'flex', flexDirection: 'column', gap: '16px' }}
      >
        {mode === 'list' ? (
          listContent
        ) : (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {mode === 'create' ? (
              <CreateCutter
                cutter={selectedCutter}
                setCutter={(cutter: Cutter) => {
                  setSelectedCutter((prev) => ({
                    ...prev,
                    ...cutter,
                  }));
                }}
                onPhoneNumberErrorChange={setHasPhoneNumberError}
              />
            ) : (
              <ExistedCutter cutter={selectedCutter} />
            )}
            <CreateCutterVehicle
              vehicle={selectedCutterVehicle}
              setVehicle={(vehicle: CutterVehicle) => {
                setSelectedCutterVehicle((prev) => ({
                  ...prev,
                  ...vehicle,
                }));
              }}
            />
          </Box>
        )}
      </Box>
    </Drawer>
  );
};
