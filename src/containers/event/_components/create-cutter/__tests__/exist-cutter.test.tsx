/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * Unit tests for ExistedCutter component
 *
 * This test suite covers:
 * - Basic rendering with complete cutter data
 * - Handling of missing/null data (avatar, phone, name)
 * - Certification status display (certified/uncertified)
 * - Image URL generation and fallback behavior
 * - Component styling and structure
 * - Edge cases (long names, special characters)
 *
 * To run these tests:
 * - Single test file: yarn test --testPathPatterns=exist-cutter.test.tsx
 * - With coverage: yarn test --testPathPatterns=exist-cutter.test.tsx --coverage
 * - Watch mode: yarn test --testPathPatterns=exist-cutter.test.tsx --watch
 */

import { render, screen } from '@testing-library/react';
import { ExistedCutter } from '../exist-cutter';
import { Cutter } from 'types';

// Mock the utils module
jest.mock('utils', () => ({
  getImageUrl: jest.fn(),
}));

// Mock the components module
jest.mock('components', () => ({
  DetailRow: ({ title, content, sx }: { title: string; content?: React.ReactNode; sx?: any }) => (
    <div data-testid="detail-row" data-sx={JSON.stringify(sx)}>
      <span data-testid="detail-row-title">{title}</span>
      <span data-testid="detail-row-content">{content || '--'}</span>
    </div>
  ),
}));

import { getImageUrl } from 'utils';

const mockGetImageUrl = getImageUrl as jest.MockedFunction<typeof getImageUrl>;

describe('ExistedCutter', () => {
  const mockCutter: Cutter = {
    id: 'cutter-1',
    name: 'John Doe',
    licenseNumber: 'LIC123',
    isCertified: true,
    phoneNumber: '**********',
    firstName: 'John',
    lastName: 'Doe',
    profileId: 'profile-1',
    existingCutterProfileId: null,
    existingCutterId: null,
    avatar: {
      id: 'avatar-1',
      filenameDisk: 'avatar.jpg',
      filenameDownload: 'avatar.jpg',
    },
  };

  beforeEach(() => {
    mockGetImageUrl.mockClear();
  });

  it('should render cutter information correctly', () => {
    mockGetImageUrl.mockReturnValue('https://example.com/avatar.jpg');

    render(<ExistedCutter cutter={mockCutter} />);

    // Check if avatar is rendered with correct src
    const avatar = screen.getByRole('img', { name: 'user-image' });
    expect(avatar).toBeInTheDocument();
    expect(avatar).toHaveAttribute('src', 'https://example.com/avatar.jpg');

    // Check if support line text is rendered
    expect(screen.getByText('support-line')).toBeInTheDocument();

    // Check if general information title is rendered
    expect(screen.getByText('general-information')).toBeInTheDocument();

    // Check if detail rows are rendered with correct data
    const detailRows = screen.getAllByTestId('detail-row');
    expect(detailRows).toHaveLength(3);

    // Check phone number row
    const phoneRow = detailRows[0];
    expect(phoneRow).toHaveTextContent('cutter-phone-number');
    expect(phoneRow).toHaveTextContent('**********');

    // Check name row
    const nameRow = detailRows[1];
    expect(nameRow).toHaveTextContent('cutter-name');
    expect(nameRow).toHaveTextContent('John Doe');

    // Check license row
    const licenseRow = detailRows[2];
    expect(licenseRow).toHaveTextContent('have-license');
    expect(licenseRow).toHaveTextContent('registered-cutter');
  });

  it('should handle cutter without avatar', () => {
    const cutterWithoutAvatar: Cutter = {
      ...mockCutter,
      avatar: undefined,
    };

    mockGetImageUrl.mockReturnValue(null);

    render(<ExistedCutter cutter={cutterWithoutAvatar} />);

    // When no avatar is provided, MUI Avatar shows a fallback icon instead of img
    const avatarContainer = screen.getByTestId('PersonIcon').closest('.MuiAvatar-root');
    expect(avatarContainer).toBeInTheDocument();
    expect(mockGetImageUrl).toHaveBeenCalledWith(undefined);
  });

  it('should handle cutter without phone number', () => {
    const cutterWithoutPhone: Cutter = {
      ...mockCutter,
      phoneNumber: undefined,
    };

    render(<ExistedCutter cutter={cutterWithoutPhone} />);

    const detailRows = screen.getAllByTestId('detail-row');
    const phoneRow = detailRows[0];
    expect(phoneRow).toHaveTextContent('--');
  });

  it('should handle cutter without name', () => {
    const cutterWithoutName: Cutter = {
      ...mockCutter,
      name: '',
    };

    render(<ExistedCutter cutter={cutterWithoutName} />);

    const detailRows = screen.getAllByTestId('detail-row');
    const nameRow = detailRows[1];
    // The DetailRow mock should show '--' for empty content
    expect(nameRow).toHaveTextContent('--');
  });

  it('should display unregistered cutter when isCertified is false', () => {
    const unregisteredCutter: Cutter = {
      ...mockCutter,
      isCertified: false,
    };

    render(<ExistedCutter cutter={unregisteredCutter} />);

    const detailRows = screen.getAllByTestId('detail-row');
    const licenseRow = detailRows[2];
    expect(licenseRow).toHaveTextContent('unregistered-cutter');
  });

  it('should display unregistered cutter when isCertified is undefined', () => {
    const cutterWithUndefinedCertification: Cutter = {
      ...mockCutter,
      isCertified: undefined,
    };

    render(<ExistedCutter cutter={cutterWithUndefinedCertification} />);

    const detailRows = screen.getAllByTestId('detail-row');
    const licenseRow = detailRows[2];
    expect(licenseRow).toHaveTextContent('unregistered-cutter');
  });

  it('should call getImageUrl with correct parameter', () => {
    mockGetImageUrl.mockReturnValue('https://example.com/avatar.jpg');

    render(<ExistedCutter cutter={mockCutter} />);

    expect(mockGetImageUrl).toHaveBeenCalledWith('avatar.jpg');
    expect(mockGetImageUrl).toHaveBeenCalledTimes(1);
  });

  it('should apply correct styling to detail rows', () => {
    render(<ExistedCutter cutter={mockCutter} />);

    const detailRows = screen.getAllByTestId('detail-row');
    detailRows.forEach((row) => {
      const sxData = row.getAttribute('data-sx');
      expect(sxData).toBe('{"flexDirection":"row"}');
    });
  });

  it('should render with correct container structure', () => {
    const { container } = render(<ExistedCutter cutter={mockCutter} />);

    // Check main container structure
    const mainBox = container.firstChild as HTMLElement;
    expect(mainBox).toHaveStyle({
      display: 'flex',
      'flex-direction': 'column',
      gap: '16px', // MUI converts gap: 2 to 16px
      'margin-top': '24px', // MUI converts mt: 3 to 24px
    });
  });

  it('should handle null cutter gracefully', () => {
    const nullCutter = {
      ...mockCutter,
      name: null as any,
      phoneNumber: null as any,
      isCertified: null as any,
      avatar: null as any,
    };

    mockGetImageUrl.mockReturnValue(null);

    render(<ExistedCutter cutter={nullCutter} />);

    // Should render without crashing
    expect(screen.getByText('general-information')).toBeInTheDocument();

    // Check that fallback values are used
    const detailRows = screen.getAllByTestId('detail-row');
    expect(detailRows[0]).toHaveTextContent('--'); // phone number
    expect(detailRows[1]).toHaveTextContent('--'); // name
    expect(detailRows[2]).toHaveTextContent('unregistered-cutter'); // certification
  });

  it('should handle cutter with avatar but null filenameDisk', () => {
    const cutterWithNullFilename: Cutter = {
      ...mockCutter,
      avatar: {
        id: 'avatar-1',
        filenameDisk: null as any,
        filenameDownload: 'avatar.jpg',
      },
    };

    mockGetImageUrl.mockReturnValue(null);

    render(<ExistedCutter cutter={cutterWithNullFilename} />);

    expect(mockGetImageUrl).toHaveBeenCalledWith(null);
    // Should show fallback avatar icon
    const avatarContainer = screen.getByTestId('PersonIcon').closest('.MuiAvatar-root');
    expect(avatarContainer).toBeInTheDocument();
  });

  it('should handle very long cutter name', () => {
    const cutterWithLongName: Cutter = {
      ...mockCutter,
      name: 'This is a very long cutter name that might cause layout issues if not handled properly',
    };

    render(<ExistedCutter cutter={cutterWithLongName} />);

    const detailRows = screen.getAllByTestId('detail-row');
    const nameRow = detailRows[1];
    expect(nameRow).toHaveTextContent('This is a very long cutter name that might cause layout issues if not handled properly');
  });

  it('should handle special characters in cutter data', () => {
    const cutterWithSpecialChars: Cutter = {
      ...mockCutter,
      name: 'John & Jane Doe <script>alert("test")</script>',
      phoneNumber: '+66-81-234-5678',
    };

    render(<ExistedCutter cutter={cutterWithSpecialChars} />);

    const detailRows = screen.getAllByTestId('detail-row');
    expect(detailRows[0]).toHaveTextContent('+66-81-234-5678');
    expect(detailRows[1]).toHaveTextContent('John & Jane Doe <script>alert("test")</script>');
  });

  describe('Phone Number Formatting', () => {
    it('should format phone number by adding 0 prefix when not present', () => {
      const cutterWithoutPrefix: Cutter = {
        ...mockCutter,
        phoneNumber: '812345678',
      };

      render(<ExistedCutter cutter={cutterWithoutPrefix} />);

      const detailRows = screen.getAllByTestId('detail-row');
      const phoneRow = detailRows[0];
      expect(phoneRow).toHaveTextContent('**********');
    });

    it('should not modify phone number when it already starts with 0', () => {
      const cutterWithPrefix: Cutter = {
        ...mockCutter,
        phoneNumber: '**********',
      };

      render(<ExistedCutter cutter={cutterWithPrefix} />);

      const detailRows = screen.getAllByTestId('detail-row');
      const phoneRow = detailRows[0];
      expect(phoneRow).toHaveTextContent('**********');
    });

    it('should display -- when phone number is undefined', () => {
      const cutterWithoutPhone: Cutter = {
        ...mockCutter,
        phoneNumber: undefined,
      };

      render(<ExistedCutter cutter={cutterWithoutPhone} />);

      const detailRows = screen.getAllByTestId('detail-row');
      const phoneRow = detailRows[0];
      expect(phoneRow).toHaveTextContent('--');
    });

    it('should display -- when phone number is empty string', () => {
      const cutterWithEmptyPhone: Cutter = {
        ...mockCutter,
        phoneNumber: '',
      };

      render(<ExistedCutter cutter={cutterWithEmptyPhone} />);

      const detailRows = screen.getAllByTestId('detail-row');
      const phoneRow = detailRows[0];
      expect(phoneRow).toHaveTextContent('--');
    });

    it('should handle phone numbers with international format', () => {
      const cutterWithInternationalPhone: Cutter = {
        ...mockCutter,
        phoneNumber: '+66812345678',
      };

      render(<ExistedCutter cutter={cutterWithInternationalPhone} />);

      const detailRows = screen.getAllByTestId('detail-row');
      const phoneRow = detailRows[0];
      expect(phoneRow).toHaveTextContent('0+66812345678');
    });
  });
});
