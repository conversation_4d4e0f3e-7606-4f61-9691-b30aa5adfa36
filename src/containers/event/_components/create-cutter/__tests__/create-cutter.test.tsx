/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { CreateCutter } from '../create-cutter';
import { Cutter } from 'types';

// Mock the UploadCutterImage component
jest.mock('../upload-cutter-img', () => {
  return function MockUploadCutterImage({ onChange }: { onChange: (imgString: string) => void }) {
    return (
      <div data-testid="upload-cutter-image">
        <button
          data-testid="upload-image-button"
          onClick={() => onChange('test-image-string')}
        >
          Upload Image
        </button>
      </div>
    );
  };
});

// Mock the TextInput component
jest.mock('components', () => ({
  TextInput: ({ value, onChange, maxLength, required, ...props }: any) => (
    <input
      data-testid={props['data-testid'] || 'text-input'}
      value={value || ''}
      onChange={onChange}
      maxLength={maxLength}
      required={required}
      {...props}
    />
  ),
}));

// Mock the filter table styles
jest.mock('components/filter-table/filter-table.styles', () => ({
  activeButtonSelectStyle: { backgroundColor: 'blue' },
  normalButtonSelectStyle: { backgroundColor: 'gray' },
}));

describe('CreateCutter', () => {
  const mockCutter: Cutter = {
    id: '1',
    name: 'Test Cutter',
    licenseNumber: 'LIC123',
    isCertified: false,
    profileId: null,
    phoneNumber: '**********',
    existingCutterProfileId: null,
    existingCutterId: null,
    avatar: {
      id: 'avatar-1',
      filenameDisk: 'test-avatar.jpg',
      filenameDownload: 'avatar.jpg',
    },
  };

  const mockSetCutter = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all required elements', () => {
    render(<CreateCutter cutter={mockCutter} setCutter={mockSetCutter} />);

    // Check if upload image component is rendered
    expect(screen.getByTestId('upload-cutter-image')).toBeInTheDocument();

    // Check if general information title is rendered
    expect(screen.getByText('general-information')).toBeInTheDocument();

    // Check if phone number field is rendered
    expect(screen.getByText('cutter-phone-number')).toBeInTheDocument();
    expect(screen.getAllByText('*')).toHaveLength(2); // Required asterisks

    // Check if name field is rendered
    expect(screen.getByText('cutter-name')).toBeInTheDocument();

    // Check if license question is rendered
    expect(screen.getByText('cutter-license-question')).toBeInTheDocument();

    // Check if license buttons are rendered
    expect(screen.getByText('registered-cutter')).toBeInTheDocument();
    expect(screen.getByText('unregistered-cutter')).toBeInTheDocument();
  });

  it('displays current cutter data in form fields', () => {
    render(<CreateCutter cutter={mockCutter} setCutter={mockSetCutter} />);

    const phoneInput = screen.getAllByRole('textbox')[0];
    const nameInput = screen.getAllByRole('textbox')[1];

    expect(phoneInput).toHaveValue('**********');
    expect(nameInput).toHaveValue('Test Cutter');
  });

  it('handles phone number input correctly', () => {
    render(<CreateCutter cutter={mockCutter} setCutter={mockSetCutter} />);

    const phoneInput = screen.getAllByRole('textbox')[0];

    // Clear previous calls from initial render
    mockSetCutter.mockClear();

    // Test numeric input (use different number from initial value)
    fireEvent.change(phoneInput, { target: { value: '**********' } });
    expect(mockSetCutter).toHaveBeenCalledWith({
      ...mockCutter,
      phoneNumber: '**********',
    });

    // Test non-numeric input (should be filtered, only digits and + allowed)
    fireEvent.change(phoneInput, { target: { value: 'abc+66123def456' } });
    expect(mockSetCutter).toHaveBeenCalledWith({
      ...mockCutter,
      phoneNumber: '+66123456',
    });
  });

  it('handles name input correctly', () => {
    render(<CreateCutter cutter={mockCutter} setCutter={mockSetCutter} />);

    const nameInput = screen.getAllByRole('textbox')[1];

    // Test valid alphanumeric input
    fireEvent.change(nameInput, { target: { value: 'John Doe 123' } });
    expect(mockSetCutter).toHaveBeenCalledWith({
      ...mockCutter,
      name: 'John Doe 123',
    });

    // Test input with special characters (should be filtered)
    fireEvent.change(nameInput, { target: { value: 'John@Doe#123!' } });
    expect(mockSetCutter).toHaveBeenCalledWith({
      ...mockCutter,
      name: 'JohnDoe123',
    });
  });

  it('handles license certification selection', () => {
    render(<CreateCutter cutter={mockCutter} setCutter={mockSetCutter} />);

    const registeredButton = screen.getByText('registered-cutter');
    const unregisteredButton = screen.getByText('unregistered-cutter');

    // Click registered cutter button
    fireEvent.click(registeredButton);
    expect(mockSetCutter).toHaveBeenCalledWith({
      ...mockCutter,
      isCertified: true,
    });

    // Click unregistered cutter button
    fireEvent.click(unregisteredButton);
    expect(mockSetCutter).toHaveBeenCalledWith({
      ...mockCutter,
      isCertified: false,
    });
  });

  it('applies correct styles to license buttons based on certification status', () => {
    const certifiedCutter = { ...mockCutter, isCertified: true };
    const { rerender } = render(<CreateCutter cutter={certifiedCutter} setCutter={mockSetCutter} />);

    const registeredButton = screen.getByText('registered-cutter');
    const unregisteredButton = screen.getByText('unregistered-cutter');

    // When certified, registered button should have active style
    expect(registeredButton).toHaveStyle('background-color: rgb(0, 0, 255)');
    expect(unregisteredButton).toHaveStyle('background-color: rgb(128, 128, 128)');

    // Re-render with uncertified cutter
    const uncertifiedCutter = { ...mockCutter, isCertified: false };
    rerender(<CreateCutter cutter={uncertifiedCutter} setCutter={mockSetCutter} />);

    // When not certified, unregistered button should have active style
    expect(registeredButton).toHaveStyle('background-color: rgb(128, 128, 128)');
    expect(unregisteredButton).toHaveStyle('background-color: rgb(0, 0, 255)');
  });

  it('handles image upload correctly', () => {
    render(<CreateCutter cutter={mockCutter} setCutter={mockSetCutter} />);

    const uploadButton = screen.getByTestId('upload-image-button');

    fireEvent.click(uploadButton);

    expect(mockSetCutter).toHaveBeenCalledWith({
      ...mockCutter,
      avatar: {
        ...mockCutter.avatar,
        filenameDisk: 'test-image-string',
        id: mockCutter.avatar?.id ?? '',
        filenameDownload: mockCutter.avatar?.filenameDownload ?? '',
      },
    });
  });

  it('handles cutter without avatar', () => {
    const cutterWithoutAvatar = { ...mockCutter, avatar: undefined };
    render(<CreateCutter cutter={cutterWithoutAvatar} setCutter={mockSetCutter} />);

    const uploadButton = screen.getByTestId('upload-image-button');
    fireEvent.click(uploadButton);

    expect(mockSetCutter).toHaveBeenCalledWith({
      ...cutterWithoutAvatar,
      avatar: {
        filenameDisk: 'test-image-string',
        id: '',
        filenameDownload: '',
      },
    });
  });

  it('enforces maxLength constraints on inputs', () => {
    render(<CreateCutter cutter={mockCutter} setCutter={mockSetCutter} />);

    const phoneInput = screen.getAllByRole('textbox')[0];
    const nameInput = screen.getAllByRole('textbox')[1];

    // Phone input no longer has maxLength constraint
    expect(phoneInput).not.toHaveAttribute('maxLength');
    expect(nameInput).toHaveAttribute('maxLength', '100');
  });

  it('marks required fields correctly', () => {
    render(<CreateCutter cutter={mockCutter} setCutter={mockSetCutter} />);

    const phoneInput = screen.getAllByRole('textbox')[0];
    const nameInput = screen.getAllByRole('textbox')[1];

    expect(phoneInput).toHaveAttribute('required');
    expect(nameInput).toHaveAttribute('required');
  });

  it('handles empty phone number input', () => {
    const cutterWithoutPhone = { ...mockCutter, phoneNumber: undefined };
    render(<CreateCutter cutter={cutterWithoutPhone} setCutter={mockSetCutter} />);

    const phoneInput = screen.getAllByRole('textbox')[0];
    expect(phoneInput).toHaveValue('');
  });

  it('handles empty name input', () => {
    const cutterWithoutName = { ...mockCutter, name: '' };
    render(<CreateCutter cutter={cutterWithoutName} setCutter={mockSetCutter} />);

    const nameInput = screen.getAllByRole('textbox')[1];
    expect(nameInput).toHaveValue('');
  });

  describe('Phone number validation', () => {
    it('allows valid Thai phone number starting with 0 (10 digits)', () => {
      render(<CreateCutter cutter={mockCutter} setCutter={mockSetCutter} />);
      const phoneInput = screen.getAllByRole('textbox')[0];

      // Clear previous calls from initial render
      mockSetCutter.mockClear();

      // Use different number from initial value
      fireEvent.change(phoneInput, { target: { value: '**********' } });

      expect(mockSetCutter).toHaveBeenCalledWith({
        ...mockCutter,
        phoneNumber: '**********',
      });

      // Should not show error for valid format
      expect(screen.queryByText('invalid-thai-phone-number')).not.toBeInTheDocument();
    });

    it('allows valid Thai phone number starting with +66 (12 characters)', () => {
      render(<CreateCutter cutter={mockCutter} setCutter={mockSetCutter} />);
      const phoneInput = screen.getAllByRole('textbox')[0];

      fireEvent.change(phoneInput, { target: { value: '+66812345678' } });

      expect(mockSetCutter).toHaveBeenCalledWith({
        ...mockCutter,
        phoneNumber: '+66812345678',
      });

      // Should not show error for valid format
      expect(screen.queryByText('invalid-thai-phone-number')).not.toBeInTheDocument();
    });

    it('allows valid Thai phone number starting with 66 (11 digits)', () => {
      render(<CreateCutter cutter={mockCutter} setCutter={mockSetCutter} />);
      const phoneInput = screen.getAllByRole('textbox')[0];

      fireEvent.change(phoneInput, { target: { value: '66812345678' } });

      expect(mockSetCutter).toHaveBeenCalledWith({
        ...mockCutter,
        phoneNumber: '66812345678',
      });

      // Should not show error for valid format
      expect(screen.queryByText('invalid-thai-phone-number')).not.toBeInTheDocument();
    });

    it('shows error for invalid phone number format', () => {
      render(<CreateCutter cutter={mockCutter} setCutter={mockSetCutter} />);
      const phoneInput = screen.getAllByRole('textbox')[0];

      fireEvent.change(phoneInput, { target: { value: '123456789' } });

      expect(mockSetCutter).toHaveBeenCalledWith({
        ...mockCutter,
        phoneNumber: '123456789',
      });

      // Should show error for invalid format
      expect(screen.getByText('invalid-thai-phone-number')).toBeInTheDocument();
    });

    it('shows error for phone number with wrong length', () => {
      render(<CreateCutter cutter={mockCutter} setCutter={mockSetCutter} />);
      const phoneInput = screen.getAllByRole('textbox')[0];

      fireEvent.change(phoneInput, { target: { value: '081234567' } }); // 9 digits instead of 10

      expect(mockSetCutter).toHaveBeenCalledWith({
        ...mockCutter,
        phoneNumber: '081234567',
      });

      // Should show error for invalid length
      expect(screen.getByText('invalid-thai-phone-number')).toBeInTheDocument();
    });

    it('filters out non-numeric characters except + from phone input', () => {
      render(<CreateCutter cutter={mockCutter} setCutter={mockSetCutter} />);
      const phoneInput = screen.getAllByRole('textbox')[0];

      fireEvent.change(phoneInput, { target: { value: '+66a8b1c2d3e4f5g6h7i8j' } });

      expect(mockSetCutter).toHaveBeenCalledWith({
        ...mockCutter,
        phoneNumber: '+66812345678',
      });
    });

    it('filters out spaces from phone input', () => {
      render(<CreateCutter cutter={mockCutter} setCutter={mockSetCutter} />);
      const phoneInput = screen.getAllByRole('textbox')[0];

      fireEvent.change(phoneInput, { target: { value: '************' } });

      expect(mockSetCutter).toHaveBeenCalledWith({
        ...mockCutter,
        phoneNumber: '**********',
      });
    });

    it('handles empty phone number input correctly', () => {
      render(<CreateCutter cutter={mockCutter} setCutter={mockSetCutter} />);
      const phoneInput = screen.getAllByRole('textbox')[0];

      fireEvent.change(phoneInput, { target: { value: '' } });

      expect(mockSetCutter).toHaveBeenCalledWith({
        ...mockCutter,
        phoneNumber: '',
      });

      // Should not show error for empty input
      expect(screen.queryByText('invalid-thai-phone-number')).not.toBeInTheDocument();
    });

    it('clears error when valid phone number is entered after invalid one', () => {
      render(<CreateCutter cutter={mockCutter} setCutter={mockSetCutter} />);
      const phoneInput = screen.getAllByRole('textbox')[0];

      // Enter invalid phone number first
      fireEvent.change(phoneInput, { target: { value: '123' } });
      expect(screen.getByText('invalid-thai-phone-number')).toBeInTheDocument();

      // Enter valid phone number (different from initial value)
      fireEvent.change(phoneInput, { target: { value: '**********' } });
      expect(screen.queryByText('invalid-thai-phone-number')).not.toBeInTheDocument();
    });

    it('calls onPhoneNumberErrorChange callback when phone number validation changes', () => {
      const mockOnPhoneNumberErrorChange = jest.fn();
      render(
        <CreateCutter
          cutter={mockCutter}
          setCutter={mockSetCutter}
          onPhoneNumberErrorChange={mockOnPhoneNumberErrorChange}
        />
      );

      const phoneInput = screen.getAllByRole('textbox')[0];

      // Clear phone number first
      fireEvent.change(phoneInput, { target: { value: '' } });
      expect(mockOnPhoneNumberErrorChange).toHaveBeenCalledWith(false);

      // Enter invalid phone number
      fireEvent.change(phoneInput, { target: { value: '123' } });
      expect(mockOnPhoneNumberErrorChange).toHaveBeenCalledWith(true);

      // Enter valid phone number
      fireEvent.change(phoneInput, { target: { value: '**********' } });
      expect(mockOnPhoneNumberErrorChange).toHaveBeenCalledWith(false);
    });
  });

  describe('Name validation', () => {
    it('allows alphanumeric characters and spaces', () => {
      render(<CreateCutter cutter={mockCutter} setCutter={mockSetCutter} />);
      const nameInput = screen.getAllByRole('textbox')[1];

      fireEvent.change(nameInput, { target: { value: 'John Doe 123' } });

      expect(mockSetCutter).toHaveBeenCalledWith({
        ...mockCutter,
        name: 'John Doe 123',
      });
    });

    it('filters out special characters from name input', () => {
      render(<CreateCutter cutter={mockCutter} setCutter={mockSetCutter} />);
      const nameInput = screen.getAllByRole('textbox')[1];

      fireEvent.change(nameInput, { target: { value: 'John@#$%Doe!&*()123' } });

      expect(mockSetCutter).toHaveBeenCalledWith({
        ...mockCutter,
        name: 'JohnDoe123',
      });
    });

    it('handles empty name input correctly', () => {
      render(<CreateCutter cutter={mockCutter} setCutter={mockSetCutter} />);
      const nameInput = screen.getAllByRole('textbox')[1];

      fireEvent.change(nameInput, { target: { value: '' } });

      expect(mockSetCutter).toHaveBeenCalledWith({
        ...mockCutter,
        name: '',
      });
    });
  });

  describe('License certification buttons', () => {
    it('renders both license options', () => {
      render(<CreateCutter cutter={mockCutter} setCutter={mockSetCutter} />);

      expect(screen.getByText('registered-cutter')).toBeInTheDocument();
      expect(screen.getByText('unregistered-cutter')).toBeInTheDocument();
    });

    it('highlights correct button based on certification status', () => {
      const certifiedCutter = { ...mockCutter, isCertified: true };
      render(<CreateCutter cutter={certifiedCutter} setCutter={mockSetCutter} />);

      const registeredButton = screen.getByText('registered-cutter');
      const unregisteredButton = screen.getByText('unregistered-cutter');

      expect(registeredButton).toHaveStyle('background-color: rgb(0, 0, 255)');
      expect(unregisteredButton).toHaveStyle('background-color: rgb(128, 128, 128)');
    });

    it('toggles certification status when buttons are clicked', () => {
      render(<CreateCutter cutter={mockCutter} setCutter={mockSetCutter} />);

      const registeredButton = screen.getByText('registered-cutter');

      fireEvent.click(registeredButton);

      expect(mockSetCutter).toHaveBeenCalledWith({
        ...mockCutter,
        isCertified: true,
      });
    });
  });

  describe('Image upload functionality', () => {
    it('calls onChange with image string when upload is triggered', () => {
      render(<CreateCutter cutter={mockCutter} setCutter={mockSetCutter} />);

      const uploadButton = screen.getByTestId('upload-image-button');
      fireEvent.click(uploadButton);

      expect(mockSetCutter).toHaveBeenCalledWith({
        ...mockCutter,
        avatar: {
          ...mockCutter.avatar,
          filenameDisk: 'test-image-string',
          id: mockCutter.avatar?.id ?? '',
          filenameDownload: mockCutter.avatar?.filenameDownload ?? '',
        },
      });
    });

    it('creates new avatar object when cutter has no existing avatar', () => {
      const cutterWithoutAvatar = { ...mockCutter, avatar: undefined };
      render(<CreateCutter cutter={cutterWithoutAvatar} setCutter={mockSetCutter} />);

      const uploadButton = screen.getByTestId('upload-image-button');
      fireEvent.click(uploadButton);

      expect(mockSetCutter).toHaveBeenCalledWith({
        ...cutterWithoutAvatar,
        avatar: {
          filenameDisk: 'test-image-string',
          id: '',
          filenameDownload: '',
        },
      });
    });
  });

  describe('Component accessibility', () => {
    it('has proper labels for form fields', () => {
      render(<CreateCutter cutter={mockCutter} setCutter={mockSetCutter} />);

      expect(screen.getByText('cutter-phone-number')).toBeInTheDocument();
      expect(screen.getByText('cutter-name')).toBeInTheDocument();
      expect(screen.getByText('cutter-license-question')).toBeInTheDocument();
    });

    it('marks required fields with asterisks', () => {
      render(<CreateCutter cutter={mockCutter} setCutter={mockSetCutter} />);

      const asterisks = screen.getAllByText('*');
      expect(asterisks).toHaveLength(2); // Phone and name fields
    });

    it('has proper button roles for license selection', () => {
      render(<CreateCutter cutter={mockCutter} setCutter={mockSetCutter} />);

      const registeredButton = screen.getByRole('button', { name: 'registered-cutter' });
      const unregisteredButton = screen.getByRole('button', { name: 'unregistered-cutter' });

      expect(registeredButton).toBeInTheDocument();
      expect(unregisteredButton).toBeInTheDocument();
    });
  });
});
