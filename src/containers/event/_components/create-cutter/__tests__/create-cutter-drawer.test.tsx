/* eslint-disable @next/next/no-img-element */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { CreateCutterDrawer } from '../create-cutter-drawer';
import { getImageUrl } from 'utils';
import { Cutter } from 'types';

// Mock dependencies
jest.mock('services/resource.service');
jest.mock('utils', () => ({
  getImageUrl: jest.fn(),
}));

// Mock @tanstack/react-query
jest.mock('@tanstack/react-query', () => ({
  useQuery: jest.fn(),
}));

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: () => (key: string) => key,
}));

// Mock Next.js server components that cause ReferenceError
jest.mock('next/server', () => ({
  NextRequest: jest.fn(),
  NextResponse: jest.fn(),
}));

// Mock server-side utilities
jest.mock('configs/cookie', () => ({
  getCookieServer: jest.fn(),
}));

// Mock components
jest.mock('components', () => ({
  DebounceSearchInput: ({ value, onChange, placeholder, startAdornment }: any) => (
    <div data-testid="debounce-search-input">
      <input
        data-testid="search-input"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
      />
      {startAdornment}
    </div>
  ),
  Drawer: ({ children, open, onClose, drawerTitle, footerElement, anchor, hasActionBtn }: any) => (
    <div data-testid="drawer" data-open={open} data-anchor={anchor} data-has-action-btn={hasActionBtn}>
      <div data-testid="drawer-title">{drawerTitle}</div>
      <div data-testid="drawer-content">{children}</div>
      <div data-testid="drawer-footer">{footerElement}</div>
      <button data-testid="drawer-close" onClick={onClose}>Close</button>
    </div>
  ),
}));

// Mock child components
jest.mock('../create-cutter', () => ({
  CreateCutter: ({ cutter, setCutter, onPhoneNumberErrorChange }: any) => {
    const validateThaiPhoneNumber = (phoneNumber: string): boolean => {
      if (/^0\d{9}$/.test(phoneNumber)) return true;
      if (/^\+66\d{9}$/.test(phoneNumber)) return true;
      if (/^66\d{9}$/.test(phoneNumber)) return true;
      return false;
    };

    return (
      <div data-testid="create-cutter">
        <input
          data-testid="cutter-name-input"
          value={cutter.name}
          onChange={(e) => setCutter({ ...cutter, name: e.target.value })}
          placeholder="Cutter Name"
        />
        <input
          data-testid="cutter-phone-input"
          value={cutter.phoneNumber ?? ''}
          onChange={(e) => {
            const value = e.target.value;
            setCutter({ ...cutter, phoneNumber: value });

            // Simulate phone number validation
            if (value === '') {
              onPhoneNumberErrorChange?.(false);
            } else if (!validateThaiPhoneNumber(value)) {
              onPhoneNumberErrorChange?.(true);
            } else {
              onPhoneNumberErrorChange?.(false);
            }
          }}
          placeholder="Phone Number"
        />
        {cutter.phoneNumber && !validateThaiPhoneNumber(cutter.phoneNumber) && (
          <div>invalid-thai-phone-number</div>
        )}
      </div>
    );
  },
}));

jest.mock('../exist-cutter', () => ({
  ExistedCutter: ({ cutter }: any) => (
    <div data-testid="existed-cutter">
      <span data-testid="existed-cutter-name">{cutter.name}</span>
      <span data-testid="existed-cutter-phone">{cutter.phoneNumber}</span>
    </div>
  ),
}));

jest.mock('../create-cutter-vehicle', () => ({
  CreateCutterVehicle: ({ vehicle, setVehicle }: any) => (
    <div data-testid="create-cutter-vehicle">
      <input
        data-testid="vehicle-registration-input"
        value={vehicle.vehicleRegistrationNumber}
        onChange={(e) => setVehicle({ ...vehicle, vehicleRegistrationNumber: e.target.value })}
        placeholder="Vehicle Registration"
      />
      <input
        data-testid="province-registration-input"
        value={vehicle.provinceRegistrationNumber}
        onChange={(e) => setVehicle({ ...vehicle, provinceRegistrationNumber: e.target.value })}
        placeholder="Province Registration"
      />
    </div>
  ),
}));

// Mock MUI components that might cause issues
jest.mock('@mui/material', () => ({
  ...jest.requireActual('@mui/material'),
  LinearProgress: () => <div data-testid="linear-progress">Loading...</div>,
  Avatar: ({ src, alt }: { src: string; alt: string }) => (
    <img data-testid="avatar" src={src} alt={alt} />
  ),
}));

// Mock Next.js Image component
jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ src, alt, width, height }: any) => (
    <img data-testid="next-image" src={src} alt={alt} width={width} height={height} />
  ),
}));

const mockGetImageUrl = getImageUrl as jest.MockedFunction<typeof getImageUrl>;

// Import useQuery after mocking
import { useQuery } from '@tanstack/react-query';
const mockUseQuery = useQuery as jest.MockedFunction<typeof useQuery>;

describe('CreateCutterDrawer Component', () => {
  const mockToggle = jest.fn();
  const mockOnAddCutter = jest.fn();

  const mockCutters: Cutter[] = [
    {
      id: '1',
      name: 'John Doe',
      licenseNumber: 'LIC123',
      isCertified: true,
      profileId: 'profile1',
      phoneNumber: '**********',
      existingCutterProfileId: 'profile1',
      existingCutterId: '1',
      vehicleNumber: 'ABC123',
      provinceVehicleCode: 'BKK',
      vehicleImage: 'vehicle1.jpg',
      avatar: {
        id: 'avatar1',
        filenameDisk: 'avatar1.jpg',
        filenameDownload: 'avatar1.jpg',
      },
    },
    {
      id: '2',
      name: 'Jane Smith',
      licenseNumber: 'LIC456',
      isCertified: false,
      profileId: 'profile2',
      phoneNumber: '**********',
      firstName: 'Jane',
      lastName: 'Smith',
      existingCutterProfileId: 'profile2',
      existingCutterId: '2',
      vehicleNumber: 'XYZ789',
      provinceVehicleCode: 'CNX',
      vehicleImage: 'vehicle2.jpg',
      avatar: {
        id: 'avatar2',
        filenameDisk: 'avatar2.jpg',
        filenameDownload: 'avatar2.jpg',
      },
    },
  ];

  const defaultProps = {
    open: true,
    toggle: mockToggle,
    onAddCutter: mockOnAddCutter,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockGetImageUrl.mockReturnValue('https://example.com/image.jpg');
    mockUseQuery.mockReturnValue({
      data: { data: mockCutters },
      isLoading: false,
      isFetching: false,
      error: null,
      isError: false,
      isSuccess: true,
      refetch: jest.fn(),
    } as any);
  });

  const renderComponent = (props = {}) => {
    return render(<CreateCutterDrawer {...defaultProps} {...props} />);
  };

  describe('Initial Render', () => {
    it('should render drawer with correct title and initial state', () => {
      renderComponent();

      expect(screen.getByTestId('drawer')).toBeInTheDocument();
      expect(screen.getByTestId('drawer-title')).toHaveTextContent('add-cutter-information');
      expect(screen.getByTestId('drawer')).toHaveAttribute('data-open', 'true');
      expect(screen.getByTestId('drawer')).toHaveAttribute('data-anchor', 'right');
    });

    it('should render search input in list mode', () => {
      renderComponent();

      expect(screen.getByTestId('debounce-search-input')).toBeInTheDocument();
      expect(screen.getByTestId('search-input')).toHaveAttribute('placeholder', 'search-cutter');
    });

    it('should render add walk-in cutter button in list mode', () => {
      renderComponent();

      const addButton = screen.getByText('add-walk-in-cutter');
      expect(addButton).toBeInTheDocument();
    });
  });

  describe('Cutter List Display', () => {
    it('should display list of cutters when data is available', () => {
      renderComponent();

      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('**********')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      expect(screen.getByText('**********')).toBeInTheDocument();
    });

    it('should display loading indicator when fetching data', () => {
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        isFetching: true,
        error: null,
        isError: false,
        isSuccess: false,
        refetch: jest.fn(),
      } as any);

      renderComponent();

      expect(screen.getByTestId('linear-progress')).toBeInTheDocument();
    });

    it('should display empty state when no cutters found with search keyword', async () => {
      const user = userEvent.setup();
      mockUseQuery.mockReturnValue({
        data: { data: [] },
        isLoading: false,
        isFetching: false,
        error: null,
        isError: false,
        isSuccess: true,
        refetch: jest.fn(),
      } as any);

      renderComponent();

      // Type in search input to trigger empty state
      const searchInput = screen.getByTestId('search-input');
      await user.type(searchInput, 'nonexistent');

      await waitFor(() => {
        expect(screen.getByTestId('next-image')).toBeInTheDocument();
        expect(screen.getByText('no-result-found')).toBeInTheDocument();
      });
    });

    it('should not display empty state when no search keyword is provided', () => {
      mockUseQuery.mockReturnValue({
        data: { data: [] },
        isLoading: false,
        isFetching: false,
        error: null,
        isError: false,
        isSuccess: true,
        refetch: jest.fn(),
      } as any);

      renderComponent();

      // Should not show empty state without search keyword
      expect(screen.queryByTestId('next-image')).not.toBeInTheDocument();
      expect(screen.queryByText('no-result-found')).not.toBeInTheDocument();
    });

    it('should filter out cutters without name or phone number', () => {
      const cuttersWithEmpty = [
        ...mockCutters,
        {
          id: '3',
          name: '',
          licenseNumber: 'LIC789',
          isCertified: false,
          profileId: 'profile3',
          phoneNumber: '',
          existingCutterProfileId: null,
          existingCutterId: null,
          vehicleNumber: null,
          provinceVehicleCode: null,
          vehicleImage: null,
        },
      ];

      mockUseQuery.mockReturnValue({
        data: { data: cuttersWithEmpty },
        isLoading: false,
        isFetching: false,
        error: null,
        isError: false,
        isSuccess: true,
        refetch: jest.fn(),
      } as any);

      renderComponent();

      // Should only show the valid cutters
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      expect(screen.queryByText('LIC789')).not.toBeInTheDocument();
    });
  });

  describe('Search Functionality', () => {
    it('should update search keyword when typing in search input', async () => {
      const user = userEvent.setup();
      renderComponent();

      const searchInput = screen.getByTestId('search-input');
      await user.type(searchInput, 'John');

      expect(searchInput).toHaveValue('John');
    });

    it('should display search result text when searching', async () => {
      const user = userEvent.setup();
      renderComponent();

      const searchInput = screen.getByTestId('search-input');
      await user.type(searchInput, 'John');

      await waitFor(() => {
        expect(screen.getByText('search-result')).toBeInTheDocument();
      });
    });

    it('should display suggested cutter text when not searching', () => {
      renderComponent();

      expect(screen.getByText('suggested-cutter')).toBeInTheDocument();
    });

    it('should trigger query when search keyword changes', async () => {
      const user = userEvent.setup();
      renderComponent();

      const searchInput = screen.getByTestId('search-input');
      await user.type(searchInput, 'test-search');

      // Verify that the search input value is updated
      expect(searchInput).toHaveValue('test-search');

      // The query should be triggered with the new search keyword
      // This test verifies the search functionality works
      await waitFor(() => {
        expect(screen.getByText('search-result')).toBeInTheDocument();
      });
    });
  });

  describe('Mode Navigation', () => {
    it('should switch to detail mode when cutter is selected', async () => {
      const user = userEvent.setup();
      renderComponent();

      const cutterItem = screen.getByText('John Doe').closest('div');
      await user.click(cutterItem!);

      await waitFor(() => {
        expect(screen.getByTestId('existed-cutter')).toBeInTheDocument();
        expect(screen.getByTestId('create-cutter-vehicle')).toBeInTheDocument();
      });
    });

    it('should populate vehicle information when cutter with vehicle data is selected', async () => {
      const user = userEvent.setup();
      renderComponent();

      const cutterItem = screen.getByText('John Doe').closest('div');
      await user.click(cutterItem!);

      await waitFor(() => {
        expect(screen.getByTestId('existed-cutter')).toBeInTheDocument();
        expect(screen.getByTestId('create-cutter-vehicle')).toBeInTheDocument();

        // Check that vehicle registration input is populated with cutter's vehicle number
        const vehicleInput = screen.getByTestId('vehicle-registration-input');
        expect(vehicleInput).toHaveValue('ABC123');

        // Check that province registration input is populated with cutter's province code
        const provinceInput = screen.getByTestId('province-registration-input');
        expect(provinceInput).toHaveValue('BKK');
      });
    });

    it('should handle cutter selection with null vehicle data gracefully', async () => {
      const user = userEvent.setup();

      // Create a cutter with null vehicle data
      const cutterWithNullVehicle = {
        ...mockCutters[0],
        vehicleNumber: null,
        provinceVehicleCode: null,
        vehicleImage: null,
      };

      mockUseQuery.mockReturnValue({
        data: { data: [cutterWithNullVehicle] },
        isLoading: false,
        isFetching: false,
        error: null,
        isError: false,
        isSuccess: true,
        refetch: jest.fn(),
      } as any);

      renderComponent();

      const cutterItem = screen.getByText('John Doe').closest('div');
      await user.click(cutterItem!);

      await waitFor(() => {
        expect(screen.getByTestId('existed-cutter')).toBeInTheDocument();
        expect(screen.getByTestId('create-cutter-vehicle')).toBeInTheDocument();

        // Check that vehicle inputs are populated with empty strings when cutter data is null
        const vehicleInput = screen.getByTestId('vehicle-registration-input');
        expect(vehicleInput).toHaveValue('');

        const provinceInput = screen.getByTestId('province-registration-input');
        expect(provinceInput).toHaveValue('');
      });
    });

    it('should switch to create mode when add walk-in cutter button is clicked', async () => {
      const user = userEvent.setup();
      renderComponent();

      const addButton = screen.getByText('add-walk-in-cutter');
      await user.click(addButton);

      await waitFor(() => {
        expect(screen.getByTestId('create-cutter')).toBeInTheDocument();
        expect(screen.getByTestId('create-cutter-vehicle')).toBeInTheDocument();
      });
    });

    it('should return to list mode when back button is clicked', async () => {
      const user = userEvent.setup();
      renderComponent();

      // Go to create mode first
      const addButton = screen.getByText('add-walk-in-cutter');
      await user.click(addButton);

      await waitFor(() => {
        expect(screen.getByTestId('create-cutter')).toBeInTheDocument();
      });

      // Click back
      const backButton = screen.getByText('back');
      await user.click(backButton);

      await waitFor(() => {
        expect(screen.getByTestId('debounce-search-input')).toBeInTheDocument();
        expect(screen.queryByTestId('create-cutter')).not.toBeInTheDocument();
      });
    });
  });

  describe('Form Validation and Save', () => {
    it('should disable add button when required fields are empty', async () => {
      const user = userEvent.setup();
      renderComponent();

      // Go to create mode
      const addButton = screen.getByText('add-walk-in-cutter');
      await user.click(addButton);

      await waitFor(() => {
        const modalAddButton = screen.getByText('add-modal-btn');
        expect(modalAddButton).toBeDisabled();
      });
    });

    it('should enable add button when all required fields are filled', async () => {
      const user = userEvent.setup();
      renderComponent();

      // Go to create mode
      const addButton = screen.getByText('add-walk-in-cutter');
      await user.click(addButton);

      await waitFor(() => {
        expect(screen.getByTestId('create-cutter')).toBeInTheDocument();
      });

      // Fill required fields
      const nameInput = screen.getByTestId('cutter-name-input');
      const phoneInput = screen.getByTestId('cutter-phone-input');
      const vehicleInput = screen.getByTestId('vehicle-registration-input');
      const provinceInput = screen.getByTestId('province-registration-input');

      await user.type(nameInput, 'Test Cutter');
      await user.type(phoneInput, '**********');
      await user.type(vehicleInput, 'ABC123');
      await user.type(provinceInput, 'BKK');

      await waitFor(() => {
        const submitButton = screen.getByText('add-modal-btn');
        expect(submitButton).not.toBeDisabled();
      });
    });

    it('should call onAddCutter and close drawer when add button is clicked with valid data', async () => {
      const user = userEvent.setup();
      renderComponent();

      // Go to create mode
      const addButton = screen.getByText('add-walk-in-cutter');
      await user.click(addButton);

      await waitFor(() => {
        expect(screen.getByTestId('create-cutter')).toBeInTheDocument();
      });

      // Fill required fields
      const nameInput = screen.getByTestId('cutter-name-input');
      const phoneInput = screen.getByTestId('cutter-phone-input');
      const vehicleInput = screen.getByTestId('vehicle-registration-input');
      const provinceInput = screen.getByTestId('province-registration-input');

      await user.type(nameInput, 'Test Cutter');
      await user.type(phoneInput, '**********');
      await user.type(vehicleInput, 'ABC123');
      await user.type(provinceInput, 'BKK');

      // Click add
      const submitButton = screen.getByText('add-modal-btn');
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockOnAddCutter).toHaveBeenCalledWith(
          expect.objectContaining({
            name: 'Test Cutter',
            phoneNumber: '**********',
          }),
          expect.objectContaining({
            vehicleRegistrationNumber: 'ABC123',
            provinceRegistrationNumber: 'BKK',
          })
        );
        expect(mockToggle).toHaveBeenCalled();
      });
    });

    it('should disable add button when phone number has validation error', async () => {
      const user = userEvent.setup();
      renderComponent();

      // Go to create mode
      const addButton = screen.getByText('add-walk-in-cutter');
      await user.click(addButton);

      await waitFor(() => {
        expect(screen.getByTestId('create-cutter')).toBeInTheDocument();
      });

      // Fill required fields with invalid phone number
      const nameInput = screen.getByTestId('cutter-name-input');
      const phoneInput = screen.getByTestId('cutter-phone-input');
      const vehicleInput = screen.getByTestId('vehicle-registration-input');
      const provinceInput = screen.getByTestId('province-registration-input');

      await user.type(nameInput, 'Test Cutter');
      await user.type(phoneInput, '123'); // Invalid phone number
      await user.type(vehicleInput, 'ABC123');
      await user.type(provinceInput, 'BKK');

      // Wait for phone number validation error to appear
      await waitFor(() => {
        expect(screen.getByText('invalid-thai-phone-number')).toBeInTheDocument();
      });

      // Add button should be disabled due to phone number error
      await waitFor(() => {
        const submitButton = screen.getByText('add-modal-btn');
        expect(submitButton).toBeDisabled();
      });
    });
  });

  describe('Drawer Close Functionality', () => {
    it('should call toggle when drawer close button is clicked', async () => {
      const user = userEvent.setup();
      renderComponent();

      const closeButton = screen.getByTestId('drawer-close');
      await user.click(closeButton);

      expect(mockToggle).toHaveBeenCalled();
    });

    it('should reset state when drawer is closed', async () => {
      const user = userEvent.setup();
      renderComponent();

      // Go to create mode and fill some data
      const addButton = screen.getByText('add-walk-in-cutter');
      await user.click(addButton);

      await waitFor(() => {
        const nameInput = screen.getByTestId('cutter-name-input');
        user.type(nameInput, 'Test');
      });

      // Close drawer
      const closeButton = screen.getByTestId('drawer-close');
      await user.click(closeButton);

      expect(mockToggle).toHaveBeenCalled();
    });
  });

  describe('Avatar Display', () => {
    it('should display cutter avatars with correct image URLs', () => {
      renderComponent();

      const avatars = screen.getAllByTestId('avatar');
      expect(avatars).toHaveLength(2);
      expect(mockGetImageUrl).toHaveBeenCalledWith('avatar1.jpg');
      expect(mockGetImageUrl).toHaveBeenCalledWith('avatar2.jpg');
    });

    it('should handle empty avatar gracefully', () => {
      const cuttersWithoutAvatar = [
        {
          ...mockCutters[0],
          avatar: undefined,
        },
      ];

      mockUseQuery.mockReturnValue({
        data: { data: cuttersWithoutAvatar },
        isLoading: false,
        isFetching: false,
        error: null,
        isError: false,
        isSuccess: true,
        refetch: jest.fn(),
      } as any);

      renderComponent();

      expect(mockGetImageUrl).toHaveBeenCalledWith('');
    });
  });

  describe('Phone Number Display', () => {
    it('should display phone number with leading zero when it starts with 0', () => {
      renderComponent();

      expect(screen.getByText('**********')).toBeInTheDocument();
      expect(screen.getByText('**********')).toBeInTheDocument();
    });

    it('should add leading zero to phone number when it does not start with 0', () => {
      const cuttersWithoutLeadingZero = [
        {
          ...mockCutters[0],
          phoneNumber: '812345678',
        },
      ];

      mockUseQuery.mockReturnValue({
        data: { data: cuttersWithoutLeadingZero },
        isLoading: false,
        isFetching: false,
        error: null,
        isError: false,
        isSuccess: true,
        refetch: jest.fn(),
      } as any);

      renderComponent();

      expect(screen.getByText('**********')).toBeInTheDocument();
    });
  });

  describe('Name Display', () => {
    it('should display full name when name field is available', () => {
      renderComponent();

      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    it('should display firstName and lastName when name field is falsy but still passes filter', () => {
      // Note: This test demonstrates the display logic, but due to filtering logic,
      // cutters with falsy names won't actually be displayed. This is a potential
      // inconsistency in the component logic.
      const cuttersWithFirstLastName = [
        {
          ...mockCutters[0],
          name: 'Valid Name', // Must have valid name to pass filter
          firstName: 'Jane',
          lastName: 'Smith',
          phoneNumber: '**********',
        },
      ];

      mockUseQuery.mockReturnValue({
        data: { data: cuttersWithFirstLastName },
        isLoading: false,
        isFetching: false,
        error: null,
        isError: false,
        isSuccess: true,
        refetch: jest.fn(),
      } as any);

      renderComponent();

      // Since name is truthy, it will display the name field, not firstName/lastName
      expect(screen.getByText('Valid Name')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should handle query error gracefully', () => {
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        isFetching: false,
        error: new Error('Network error'),
        isError: true,
        isSuccess: false,
        refetch: jest.fn(),
      } as any);

      renderComponent();

      // Should still render the drawer and search input
      expect(screen.getByTestId('drawer')).toBeInTheDocument();
      expect(screen.getByTestId('search-input')).toBeInTheDocument();
    });
  });
});
