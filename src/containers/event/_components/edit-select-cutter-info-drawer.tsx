import { Box, Button } from '@mui/material';
import { useTranslations } from 'next-intl';
import { FC, useMemo, useState, useEffect } from 'react';
import { Drawer } from 'components';
import { Cutter, CutterVehicle } from 'types';
import { CreateCutter } from './create-cutter/create-cutter';
import { ExistedCutter } from './create-cutter/exist-cutter';
import { CreateCutterVehicle } from './create-cutter/create-cutter-vehicle';

interface EditSelectCutterInfoDrawerProps {
  open: boolean;
  toggle: () => void;
  currentCutter: Cutter;
  currentCutterVehicle: CutterVehicle;
  onSave: (cutter: Cutter, cutterVehicle: CutterVehicle) => void;
}

export const EditSelectCutterInfoDrawer: FC<EditSelectCutterInfoDrawerProps> = ({
  open,
  toggle,
  currentCutter,
  currentCutterVehicle,
  onSave,
}) => {
  const commonT = useTranslations('common');
  const receivingTranslation = useTranslations('receive');

  const [selectedCutter, setSelectedCutter] = useState<Cutter>(currentCutter);
  const [selectedCutterVehicle, setSelectedCutterVehicle] = useState<CutterVehicle>(currentCutterVehicle);
  const [hasPhoneNumberError, setHasPhoneNumberError] = useState<boolean>(false);

  useEffect(() => {
    if (open) {
      setSelectedCutter(currentCutter);
      setSelectedCutterVehicle(currentCutterVehicle);
      setHasPhoneNumberError(false);
    }
  }, [open, currentCutter, currentCutterVehicle]);

  const onClose = () => {
    setSelectedCutter(currentCutter);
    setSelectedCutterVehicle(currentCutterVehicle);
    setHasPhoneNumberError(false);
    toggle();
  };

  const handleSave = () => {
    if (enableSaveButton) {
      onSave(selectedCutter, selectedCutterVehicle);
      toggle();
    }
  };

  const drawerTitle = `${commonT('edit')} ${receivingTranslation('cutter-information')}`;

  const enableSaveButton = useMemo(() => {
    return Boolean(
      selectedCutter.name.trim() &&
        selectedCutter.phoneNumber &&
        !hasPhoneNumberError &&
        selectedCutterVehicle.vehicleRegistrationNumber.trim() &&
        selectedCutterVehicle.provinceRegistrationNumber
    );
  }, [selectedCutter, selectedCutterVehicle, hasPhoneNumberError]);

  const footerElement = (
    <Box
      sx={{
        px: 2,
        py: 1.5,
        borderTop: 1,
        borderColor: 'divider',
        display: 'flex',
        justifyContent: 'space-between',
        gap: 1,
      }}
    >
      <Button fullWidth variant="outlined" onClick={onClose}>
        {commonT('cancel-modal-btn')}
      </Button>
      <Button fullWidth variant="contained" onClick={handleSave} disabled={!enableSaveButton}>
        {commonT('save-modal-btn')}
      </Button>
    </Box>
  );

  const hasProfileId = Boolean(selectedCutter.profileId);

  return (
    <Drawer
      anchor="right"
      drawerTitle={drawerTitle}
      open={open}
      onClose={onClose}
      hasActionBtn={false}
      footerElement={footerElement}
    >
      <Box
        component="div"
        sx={{ width: '100%', p: '24px 16px', display: 'flex', flexDirection: 'column', gap: '16px' }}
      >
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          {hasProfileId ? (
            <ExistedCutter cutter={selectedCutter} />
          ) : (
            <CreateCutter
              cutter={selectedCutter}
              setCutter={(cutter: Cutter) => {
                setSelectedCutter((prev) => ({
                  ...prev,
                  ...cutter,
                }));
              }}
              onPhoneNumberErrorChange={setHasPhoneNumberError}
            />
          )}
          <CreateCutterVehicle
            vehicle={selectedCutterVehicle}
            setVehicle={(vehicle: CutterVehicle) => {
              setSelectedCutterVehicle((prev) => ({
                ...prev,
                ...vehicle,
              }));
            }}
          />
        </Box>
      </Box>
    </Drawer>
  );
};
