import React, { useMemo } from 'react';
import { Typography } from '@mui/material';
import { CustomDatePicker } from 'components/date-picker/custom-date-picker';
import { useTranslations } from 'next-intl';
import dayjs from 'dayjs';

export interface FlowerBloomingDayInputProps {
  value?: string | number | null;
  disabled?: boolean;
}

export const FlowerBloomingDayInput: React.FC<FlowerBloomingDayInputProps> = React.memo(({
  value,
  disabled,
}) => {
  const receiveTranslation = useTranslations('receive');

  const formattedBloomDay = useMemo(() => {
    if (value) {
      const timestamp = typeof value === 'string' ? Number.parseInt(value, 10) : value;

      if (!isNaN(timestamp)) {
        return dayjs(timestamp * 1000);
      }
    }
    return null;
  }, [value]);

  return (
    <>
      <Typography sx={{ fontSize: '18px' }} variant="caption">
        {receiveTranslation('flower-blooming-day')}{' '}
        <Typography variant="caption" color="error">
          *
        </Typography>
      </Typography>
      <CustomDatePicker
        value={formattedBloomDay}
        disabled={disabled}
        slotProps={{
          textField: {
            variant: 'outlined',
            fullWidth: true,
            margin: 'normal',
          },
        }}
        onChange={() => {}}
        sx={{
          mb: 2,
          '& .MuiOutlinedInput-root': {
            bgcolor: 'white',
          },
        }}
      />
    </>
  );
});

FlowerBloomingDayInput.displayName = 'FlowerBloomingDayInput';
