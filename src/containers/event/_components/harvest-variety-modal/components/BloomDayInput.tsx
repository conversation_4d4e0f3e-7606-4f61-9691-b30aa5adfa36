import React from 'react';
import { Typography } from '@mui/material';
import { CustomDatePicker } from 'components/date-picker/custom-date-picker';
import { useTranslations } from 'next-intl';
import dayjs from 'dayjs';
import { theme } from 'styles/theme';
import { BloomDayInputProps } from '../types';
import { BloomDayWarning } from './BloomDayWarning';

export const BloomDayInput: React.FC<BloomDayInputProps> = React.memo(({
  variety,
  value,
  cuttingDay,
  onChange,
  disabled = false,
  locale,
}) => {
  const receiveTranslation = useTranslations('receive');

  const selectedDate = value ? dayjs(value * 1000) : null;
  const warningDayRange = cuttingDay?.subtract(variety?.flowerBloomingDuration ?? 0, 'day');
  
  const isInWarningRange = selectedDate &&
    warningDayRange &&
    selectedDate.isAfter(warningDayRange) &&
    selectedDate.isBefore(cuttingDay);

  const handleChange = (date: dayjs.Dayjs | null) => {
    onChange(date ? date.startOf('day').unix() : null);
  };

  return (
    <>
      <Typography sx={{ fontSize: '18px' }} variant="caption">
        {receiveTranslation('flower-blooming-day')}{' '}
        <Typography variant="caption" color="error">
          *
        </Typography>
      </Typography>
      <CustomDatePicker
        value={selectedDate}
        slotProps={{
          textField: {
            variant: 'outlined',
            fullWidth: true,
            margin: 'normal',
            helperText: isInWarningRange ? (
              <BloomDayWarning
                variety={variety}
                selectedDate={selectedDate}
                cuttingDay={cuttingDay ?? null}
                locale={locale}
              />
            ) : undefined,
            FormHelperTextProps: {
              sx: {
                color: theme.palette.customColors.waiting,
                fontWeight: 'bold',
              },
            },
          },
        }}
        maxDate={cuttingDay || dayjs()}
        onChange={handleChange}
        disabled={disabled}
        sx={{
          mb: 2,
          '& .MuiPickersInputBase-root': {
            bgcolor: 'white',
          },
        }}
      />
    </>
  );
});

BloomDayInput.displayName = 'BloomDayInput';
