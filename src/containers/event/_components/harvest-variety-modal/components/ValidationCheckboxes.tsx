import React from 'react';
import { Box, Checkbox, FormControlLabel } from '@mui/material';
import { useTranslations } from 'next-intl';

export interface ValidationCheckboxesProps {
  confirmReceiveHarvest: boolean;
  awareUndoAction: boolean;
  onConfirmReceiveHarvestChange: (checked: boolean) => void;
  onAwareUndoActionChange: (checked: boolean) => void;
}

export const ValidationCheckboxes: React.FC<ValidationCheckboxesProps> = React.memo(({
  confirmReceiveHarvest,
  awareUndoAction,
  onConfirmReceiveHarvestChange,
  onAwareUndoActionChange,
}) => {
  const receiveTranslation = useTranslations('receive');

  const handleConfirmReceiveHarvestChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onConfirmReceiveHarvestChange(e.target.checked);
  };

  const handleAwareUndoActionChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onAwareUndoActionChange(e.target.checked);
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'start', justifyContent: 'center' }}>
      <FormControlLabel
        control={
          <Checkbox
            checked={confirmReceiveHarvest}
            onChange={handleConfirmReceiveHarvestChange}
            name="confirm-receive-harvest"
            color="primary"
            size="small"
          />
        }
        label={receiveTranslation('confirm-receive-harvest')}
        sx={{
          alignItems: 'center',
          '& .MuiFormControlLabel-label': {
            fontSize: '14px',
          },
          '& .MuiCheckbox-root': {
            p: 0,
          },
        }}
      />

      <FormControlLabel
        control={
          <Checkbox
            checked={awareUndoAction}
            onChange={handleAwareUndoActionChange}
            name="aware-undo-action"
            color="primary"
            size="small"
          />
        }
        label={receiveTranslation('aware-undo-action')}
        sx={{
          alignItems: 'center',
          '& .MuiFormControlLabel-label': {
            fontSize: '14px',
          },
          '& .MuiCheckbox-root': {
            p: 0,
          },
        }}
      />
    </Box>
  );
});

ValidationCheckboxes.displayName = 'ValidationCheckboxes';
