import React from 'react';
import { useTranslations } from 'next-intl';
import { BloomDayWarningProps } from '../types';

export const BloomDayWarning: React.FC<BloomDayWarningProps> = React.memo(({
  variety,
  selectedDate,
  cuttingDay,
  locale,
}) => {
  const receiveTranslation = useTranslations('receive');

  if (!selectedDate || !cuttingDay) {
    return null;
  }

  const warningDayRange = cuttingDay.subtract(variety?.flowerBloomingDuration ?? 0, 'day');
  const isInWarningRange = selectedDate.isAfter(warningDayRange) && selectedDate.isBefore(cuttingDay);

  if (!isInWarningRange) {
    return null;
  }

  return (
    <>
      {receiveTranslation('warning-varieties', {
        varietyName: variety.label[locale as keyof typeof variety.label] as string,
        day: variety?.flowerBloomingDuration ?? 0,
      })}
    </>
  );
});

BloomDayWarning.displayName = 'BloomDayWarning';
