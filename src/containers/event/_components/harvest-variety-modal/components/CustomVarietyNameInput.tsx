import React from 'react';
import { TextField, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';

export interface CustomVarietyNameInputProps {
  value: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  disabled?: boolean;
  error?: string;
}

export const CustomVarietyNameInput: React.FC<CustomVarietyNameInputProps> = React.memo(({
  value,
  onChange,
  onBlur,
  disabled = false,
  error,
}) => {
  const commonT = useTranslations('common');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    onChange(e.target.value);
  };

  return (
    <>
      <Typography sx={{ fontSize: '18px' }} variant="caption">
        {commonT('variety-name')}{' '}
        <Typography variant="caption" color="error">
          *
        </Typography>
      </Typography>
      <TextField
        fullWidth
        variant="outlined"
        margin="normal"
        value={value || ''}
        onChange={handleChange}
        onBlur={onBlur}
        error={!!error}
        helperText={error}
        disabled={disabled}
        placeholder="Enter variety name (optional)"
        sx={{
          mb: 2,
          '& .MuiOutlinedInput-root': {
            bgcolor: 'white',
          },
        }}
      />
    </>
  );
});

CustomVarietyNameInput.displayName = 'CustomVarietyNameInput';
