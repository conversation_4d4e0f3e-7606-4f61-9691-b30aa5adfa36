import React from 'react';
import { TextField, Typography } from '@mui/material';
import { useMasterDataStore } from 'store/useMaterDataStore';
import { formatNumberWithCommas, isValidNumberInput } from 'utils';
import {
  cleanDecimalInput,
  hasLeadingZero,
  normalizeValue,
  processDecimalValue,
  processIntegerValue,
} from 'utils/input-grade';
import { DurianGrade, DurianVariety } from 'types';

export interface GradeWeightInputProps {
  variety: DurianVariety;
  grade: DurianGrade;
  value?: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  disabled?: boolean;
  error?: string;
  isSubmitting?: boolean;
}

export const GradeWeightInput: React.FC<GradeWeightInputProps> = React.memo(({
  grade,
  value,
  onChange,
  onBlur,
  disabled,
  error,
  isSubmitting,
}) => {
  const { getGradeLabel } = useMasterDataStore();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value.replace(/,/g, '');

    // Early return for empty value
    if (!rawValue) {
      onChange('');
      return;
    }

    // Early return for invalid input or leading zeros
    if (!isValidNumberInput(rawValue) || hasLeadingZero(rawValue)) {
      return;
    }

    // Clean and normalize the value
    const cleanValue = cleanDecimalInput(rawValue);
    const processedValue = normalizeValue(cleanValue);

    // Process based on whether it has a decimal point
    const hasDot = processedValue.includes('.');
    const finalValue = hasDot ? processDecimalValue(processedValue) : processIntegerValue(processedValue);

    // Early return if validation failed
    if (finalValue === null) {
      return;
    }

    // Update field value
    onChange(finalValue);
  };

  return (
    <>
      <Typography sx={{ fontSize: '18px', mb: 1 }} variant="caption">
        {getGradeLabel(grade.id)}{' '}
      </Typography>
      <TextField
        fullWidth
        value={formatNumberWithCommas(value)}
        variant="outlined"
        margin="normal"
        error={!!error}
        helperText={error}
        onChange={handleChange}
        onBlur={onBlur}
        disabled={isSubmitting || disabled}
        sx={{
          mb: 2,
          '& .MuiOutlinedInput-root': {
            bgcolor: 'white',
          },
        }}
      />
    </>
  );
});

GradeWeightInput.displayName = 'GradeWeightInput';
