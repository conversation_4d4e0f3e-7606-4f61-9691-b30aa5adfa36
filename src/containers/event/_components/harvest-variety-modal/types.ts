import { Dayjs } from 'dayjs';
import { DurianVariety, ReceiveUpdatePayload, UpdateVarietyRequest } from 'types';

// Modal mode enumeration
export enum ModalMode {
  EDIT = 'edit',           // Update existing harvest data
  CONFIRM = 'confirm',     // Confirm new harvest data with checkboxes
  CREATE = 'create'        // Create new variety data with bloom days
}

// Base form data types
export interface BaseFormData {
  weights: Record<string, string | undefined>;
  customVarietyName?: string;
}

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface EditModeFormData extends BaseFormData {}

export interface ConfirmModeFormData extends BaseFormData {
  confirmReceiveHarvest: boolean;
  awareUndoAction: boolean;
}

export interface CreateModeFormData extends BaseFormData {
  varietyBloomDays: Record<string, number>;
}

export type FormData = EditModeFormData | ConfirmModeFormData | CreateModeFormData;

// Main modal props interface
export interface HarvestVarietyModalProps {
  // Common props
  open: boolean;
  onClose?: () => void;
  initialWeights?: Record<string, string>;
  initialCustomName?: string;
  varietyBloomDays?: Record<string, number>;
  
  // Mode specification
  mode: ModalMode;
  
  // Edit/Confirm mode specific props
  onSave?: (data: ReceiveUpdatePayload) => Promise<void>;
  varieties?: DurianVariety[];
  useProvidedVarieties?: boolean;
  editableVarietyIds?: Set<string>;
  
  // Create mode specific props
  onSaveCreate?: (data: ReceiveUpdatePayload, varietyData: DurianVariety[]) => void;
  cuttingDay?: Dayjs;
}

// Backward compatibility props
export interface ReceiveHarvestModalCompatProps {
  open: boolean;
  onClose?: () => void;
  onSave?: (data: ReceiveUpdatePayload) => Promise<void>;
  varieties?: DurianVariety[];
  initialWeights?: Record<string, string>;
  initialCustomName?: string;
  useProvidedVarieties?: boolean;
  editableVarietyIds?: Set<string>;
  varietyBloomDays?: Record<string, number>;
  isEditReceiving: boolean;
}

export interface CreateVarietiesModalCompatProps {
  open: boolean;
  onClose?: () => void;
  onSave?: (data: ReceiveUpdatePayload, varietyData: DurianVariety[]) => void;
  initialWeights?: Record<string, string>;
  initialCustomName?: string;
  varietyBloomDays?: Record<string, number>;
  cuttingDay?: Dayjs;
}

// Enhanced component props
export interface VarietySelectionPanelProps {
  varieties: DurianVariety[];
  selectedVariety: string | null;
  editableVarietyIds: Set<string>;
  varietiesWithData: string[];
  allZeroOrNegativeWarning: boolean;
  mode: ModalMode;
  onVarietySelect: (varietyId: string) => void;
  checkVarietyHasData: (varietyId: string) => boolean;
  isVarietyEditable: (varietyId: string) => boolean;
}

export interface GradeInputPanelProps {
  selectedVariety: string | null;
  varieties: DurianVariety[];
  varietyBloomDays: Record<string, number>;
  allZeroOrNegativeWarning: boolean;
  allZeroOrNegativeWarningMessage: string;
  isSubmitting: boolean;
  editableVarietyIds: Set<string>;
  internalCustomName: string;
  mode: ModalMode;
  cuttingDay?: Dayjs;
  onCustomVarietyNameChange: (value: string) => void;
  onCustomVarietyNameBlur: () => void;
  onBloomDayChange?: (varietyId: string, timestamp: number | null) => void;
}

export interface BloomDayInputProps {
  variety: DurianVariety;
  value: number | null;
  cuttingDay?: Dayjs;
  onChange: (timestamp: number | null) => void;
  disabled?: boolean;
  locale: string;
}

export interface BloomDayWarningProps {
  variety: DurianVariety;
  selectedDate: Dayjs | null;
  cuttingDay: Dayjs | null;
  locale: string;
}

export interface ModalActionsProps {
  mode: ModalMode;
  isButtonEnabled: boolean;
  isLoading: boolean;
  isSubmitting: boolean;
  onClose: () => void;
  onSubmit: () => void;
  showCheckboxes: boolean;
  checkboxesProps?: ValidationCheckboxesProps;
}

export interface ValidationCheckboxesProps {
  confirmReceiveHarvest: boolean;
  awareUndoAction: boolean;
  onConfirmReceiveHarvestChange: (checked: boolean) => void;
  onAwareUndoActionChange: (checked: boolean) => void;
}

// Modal configuration types
export interface ModalConfig {
  title: string;
  buttonText: string;
  buttonId: string;
  showCheckboxes: boolean;
  showBloomDayInputs: boolean;
  showBloomDayWarnings: boolean;
  requireBloomDays: boolean;
  filterEditableVarieties: boolean;
  showZeroNegativeWarning: boolean;
}

// Validation types
export interface ValidationConfig {
  requireCheckboxes: boolean;
  requireBloomDays: boolean;
  validateCustomVarietyName: boolean;
  showZeroNegativeWarning: boolean;
}

export interface ButtonEnableConfig {
  baseConditions: string[];
  modeSpecificConditions: string[];
}

// Hook types
export interface UseModalConfigResult {
  config: ModalConfig;
  validationConfig: ValidationConfig;
  buttonConfig: ButtonEnableConfig;
}

export interface UseFormSchemaResult {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  schema: any; // Zod schema type - keeping as any for zodResolver compatibility
  defaultValues: FormData;
}

export interface UseButtonStateResult {
  isEnabled: boolean;
  reasons: string[];
}

// Utility types
export interface ModeSpecificHandlers {
  onSubmit: (data: FormData) => Promise<void>;
  preparePayload: (data: FormData) => ReceiveUpdatePayload | { update: { varieties: UpdateVarietyRequest[] } };
  validateForm: (data: FormData) => boolean;
}

export interface VarietyMapEntry {
  id: string;
  grades: Array<{ id: string; weight: number; name?: string }>;
  flowerBloomingDay: number;
  name?: string;
}

export type VarietiesMap = Record<string, VarietyMapEntry>;

// Event handler types
export type ModeChangeHandler = (mode: ModalMode) => void;
export type BloomDayChangeHandler = (varietyId: string, timestamp: number | null) => void;
export type SubmitHandler = (data: FormData) => Promise<void>;

// Component state types
export interface ModalState {
  selectedVariety: string | null;
  varietiesWithData: string[];
  isSubmitting: boolean;
  submitError: string | null;
  allZeroOrNegativeWarning: boolean;
  internalCustomName: string;
  internalBloomDays: Record<string, number>;
}

// Configuration constants
export const MODE_CONFIGS: Record<ModalMode, ModalConfig> = {
  [ModalMode.EDIT]: {
    title: 'varieties-update',
    buttonText: 'save-modal-btn',
    buttonId: 'save-modal-btn',
    showCheckboxes: false,
    showBloomDayInputs: false,
    showBloomDayWarnings: false,
    requireBloomDays: false,
    filterEditableVarieties: true,
    showZeroNegativeWarning: true,
  },
  [ModalMode.CONFIRM]: {
    title: 'confirm-harvest-data',
    buttonText: 'confirm-modal-btn',
    buttonId: 'confirm-modal-btn',
    showCheckboxes: true,
    showBloomDayInputs: false,
    showBloomDayWarnings: false,
    requireBloomDays: false,
    filterEditableVarieties: true,
    showZeroNegativeWarning: true,
  },
  [ModalMode.CREATE]: {
    title: 'specify-durian-weight-by-variety',
    buttonText: 'save-modal-btn',
    buttonId: 'save-modal-btn',
    showCheckboxes: false,
    showBloomDayInputs: true,
    showBloomDayWarnings: true,
    requireBloomDays: true,
    filterEditableVarieties: false,
    showZeroNegativeWarning: false,
  },
};

export const VALIDATION_CONFIGS: Record<ModalMode, ValidationConfig> = {
  [ModalMode.EDIT]: {
    requireCheckboxes: false,
    requireBloomDays: false,
    validateCustomVarietyName: false,
    showZeroNegativeWarning: true,
  },
  [ModalMode.CONFIRM]: {
    requireCheckboxes: true,
    requireBloomDays: false,
    validateCustomVarietyName: false,
    showZeroNegativeWarning: true,
  },
  [ModalMode.CREATE]: {
    requireCheckboxes: false,
    requireBloomDays: true,
    validateCustomVarietyName: true,
    showZeroNegativeWarning: false,
  },
};
