/* eslint-disable @typescript-eslint/no-require-imports */
import React from 'react';
import { render, screen } from '@testing-library/react';
import { ReceiveHarvestModalCompat, CreateVarietiesModalCompat } from '../compat';
import { ThemeProvider } from '@mui/material/styles';
import { theme } from 'styles/theme';
import dayjs from 'dayjs';

// Mock the main unified component
jest.mock('../HarvestVarietyModal', () => ({
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  HarvestVarietyModal: ({ mode, ...props }: { mode: string; [key: string]: unknown }) => (
    <div data-testid="harvest-variety-modal" data-mode={mode}>
      Unified Modal - Mode: {mode}
    </div>
  ),
}));

jest.mock('next-intl', () => ({
  useTranslations: jest.fn(() => (key: string) => key),
}));

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider theme={theme}>{children}</ThemeProvider>
);

const mockOnClose = jest.fn();
const mockOnSave = jest.fn();
const mockOnSaveCreate = jest.fn();

const mockVarieties = [
  {
    id: '1',
    value: 'variety1',
    label: { th: 'พันธุ์ 1', en: 'Variety 1' },
    flowerBloomingDay: dayjs().unix(),
    grades: [
      { id: 'A', value: 'A', label: { th: 'เกรด A', en: 'Grade A' }, weight: 0 },
    ],
  },
];

describe('Backward Compatibility Wrappers', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('ReceiveHarvestModalCompat', () => {
    const baseV2Props = {
      open: true,
      onClose: mockOnClose,
      onSave: mockOnSave,
      varieties: mockVarieties,
      initialWeights: {},
      initialCustomName: '',
      useProvidedVarieties: true,
      editableVarietyIds: new Set(['1']),
      varietyBloomDays: { '1': dayjs().unix() },
    };

    it('renders unified component with EDIT mode when isEditReceiving is true', () => {
      render(
        <TestWrapper>
          <ReceiveHarvestModalCompat
            {...baseV2Props}
            isEditReceiving={true}
          />
        </TestWrapper>
      );

      const unifiedComponent = screen.getByTestId('harvest-variety-modal');
      expect(unifiedComponent).toBeInTheDocument();
      expect(unifiedComponent).toHaveAttribute('data-mode', 'edit');
      expect(screen.getByText('Unified Modal - Mode: edit')).toBeInTheDocument();
    });

    it('renders unified component with CONFIRM mode when isEditReceiving is false', () => {
      render(
        <TestWrapper>
          <ReceiveHarvestModalCompat
            {...baseV2Props}
            isEditReceiving={false}
          />
        </TestWrapper>
      );

      const unifiedComponent = screen.getByTestId('harvest-variety-modal');
      expect(unifiedComponent).toBeInTheDocument();
      expect(unifiedComponent).toHaveAttribute('data-mode', 'confirm');
      expect(screen.getByText('Unified Modal - Mode: confirm')).toBeInTheDocument();
    });

    it('passes all props correctly to unified component', () => {
      const { container } = render(
        <TestWrapper>
          <ReceiveHarvestModalCompat
            {...baseV2Props}
            isEditReceiving={true}
          />
        </TestWrapper>
      );

      // The unified component should be rendered
      expect(container.querySelector('[data-testid="harvest-variety-modal"]')).toBeInTheDocument();
    });

    it('handles missing optional props gracefully', () => {
      const minimalProps = {
        open: true,
        isEditReceiving: true,
      };

      render(
        <TestWrapper>
          <ReceiveHarvestModalCompat {...minimalProps} />
        </TestWrapper>
      );

      const unifiedComponent = screen.getByTestId('harvest-variety-modal');
      expect(unifiedComponent).toBeInTheDocument();
      expect(unifiedComponent).toHaveAttribute('data-mode', 'edit');
    });
  });

  describe('CreateVarietiesModalCompat', () => {
    const baseCreateProps = {
      open: true,
      onClose: mockOnClose,
      onSave: mockOnSaveCreate,
      initialWeights: {},
      initialCustomName: '',
      varietyBloomDays: { '1': dayjs().unix() },
      cuttingDay: dayjs('2024-01-15'),
    };

    it('renders unified component with CREATE mode', () => {
      render(
        <TestWrapper>
          <CreateVarietiesModalCompat {...baseCreateProps} />
        </TestWrapper>
      );

      const unifiedComponent = screen.getByTestId('harvest-variety-modal');
      expect(unifiedComponent).toBeInTheDocument();
      expect(unifiedComponent).toHaveAttribute('data-mode', 'create');
      expect(screen.getByText('Unified Modal - Mode: create')).toBeInTheDocument();
    });

    it('passes all props correctly to unified component', () => {
      const { container } = render(
        <TestWrapper>
          <CreateVarietiesModalCompat {...baseCreateProps} />
        </TestWrapper>
      );

      // The unified component should be rendered
      expect(container.querySelector('[data-testid="harvest-variety-modal"]')).toBeInTheDocument();
    });

    it('handles missing optional props gracefully', () => {
      const minimalProps = {
        open: true,
      };

      render(
        <TestWrapper>
          <CreateVarietiesModalCompat {...minimalProps} />
        </TestWrapper>
      );

      const unifiedComponent = screen.getByTestId('harvest-variety-modal');
      expect(unifiedComponent).toBeInTheDocument();
      expect(unifiedComponent).toHaveAttribute('data-mode', 'create');
    });

    it('maps onSave prop to onSaveCreate in unified component', () => {
      // This test verifies that the onSave prop from CreateVarietiesModal
      // is correctly mapped to onSaveCreate in the unified component
      render(
        <TestWrapper>
          <CreateVarietiesModalCompat
            {...baseCreateProps}
            onSave={mockOnSaveCreate}
          />
        </TestWrapper>
      );

      const unifiedComponent = screen.getByTestId('harvest-variety-modal');
      expect(unifiedComponent).toBeInTheDocument();
      expect(unifiedComponent).toHaveAttribute('data-mode', 'create');
    });
  });

  describe('Props Mapping Verification', () => {
    it('ReceiveHarvestModalCompat correctly maps isEditReceiving to mode', () => {
      const { rerender } = render(
        <TestWrapper>
          <ReceiveHarvestModalCompat
            open={true}
            isEditReceiving={true}
          />
        </TestWrapper>
      );

      expect(screen.getByText('Unified Modal - Mode: edit')).toBeInTheDocument();

      rerender(
        <TestWrapper>
          <ReceiveHarvestModalCompat
            open={true}
            isEditReceiving={false}
          />
        </TestWrapper>
      );

      expect(screen.getByText('Unified Modal - Mode: confirm')).toBeInTheDocument();
    });

    it('CreateVarietiesModalCompat always uses CREATE mode', () => {
      render(
        <TestWrapper>
          <CreateVarietiesModalCompat open={true} />
        </TestWrapper>
      );

      expect(screen.getByText('Unified Modal - Mode: create')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('ReceiveHarvestModalCompat handles undefined isEditReceiving gracefully', () => {
      render(
        <TestWrapper>
          <ReceiveHarvestModalCompat
            open={true}
            isEditReceiving={undefined as unknown as boolean}
          />
        </TestWrapper>
      );

      // Should default to confirm mode when isEditReceiving is falsy
      expect(screen.getByText('Unified Modal - Mode: confirm')).toBeInTheDocument();
    });

    it('CreateVarietiesModalCompat handles missing props gracefully', () => {
      render(
        <TestWrapper>
          <CreateVarietiesModalCompat open={true} />
        </TestWrapper>
      );

      expect(screen.getByText('Unified Modal - Mode: create')).toBeInTheDocument();
    });
  });
});
