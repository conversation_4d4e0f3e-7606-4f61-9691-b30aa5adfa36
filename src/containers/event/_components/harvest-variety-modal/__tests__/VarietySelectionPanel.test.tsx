import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { VarietySelectionPanel } from '../VarietySelectionPanel';
import { ModalMode } from '../types';
import { ThemeProvider } from '@mui/material/styles';
import { theme } from 'styles/theme';
import dayjs from 'dayjs';

jest.mock('next-intl', () => ({
  useTranslations: jest.fn(() => (key: string) => key),
}));

jest.mock('store/useMaterDataStore', () => ({
  useMasterDataStore: () => ({
    getVarietyLabel: (id: string) => `Variety ${id}`,
  }),
}));

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider theme={theme}>{children}</ThemeProvider>
);

const mockVarieties = [
  {
    id: '1',
    value: 'variety1',
    label: { th: 'พันธุ์ 1', en: 'Variety 1' },
    flowerBloomingDay: dayjs().unix(),
    grades: [
      { id: 'A', value: 'A', label: { th: 'เกรด A', en: 'Grade A' }, weight: 0 },
    ],
  },
  {
    id: '2',
    value: 'variety2',
    label: { th: 'พันธุ์ 2', en: 'Variety 2' },
    flowerBloomingDay: dayjs().unix(),
    grades: [
      { id: 'B', value: 'B', label: { th: 'เกรด B', en: 'Grade B' }, weight: 0 },
    ],
  },
  {
    id: '3',
    value: 'other',
    label: { th: 'อื่นๆ', en: 'Other' },
    flowerBloomingDay: dayjs().unix(),
    grades: [
      { id: 'D', value: 'D', label: { th: 'เกรด D', en: 'Grade D' }, weight: 0 },
    ],
  },
];

const mockOnVarietySelect = jest.fn();
const mockCheckVarietyHasData = jest.fn();
const mockIsVarietyEditable = jest.fn();

const baseProps = {
  varieties: mockVarieties,
  selectedVariety: '1',
  editableVarietyIds: new Set(['1', '2']),
  varietiesWithData: ['1'],
  allZeroOrNegativeWarning: false,
  onVarietySelect: mockOnVarietySelect,
  checkVarietyHasData: mockCheckVarietyHasData,
  isVarietyEditable: mockIsVarietyEditable,
};

describe('VarietySelectionPanel', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockIsVarietyEditable.mockImplementation((id: string) => baseProps.editableVarietyIds.has(id));
    mockCheckVarietyHasData.mockImplementation((id: string) => baseProps.varietiesWithData.includes(id));
  });

  describe('CREATE Mode', () => {
    const createModeProps = {
      ...baseProps,
      mode: ModalMode.CREATE,
    };

    it('shows all varieties in create mode', () => {
      render(
        <TestWrapper>
          <VarietySelectionPanel {...createModeProps} />
        </TestWrapper>
      );

      expect(screen.getByText('Variety 1')).toBeInTheDocument();
      expect(screen.getByText('Variety 2')).toBeInTheDocument();
      expect(screen.getByText('Variety 3')).toBeInTheDocument();
    });

    it('allows clicking on all varieties in create mode', async () => {
      const user = userEvent.setup();
      render(
        <TestWrapper>
          <VarietySelectionPanel {...createModeProps} />
        </TestWrapper>
      );

      const variety2Item = screen.getByText('Variety 2');
      await user.click(variety2Item);

      expect(mockOnVarietySelect).toHaveBeenCalledWith('2');
    });

    it('ignores zero/negative warning in create mode', async () => {
      const user = userEvent.setup();
      const propsWithWarning = {
        ...createModeProps,
        allZeroOrNegativeWarning: true,
      };

      render(
        <TestWrapper>
          <VarietySelectionPanel {...propsWithWarning} />
        </TestWrapper>
      );

      const variety2Item = screen.getByText('Variety 2');
      await user.click(variety2Item);

      // Should still allow clicking despite warning
      expect(mockOnVarietySelect).toHaveBeenCalledWith('2');
    });

    it('applies correct styling for all varieties in create mode', () => {
      render(
        <TestWrapper>
          <VarietySelectionPanel {...createModeProps} />
        </TestWrapper>
      );

      const variety1Item = screen.getByText('Variety 1').closest('li');
      const variety2Item = screen.getByText('Variety 2').closest('li');
      const variety3Item = screen.getByText('Variety 3').closest('li');

      // All varieties should be visible and clickable
      expect(variety1Item).not.toHaveStyle('display: none');
      expect(variety2Item).not.toHaveStyle('display: none');
      expect(variety3Item).not.toHaveStyle('display: none');

      expect(variety1Item).toHaveStyle('opacity: 1');
      expect(variety2Item).toHaveStyle('opacity: 1');
      expect(variety3Item).toHaveStyle('opacity: 1');
    });
  });

  describe('EDIT Mode', () => {
    const editModeProps = {
      ...baseProps,
      mode: ModalMode.EDIT,
    };

    it('shows only editable varieties in edit mode', () => {
      render(
        <TestWrapper>
          <VarietySelectionPanel {...editModeProps} />
        </TestWrapper>
      );

      expect(screen.getByText('Variety 1')).toBeInTheDocument();
      expect(screen.getByText('Variety 2')).toBeInTheDocument();
      
      // Variety 3 should be hidden (display: none) since it's not in editableVarietyIds
      const variety3Item = screen.queryByText('Variety 3')?.closest('li');
      expect(variety3Item).toHaveStyle('display: none');
    });

    it('prevents clicking when zero/negative warning is active in edit mode', async () => {
      const user = userEvent.setup();
      const propsWithWarning = {
        ...editModeProps,
        allZeroOrNegativeWarning: true,
      };

      render(
        <TestWrapper>
          <VarietySelectionPanel {...propsWithWarning} />
        </TestWrapper>
      );

      const variety2Item = screen.getByText('Variety 2');
      await user.click(variety2Item);

      // Should not call onVarietySelect due to warning
      expect(mockOnVarietySelect).not.toHaveBeenCalled();
    });

    it('allows clicking on editable varieties when no warning in edit mode', async () => {
      const user = userEvent.setup();
      render(
        <TestWrapper>
          <VarietySelectionPanel {...editModeProps} />
        </TestWrapper>
      );

      const variety2Item = screen.getByText('Variety 2');
      await user.click(variety2Item);

      expect(mockOnVarietySelect).toHaveBeenCalledWith('2');
    });

    it('prevents clicking on non-editable varieties in edit mode', () => {
      mockIsVarietyEditable.mockImplementation((id: string) => id === '1');
      
      render(
        <TestWrapper>
          <VarietySelectionPanel {...editModeProps} />
        </TestWrapper>
      );

      const variety2Item = screen.getByText('Variety 2').closest('li');
      
      // Should have pointer-events: none for non-editable variety
      expect(variety2Item).toHaveStyle('pointer-events: none');
      expect(mockOnVarietySelect).not.toHaveBeenCalled();
    });
  });

  describe('CONFIRM Mode', () => {
    const confirmModeProps = {
      ...baseProps,
      mode: ModalMode.CONFIRM,
    };

    it('behaves like edit mode for variety visibility', () => {
      render(
        <TestWrapper>
          <VarietySelectionPanel {...confirmModeProps} />
        </TestWrapper>
      );

      expect(screen.getByText('Variety 1')).toBeInTheDocument();
      expect(screen.getByText('Variety 2')).toBeInTheDocument();
      
      // Variety 3 should be hidden since it's not editable
      const variety3Item = screen.queryByText('Variety 3')?.closest('li');
      expect(variety3Item).toHaveStyle('display: none');
    });

    it('prevents clicking when zero/negative warning is active in confirm mode', async () => {
      const user = userEvent.setup();
      const propsWithWarning = {
        ...confirmModeProps,
        allZeroOrNegativeWarning: true,
      };

      render(
        <TestWrapper>
          <VarietySelectionPanel {...propsWithWarning} />
        </TestWrapper>
      );

      const variety2Item = screen.getByText('Variety 2');
      await user.click(variety2Item);

      expect(mockOnVarietySelect).not.toHaveBeenCalled();
    });
  });

  describe('Common Functionality', () => {
    it('renders varieties list with correct titles', () => {
      render(
        <TestWrapper>
          <VarietySelectionPanel {...baseProps} mode={ModalMode.CREATE} />
        </TestWrapper>
      );

      expect(screen.getByText('varieties')).toBeInTheDocument();
      expect(screen.getByText('varieties-description')).toBeInTheDocument();
    });

    it('shows check icon for varieties with data', () => {
      render(
        <TestWrapper>
          <VarietySelectionPanel {...baseProps} mode={ModalMode.CREATE} />
        </TestWrapper>
      );

      // Variety 1 has data, should show check icon
      const variety1Item = screen.getByText('Variety 1').closest('li');
      expect(variety1Item?.querySelector('[data-testid="CheckIcon"]')).toBeInTheDocument();

      // Variety 2 has no data, should not show check icon
      const variety2Item = screen.getByText('Variety 2').closest('li');
      expect(variety2Item?.querySelector('[data-testid="CheckIcon"]')).not.toBeInTheDocument();
    });

    it('applies correct styling for selected variety', () => {
      render(
        <TestWrapper>
          <VarietySelectionPanel {...baseProps} mode={ModalMode.CREATE} selectedVariety="1" />
        </TestWrapper>
      );

      const selectedVarietyItem = screen.getByText('Variety 1').closest('li');
      expect(selectedVarietyItem).toHaveStyle(`background-color: ${theme.palette.customColors.lightGray}`);
    });

    it('renders empty list when no varieties provided', () => {
      render(
        <TestWrapper>
          <VarietySelectionPanel {...baseProps} mode={ModalMode.CREATE} varieties={[]} />
        </TestWrapper>
      );

      expect(screen.getByText('varieties')).toBeInTheDocument();
      expect(screen.queryByText('Variety 1')).not.toBeInTheDocument();
    });

    it('handles variety selection with proper callbacks', async () => {
      const user = userEvent.setup();
      render(
        <TestWrapper>
          <VarietySelectionPanel {...baseProps} mode={ModalMode.CREATE} />
        </TestWrapper>
      );

      const variety1Item = screen.getByText('Variety 1');
      await user.click(variety1Item);

      expect(mockOnVarietySelect).toHaveBeenCalledWith('1');
      expect(mockCheckVarietyHasData).toHaveBeenCalledWith('1');
    });
  });

  describe('Mode-Specific Behavior Verification', () => {
    it('CREATE mode ignores editability checks', async () => {
      const user = userEvent.setup();
      mockIsVarietyEditable.mockReturnValue(false); // All varieties non-editable
      
      render(
        <TestWrapper>
          <VarietySelectionPanel {...baseProps} mode={ModalMode.CREATE} />
        </TestWrapper>
      );

      const variety1Item = screen.getByText('Variety 1');
      await user.click(variety1Item);

      // Should still work in CREATE mode despite editability
      expect(mockOnVarietySelect).toHaveBeenCalledWith('1');
    });

    it('EDIT/CONFIRM modes respect editability checks', () => {
      mockIsVarietyEditable.mockReturnValue(false); // All varieties non-editable
      
      render(
        <TestWrapper>
          <VarietySelectionPanel {...baseProps} mode={ModalMode.EDIT} />
        </TestWrapper>
      );

      const variety1Item = screen.getByText('Variety 1').closest('li');
      
      // Should have pointer-events: none when not editable
      expect(variety1Item).toHaveStyle('pointer-events: none');
      expect(mockOnVarietySelect).not.toHaveBeenCalled();
    });
  });
});
