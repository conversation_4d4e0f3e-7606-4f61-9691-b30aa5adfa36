/* eslint-disable @typescript-eslint/no-require-imports */
import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { HarvestVarietyModal } from '../HarvestVarietyModal';
import { ModalMode } from '../types';
import { ThemeProvider } from '@mui/material/styles';
import { theme } from 'styles/theme';
import dayjs from 'dayjs';

// Mock dependencies
jest.mock('@tanstack/react-query', () => ({
  useQuery: jest.fn(),
}));

jest.mock('services/resource.service', () => ({
  fetchVarietiesService: jest.fn(),
}));

jest.mock('utils/cookie-client', () => ({
  getCookieLocale: jest.fn(() => 'en'),
}));

jest.mock('components/date-picker/custom-date-picker', () => ({
  CustomDatePicker: () => <input data-testid="custom-date-picker" />,
}));

jest.mock('utils', () => ({
  formatNumberWithCommas: jest.fn((value) => value),
  isValidNumberInput: jest.fn(() => true),
  REGEX_INPUT_NAME_FIELD: /^[a-zA-Z0-9\s]+$/,
}));

jest.mock('utils/input-grade', () => ({
  cleanDecimalInput: jest.fn((value) => value),
  ensureVarietyInMap: jest.fn(),
  findVarietyAndGrade: jest.fn(),
  hasLeadingZero: jest.fn(() => false),
  isValidValue: jest.fn(() => true),
  normalizeValue: jest.fn((value) => value),
  processDecimalValue: jest.fn((value) => value),
  processIntegerValue: jest.fn((value) => value),
}));

jest.mock('next-intl', () => ({
  useTranslations: jest.fn((namespace) => {
    const translations: Record<string, Record<string, string>> = {
      receive: {
        'receive-all-zero-or-negative-warning-message': 'Warning message',
        'varieties-update': 'varieties-update',
        'confirm-harvest-data': 'confirm-harvest-data',
        'specify-durian-weight-by-variety': 'specify-durian-weight-by-variety',
        varieties: 'varieties',
        'varieties-description': 'varieties-description',
        'grade-weight': 'grade-weight',
        'grade-help-text': 'grade-help-text',
        'flower-blooming-day': 'flower-blooming-day',
        'varieties-help-text': 'varieties-help-text',
        'no-varieties-available': 'no-varieties-available',
        'confirm-receive-harvest': 'confirm-receive-harvest',
        'aware-undo-action': 'aware-undo-action',
        'enter-variety-name': 'enter-variety-name',
      },
      common: {
        'variety-name': 'variety-name',
        'save-modal-btn': 'save-modal-btn',
        'confirm-modal-btn': 'confirm-modal-btn',
        'cancel-modal-btn': 'cancel-modal-btn',
        'unknown-error': 'unknown-error',
      },
      form: {
        'not-allow-characters': 'not-allow-characters',
      },
    };
    return (key: string) => translations[namespace]?.[key] || key;
  }),
}));

jest.mock('store/useMaterDataStore', () => ({
  useMasterDataStore: () => ({
    getGradeLabel: (id: string) => `Grade ${id}`,
    getVarietyLabel: (id: string) => `Variety ${id}`,
  }),
}));

jest.mock('assets/icons/empty-state.svg', () => 'empty-state-icon.svg');

jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ src, alt }: { src: string; alt: string }) => <img src={src} alt={alt} data-testid="next-image" />,
}));

const { useQuery } = require('@tanstack/react-query');

const mockVarieties = [
  {
    id: '1',
    value: 'variety1',
    label: { th: 'พันธุ์ 1', en: 'Variety 1' },
    flowerBloomingDay: dayjs().unix(),
    flowerBloomingDuration: 100,
    grades: [
      { id: 'A', value: 'A', label: { th: 'เกรด A', en: 'Grade A' }, weight: 0 },
      { id: 'B', value: 'B', label: { th: 'เกรด B', en: 'Grade B' }, weight: 0 },
    ],
  },
  {
    id: '2',
    value: 'variety2',
    label: { th: 'พันธุ์ 2', en: 'Variety 2' },
    flowerBloomingDay: dayjs().unix(),
    flowerBloomingDuration: 90,
    grades: [
      { id: 'A', value: 'A', label: { th: 'เกรด A', en: 'Grade A' }, weight: 0 },
      { id: 'C', value: 'C', label: { th: 'เกรด C', en: 'Grade C' }, weight: 0 },
    ],
  },
  {
    id: '3',
    value: 'other',
    label: { th: 'อื่นๆ', en: 'Other' },
    flowerBloomingDay: dayjs().unix(),
    flowerBloomingDuration: 80,
    grades: [
      { id: 'D', value: 'D', label: { th: 'เกรด D', en: 'Grade D' }, weight: 0 },
    ],
  },
];

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider theme={theme}>{children}</ThemeProvider>
);

const mockOnClose = jest.fn();
const mockOnSave = jest.fn();
const mockOnSaveCreate = jest.fn();

const baseProps = {
  open: true,
  onClose: mockOnClose,
  varieties: mockVarieties,
  initialWeights: {},
  initialCustomName: '',
  useProvidedVarieties: true,
  editableVarietyIds: new Set(['1', '2', '3']),
  varietyBloomDays: {
    '1': dayjs().unix(),
    '2': dayjs().unix(),
    '3': dayjs().unix(),
  },
};

describe('HarvestVarietyModal', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    useQuery.mockReturnValue({
      data: { data: mockVarieties },
      isLoading: false,
      error: null,
    });
  });

  describe('Edit Mode', () => {
    const editModeProps = {
      ...baseProps,
      mode: ModalMode.EDIT,
      onSave: mockOnSave,
    };

    it('renders correct title and button text in edit mode', () => {
      render(
        <TestWrapper>
          <HarvestVarietyModal {...editModeProps} />
        </TestWrapper>
      );

      expect(screen.getByText('varieties-update')).toBeInTheDocument();
      expect(screen.getByText('save-modal-btn')).toBeInTheDocument();
      expect(screen.queryByRole('checkbox')).not.toBeInTheDocument();
    });

    it('does not render checkboxes in edit mode', () => {
      render(
        <TestWrapper>
          <HarvestVarietyModal {...editModeProps} />
        </TestWrapper>
      );

      expect(screen.queryByRole('checkbox', { name: /confirm-receive-harvest/ })).not.toBeInTheDocument();
      expect(screen.queryByRole('checkbox', { name: /aware-undo-action/ })).not.toBeInTheDocument();
    });

    it('enables save button without checkbox validation in edit mode', async () => {
      const propsWithWeights = {
        ...editModeProps,
        initialWeights: { '1-1': '10', '2-1': '15' }, // Add some initial weights
      };

      render(
        <TestWrapper>
          <HarvestVarietyModal {...propsWithWeights} />
        </TestWrapper>
      );

      // Wait for the component to fully render and initialize
      await waitFor(() => {
        const saveButton = screen.getByText('save-modal-btn');
        expect(saveButton).not.toBeDisabled();
      });
    });

    it('shows only editable varieties in edit mode', () => {
      const editableOnlyProps = {
        ...editModeProps,
        editableVarietyIds: new Set(['1']),
      };

      render(
        <TestWrapper>
          <HarvestVarietyModal {...editableOnlyProps} />
        </TestWrapper>
      );

      expect(screen.getByText('Variety 1')).toBeInTheDocument();
      // Variety 2 and 3 should be hidden (display: none)
      const variety2Item = screen.queryByText('Variety 2')?.closest('li');
      const variety3Item = screen.queryByText('Variety 3')?.closest('li');
      expect(variety2Item).toHaveStyle('display: none');
      expect(variety3Item).toHaveStyle('display: none');
    });
  });

  describe('Confirm Mode', () => {
    const confirmModeProps = {
      ...baseProps,
      mode: ModalMode.CONFIRM,
      onSave: mockOnSave,
    };

    it('renders correct title and button text in confirm mode', () => {
      render(
        <TestWrapper>
          <HarvestVarietyModal {...confirmModeProps} />
        </TestWrapper>
      );

      expect(screen.getByText('confirm-harvest-data')).toBeInTheDocument();
      expect(screen.getByText('confirm-modal-btn')).toBeInTheDocument();
    });

    it('renders both required checkboxes in confirm mode', () => {
      render(
        <TestWrapper>
          <HarvestVarietyModal {...confirmModeProps} />
        </TestWrapper>
      );

      expect(screen.getByRole('checkbox', { name: /confirm-receive-harvest/ })).toBeInTheDocument();
      expect(screen.getByRole('checkbox', { name: /aware-undo-action/ })).toBeInTheDocument();
    });

    it('disables confirm button when checkboxes are not checked', () => {
      render(
        <TestWrapper>
          <HarvestVarietyModal {...confirmModeProps} />
        </TestWrapper>
      );

      const confirmButton = screen.getByText('confirm-modal-btn');
      expect(confirmButton).toBeDisabled();
    });

    it('enables confirm button when both checkboxes are checked', async () => {
      const user = userEvent.setup();
      const propsWithWeights = {
        ...confirmModeProps,
        initialWeights: { '1-1': '10', '2-1': '15' }, // Add some initial weights
      };

      render(
        <TestWrapper>
          <HarvestVarietyModal {...propsWithWeights} />
        </TestWrapper>
      );

      const confirmReceiveCheckbox = screen.getByRole('checkbox', { name: /confirm-receive-harvest/ });
      const awareUndoCheckbox = screen.getByRole('checkbox', { name: /aware-undo-action/ });
      const confirmButton = screen.getByText('confirm-modal-btn');

      await user.click(confirmReceiveCheckbox);
      await user.click(awareUndoCheckbox);

      await waitFor(() => {
        expect(confirmButton).not.toBeDisabled();
      });
    });
  });

  describe('Create Mode', () => {
    const createModeProps = {
      ...baseProps,
      mode: ModalMode.CREATE,
      onSaveCreate: mockOnSaveCreate,
      cuttingDay: dayjs('2024-01-15'),
      useProvidedVarieties: false,
    };

    it('renders correct title and button text in create mode', () => {
      render(
        <TestWrapper>
          <HarvestVarietyModal {...createModeProps} />
        </TestWrapper>
      );

      expect(screen.getByText('specify-durian-weight-by-variety')).toBeInTheDocument();
      expect(screen.getByText('save-modal-btn')).toBeInTheDocument();
    });

    it('does not render checkboxes in create mode', () => {
      render(
        <TestWrapper>
          <HarvestVarietyModal {...createModeProps} />
        </TestWrapper>
      );

      expect(screen.queryByRole('checkbox', { name: /confirm-receive-harvest/ })).not.toBeInTheDocument();
      expect(screen.queryByRole('checkbox', { name: /aware-undo-action/ })).not.toBeInTheDocument();
    });

    it('shows all varieties in create mode', () => {
      render(
        <TestWrapper>
          <HarvestVarietyModal {...createModeProps} />
        </TestWrapper>
      );

      expect(screen.getByText('Variety 1')).toBeInTheDocument();
      expect(screen.getByText('Variety 2')).toBeInTheDocument();
      expect(screen.getByText('Variety 3')).toBeInTheDocument();
    });

    it('shows bloom day inputs in create mode', async () => {
      const user = userEvent.setup();
      render(
        <TestWrapper>
          <HarvestVarietyModal {...createModeProps} />
        </TestWrapper>
      );

      // Click on a variety to see its inputs
      const variety1Item = screen.getByText('Variety 1');
      await user.click(variety1Item);

      await waitFor(() => {
        expect(screen.getByText('flower-blooming-day')).toBeInTheDocument();
        expect(screen.getByTestId('custom-date-picker')).toBeInTheDocument();
      });
    });

    it('renders create mode correctly', () => {
      render(
        <TestWrapper>
          <HarvestVarietyModal {...createModeProps} />
        </TestWrapper>
      );

      // Should render the modal in create mode
      expect(screen.getByText('specify-durian-weight-by-variety')).toBeInTheDocument();
      expect(screen.getByText('save-modal-btn')).toBeInTheDocument();
    });

    it('disables save button when no data is entered in create mode', () => {
      render(
        <TestWrapper>
          <HarvestVarietyModal {...createModeProps} />
        </TestWrapper>
      );

      const saveButton = screen.getByText('save-modal-btn');
      expect(saveButton).toBeDisabled();
    });
  });

  describe('Common Functionality', () => {
    it('renders modal when open is true', () => {
      render(
        <TestWrapper>
          <HarvestVarietyModal {...baseProps} mode={ModalMode.EDIT} onSave={mockOnSave} />
        </TestWrapper>
      );

      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    it('calls onClose when close button is clicked', async () => {
      const user = userEvent.setup();
      render(
        <TestWrapper>
          <HarvestVarietyModal {...baseProps} mode={ModalMode.EDIT} onSave={mockOnSave} />
        </TestWrapper>
      );

      const closeButton = screen.getByLabelText('close');
      await user.click(closeButton);

      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    it('calls onClose when cancel button is clicked', async () => {
      const user = userEvent.setup();
      render(
        <TestWrapper>
          <HarvestVarietyModal {...baseProps} mode={ModalMode.EDIT} onSave={mockOnSave} />
        </TestWrapper>
      );

      const cancelButton = screen.getByText('cancel-modal-btn');
      await user.click(cancelButton);

      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    it('shows loading spinner when data is loading', () => {
      useQuery.mockReturnValue({
        data: null,
        isLoading: true,
        error: null,
      });

      render(
        <TestWrapper>
          <HarvestVarietyModal 
            {...baseProps} 
            mode={ModalMode.CREATE} 
            onSaveCreate={mockOnSaveCreate}
            useProvidedVarieties={false}
          />
        </TestWrapper>
      );

      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    it('displays error message when varieties loading fails', () => {
      useQuery.mockReturnValue({
        data: null,
        isLoading: false,
        error: new Error('Failed to load varieties'),
      });

      render(
        <TestWrapper>
          <HarvestVarietyModal 
            {...baseProps} 
            mode={ModalMode.CREATE} 
            onSaveCreate={mockOnSaveCreate}
            useProvidedVarieties={false}
          />
        </TestWrapper>
      );

      expect(screen.getByText(/Error loading varieties/)).toBeInTheDocument();
    });
  });
});
