import React, { useCallback } from 'react';
import { Box, List, ListItem, ListItemText, Typography } from '@mui/material';
import CheckIcon from '@mui/icons-material/Check';
import { useTranslations } from 'next-intl';
import { useMasterDataStore } from 'store/useMaterDataStore';
import { theme } from 'styles/theme';
import { VarietySelectionPanelProps, ModalMode } from './types';

export const VarietySelectionPanel: React.FC<VarietySelectionPanelProps> = React.memo(({
  varieties,
  selectedVariety,
  allZeroOrNegativeWarning,
  mode,
  onVarietySelect,
  checkVarietyHasData,
  isVarietyEditable,
}) => {
  const receiveTranslation = useTranslations('receive');
  const { getVarietyLabel } = useMasterDataStore();

  const handleVarietyClick = useCallback((varietyId: string) => {
    // In CREATE mode, don't check for warning
    // In EDIT/CONFIRM modes, check for warning
    if (mode !== ModalMode.CREATE && allZeroOrNegativeWarning) return;
    
    // In CREATE mode, all varieties are selectable
    // In EDIT/CONFIRM modes, only editable varieties are selectable
    if (mode !== ModalMode.CREATE && !isVarietyEditable(varietyId)) return;
    
    onVarietySelect(varietyId);
  }, [mode, allZeroOrNegativeWarning, isVarietyEditable, onVarietySelect]);

  const renderVarietyColor = useCallback((id: string) => {
    if (selectedVariety === id) {
      return theme.palette.customColors.blueHighlight;
    }
    
    // In CREATE mode, all varieties are enabled
    if (mode === ModalMode.CREATE) {
      return 'text.primary';
    }
    
    // In EDIT/CONFIRM modes, check if variety is editable
    if (isVarietyEditable(id)) {
      return 'text.primary';
    }
    return 'text.disabled';
  }, [selectedVariety, mode, isVarietyEditable]);

  const renderVarietyBgColor = useCallback((id: string) => {
    if (selectedVariety === id) {
      return theme.palette.customColors.lightGray;
    }
    
    // In CREATE mode, all varieties are hoverable
    if (mode === ModalMode.CREATE) {
      return 'rgba(0, 0, 0, 0.04)';
    }
    
    // In EDIT/CONFIRM modes, only editable varieties are hoverable
    if (isVarietyEditable(id)) {
      return 'rgba(0, 0, 0, 0.04)';
    }
    return 'transparent';
  }, [selectedVariety, mode, isVarietyEditable]);

  const shouldShowVariety = useCallback((id: string) => {
    // In CREATE mode, show all varieties
    if (mode === ModalMode.CREATE) {
      return true;
    }
    
    // In EDIT/CONFIRM modes, only show editable varieties
    return isVarietyEditable(id);
  }, [mode, isVarietyEditable]);

  const isVarietyClickable = useCallback((id: string) => {
    // In CREATE mode, all varieties are clickable
    if (mode === ModalMode.CREATE) {
      return true;
    }
    
    // In EDIT/CONFIRM modes, only editable varieties are clickable
    return isVarietyEditable(id);
  }, [mode, isVarietyEditable]);

  return (
    <Box sx={{ width: '40%', borderRight: '0px solid #e0e0e0', p: 2 }}>
      <Typography variant="subtitle1" sx={{ mb: 1, fontSize: '16px', fontWeight: 500 }}>
        {receiveTranslation('varieties')}
      </Typography>
      <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 2 }}>
        {receiveTranslation('varieties-description')}
      </Typography>

      <List sx={{ pt: 0, mx: -2 }}>
        {varieties.map((variety) => (
          <ListItem
            key={variety.id}
            disablePadding
            sx={{
              padding: '16px 24px',
              mb: 0.5,
              bgcolor: selectedVariety === variety.id ? theme.palette.customColors.lightGray : 'transparent',
              color: renderVarietyColor(variety.id),
              '&:hover': {
                bgcolor: renderVarietyBgColor(variety.id),
                cursor: isVarietyClickable(variety.id) ? 'pointer' : 'default',
              },
              display: shouldShowVariety(variety.id) ? 'flex' : 'none',
              justifyContent: 'space-between',
              alignItems: 'center',
              alignSelf: 'stretch',
              position: 'relative',
              pl: 3,
              transition: 'all 0.2s ease-in-out',
              opacity: isVarietyClickable(variety.id) ? 1 : 0.6,
              pointerEvents: isVarietyClickable(variety.id) ? 'auto' : 'none',
            }}
            onClick={() => handleVarietyClick(variety.id)}
          >
            <ListItemText
              primary={getVarietyLabel(variety.id)}
              slotProps={{
                primary: {
                  sx: {
                    color:
                      selectedVariety === variety.id ? theme.palette.customColors.blueHighlight : 'inherit',
                    fontWeight: selectedVariety === variety.id ? 500 : 400,
                  },
                },
              }}
            />
            {checkVarietyHasData(variety.id) && (
              <CheckIcon
                sx={{
                  color: theme.palette.customColors.blueHighlight,
                  fontSize: '1.2rem',
                }}
              />
            )}
          </ListItem>
        ))}
      </List>
    </Box>
  );
});

VarietySelectionPanel.displayName = 'VarietySelectionPanel';
