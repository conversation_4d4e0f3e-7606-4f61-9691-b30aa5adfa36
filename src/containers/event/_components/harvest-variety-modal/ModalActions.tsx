import React, { useMemo } from 'react';
import { Box, Button, CircularProgress, DialogActions } from '@mui/material';
import { useTranslations } from 'next-intl';
import { ValidationCheckboxes } from './components/ValidationCheckboxes';
import { ModalActionsProps, ModalMode } from './types';

interface ButtonConfig {
  text: string;
  id: string;
  className: string;
}

export const ModalActions: React.FC<ModalActionsProps> = React.memo(({
  mode,
  isButtonEnabled,
  isLoading,
  isSubmitting,
  onClose,
  onSubmit,
  showCheckboxes,
  checkboxesProps,
}) => {
  const commonT = useTranslations('common');

  // Button configuration based on mode
  const buttonConfig: ButtonConfig = useMemo(() => {
    switch (mode) {
      case ModalMode.EDIT:
        return {
          text: commonT('save-modal-btn'),
          id: 'save-modal-btn',
          className: 'save-modal-btn',
        };
      case ModalMode.CONFIRM:
        return {
          text: commonT('confirm-modal-btn'),
          id: 'confirm-modal-btn',
          className: 'confirm-modal-btn',
        };
      case ModalMode.CREATE:
      default:
        return {
          text: commonT('save-modal-btn'),
          id: 'save-modal-btn',
          className: 'save-modal-btn',
        };
    }
  }, [mode, commonT]);

  const shouldShowCheckboxes = useMemo(() => {
    return mode === ModalMode.CONFIRM && showCheckboxes && checkboxesProps;
  }, [mode, showCheckboxes, checkboxesProps]);

  const dialogActionsJustification = useMemo(() => {
    return shouldShowCheckboxes ? 'space-between' : 'flex-end';
  }, [shouldShowCheckboxes]);

  return (
    <DialogActions
      sx={{
        p: 3,
        display: 'flex',
        justifyContent: dialogActionsJustification,
        alignItems: 'center',
      }}
    >
      {/* Conditional Checkboxes Section - Only in Confirm Mode */}
      {shouldShowCheckboxes && (
        <ValidationCheckboxes {...checkboxesProps!} />
      )}

      <Box sx={{ display: 'flex', gap: 1 }}>
        <Button
          onClick={onClose}
          variant="outlined"
          disabled={isLoading}
          data-testid="close-update-modal"
          sx={{
            minWidth: '140px',
            px: 3,
            py: 1,
            fontSize: '0.875rem',
            fontWeight: 500,
          }}
        >
          {commonT('cancel-modal-btn')}
        </Button>

        <Button
          onClick={onSubmit}
          variant="contained"
          color="primary"
          disabled={!isButtonEnabled}
          startIcon={isSubmitting ? <CircularProgress size={20} color="inherit" /> : null}
          data-testid="save-update-modal"
          sx={{
            minWidth: '140px',
            px: 3,
            py: 1,
            fontSize: '0.875rem',
            fontWeight: 500,
          }}
          id={buttonConfig.id}
          className={buttonConfig.className}
        >
          {buttonConfig.text}
        </Button>
      </Box>
    </DialogActions>
  );
});

ModalActions.displayName = 'ModalActions';
