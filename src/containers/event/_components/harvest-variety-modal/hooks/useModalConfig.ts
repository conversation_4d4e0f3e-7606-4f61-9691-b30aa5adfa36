import { useMemo } from 'react';
import { 
  ModalMode, 
  ModalConfig, 
  ValidationConfig, 
  ButtonEnableConfig,
  UseModalConfigResult,
  MODE_CONFIGS,
  VALIDATION_CONFIGS
} from '../types';

export const useModalConfig = (mode: ModalMode): UseModalConfigResult => {
  const config = useMemo((): ModalConfig => {
    return MODE_CONFIGS[mode];
  }, [mode]);

  const validationConfig = useMemo((): ValidationConfig => {
    return VALIDATION_CONFIGS[mode];
  }, [mode]);

  const buttonConfig = useMemo((): ButtonEnableConfig => {
    const baseConditions = [
      'not-loading',
      'varieties-available',
      'no-zero-negative-warning'
    ];

    const modeSpecificConditions: string[] = [];

    switch (mode) {
      case ModalMode.EDIT:
        // No additional conditions for edit mode
        break;
      
      case ModalMode.CONFIRM:
        modeSpecificConditions.push(
          'confirm-checkbox-checked',
          'aware-checkbox-checked'
        );
        break;
      
      case ModalMode.CREATE:
        modeSpecificConditions.push(
          'has-at-least-one-weight',
          'all-varieties-have-bloom-days',
          'valid-custom-variety-names'
        );
        break;
    }

    return {
      baseConditions,
      modeSpecificConditions,
    };
  }, [mode]);

  return {
    config,
    validationConfig,
    buttonConfig,
  };
};
