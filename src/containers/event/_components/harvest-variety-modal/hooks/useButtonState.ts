import { useMemo } from 'react';
import { DurianVariety } from 'types';
import {
  ModalMode,
  FormData,
  CreateModeFormData,
  ConfirmModeFormData,
  UseButtonStateResult
} from '../types';

interface VarietiesResponse {
  data: DurianVariety[];
}

export const useButtonState = (
  mode: ModalMode,
  formData: FormData,
  varieties: DurianVariety[],
  isLoading: boolean,
  varietiesResponse: VarietiesResponse | undefined,
  useProvidedVarieties: boolean,
  allZeroOrNegativeWarning: boolean
): UseButtonStateResult => {
  const result = useMemo((): UseButtonStateResult => {
    const reasons: string[] = [];
    let isEnabled = true;

    // Base conditions
    if (isLoading) {
      isEnabled = false;
      reasons.push('Loading in progress');
    }

    if (!useProvidedVarieties && !varietiesResponse) {
      isEnabled = false;
      reasons.push('Varieties not loaded');
    }

    if (allZeroOrNegativeWarning && mode !== ModalMode.CREATE) {
      isEnabled = false;
      reasons.push('Zero or negative weight warning active');
    }

    // Mode-specific conditions
    switch (mode) {
      case ModalMode.CONFIRM: {
        const confirmData = formData as ConfirmModeFormData;
        if (!confirmData.confirmReceiveHarvest) {
          isEnabled = false;
          reasons.push('Confirm receive harvest checkbox not checked');
        }
        if (!confirmData.awareUndoAction) {
          isEnabled = false;
          reasons.push('Aware undo action checkbox not checked');
        }
        break;
      }

      case ModalMode.CREATE: {
        const createData = formData as CreateModeFormData;
        
        // Check if at least one weight is entered
        const hasAtLeastOneWeight = Object.values(createData.weights || {}).some(
          (value) => value !== '' && value !== undefined && value !== null
        );
        
        if (!hasAtLeastOneWeight) {
          isEnabled = false;
          reasons.push('No weights entered');
        }

        // Get variety IDs that have weights
        const varietyIdsFromWeights = Object.entries(createData.weights || {})
          .filter(([, value]) => value !== null && value !== undefined && value !== '')
          .map(([key]) => key.split('-')[0]);
        
        const uniqueVarietyIds = [...new Set(varietyIdsFromWeights)];

        // Check if all varieties with weights have bloom days
        const varietyIdsWithBloomDays = Object.entries(createData.varietyBloomDays || {})
          .filter(([, value]) => value !== null && value !== undefined)
          .map(([key]) => key);

        const allVarietiesHaveBloomDays = uniqueVarietyIds.every((varietyId) => {
          return varietyIdsWithBloomDays.some((id) => id.includes(varietyId));
        });

        if (!allVarietiesHaveBloomDays) {
          isEnabled = false;
          reasons.push('Not all varieties with weights have bloom days');
        }

        // Check custom variety names for "other" varieties
        const hasValidCustomVarietyNames = varietyIdsWithBloomDays.every((varietyId) => {
          const variety = varieties.find((v) => v.id === varietyId);
          if (variety?.value === 'other') {
            const hasWeights = Object.entries(createData.weights || {})
              .filter(([key]) => key.startsWith(varietyId))
              .some(([, value]) => value !== '' && value !== undefined && value !== null);
            
            const hasValidName = createData.customVarietyName && createData.customVarietyName.trim() !== '';
            
            return hasValidName && hasWeights;
          }
          return true;
        });

        if (!hasValidCustomVarietyNames) {
          isEnabled = false;
          reasons.push('Invalid custom variety names for "other" varieties');
        }

        break;
      }

      case ModalMode.EDIT:
      default:
        // No additional conditions for edit mode
        break;
    }

    return {
      isEnabled,
      reasons,
    };
  }, [mode, formData, varieties, isLoading, varietiesResponse, useProvidedVarieties, allZeroOrNegativeWarning]);

  return result;
};
