import { useMemo } from 'react';
import * as z from 'zod';
import { DurianVariety } from 'types';
import { 
  ModalMode, 
  FormData, 
  EditModeFormData, 
  ConfirmModeFormData, 
  CreateModeFormData,
  UseFormSchemaResult 
} from '../types';

export const useFormSchema = (
  mode: ModalMode,
  varieties: DurianVariety[],
  initialWeights: Record<string, string>,
  initialCustomName: string,
  varietyBloomDays: Record<string, number>
): UseFormSchemaResult => {
  const createWeightsSchema = useMemo(() => {
    return z.record(z.string(), z.string().optional());
  }, []);

  const createBloomDaysSchema = useMemo(() => {
    const bloomDaysShape: Record<string, z.ZodType<number | null | undefined>> = {};

    varieties.forEach((variety) => {
      const key = variety.id;
      bloomDaysShape[key] = z.number().nullable().optional();
    });

    return z.object(bloomDaysShape);
  }, [varieties]);

  const schema = useMemo(() => {
    const baseSchema = {
      weights: createWeightsSchema,
      customVarietyName: z.string().optional(),
    };

    switch (mode) {
      case ModalMode.CREATE:
        return z.object({
          ...baseSchema,
          varietyBloomDays: createBloomDaysSchema,
        });
      
      case ModalMode.CONFIRM:
        return z.object({
          ...baseSchema,
          confirmReceiveHarvest: z.boolean(),
          awareUndoAction: z.boolean(),
        });
      
      case ModalMode.EDIT:
      default:
        return z.object(baseSchema);
    }
  }, [mode, createWeightsSchema, createBloomDaysSchema]);

  const defaultValues = useMemo((): FormData => {
    const baseDefaults = {
      weights: initialWeights as Record<string, string | undefined>,
      customVarietyName: initialCustomName,
    };

    switch (mode) {
      case ModalMode.CREATE:
        return {
          ...baseDefaults,
          varietyBloomDays: varietyBloomDays,
        } as CreateModeFormData;
      
      case ModalMode.CONFIRM:
        return {
          ...baseDefaults,
          confirmReceiveHarvest: false,
          awareUndoAction: false,
        } as ConfirmModeFormData;
      
      case ModalMode.EDIT:
      default:
        return baseDefaults as EditModeFormData;
    }
  }, [mode, initialWeights, initialCustomName, varietyBloomDays]);

  return {
    schema,
    defaultValues,
  };
};
