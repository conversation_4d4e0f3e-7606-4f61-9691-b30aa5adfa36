import React, { useMemo } from 'react';
import { Alert, Box, Typography } from '@mui/material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { useTranslations } from 'next-intl';
import { theme } from 'styles/theme';
import { Controller, Control, FieldErrors } from 'react-hook-form';
import { getCookieLocale } from 'utils/cookie-client';
import emptyStateIcon from 'assets/icons/empty-state.svg';
import Image from 'next/image';
import { 
  CustomVarietyNameInput, 
  GradeWeightInput, 
  FlowerBloomingDayInput,
  BloomDayInput 
} from './components';
import { GradeInputPanelProps, FormData, ModalMode } from './types';

interface ExtendedGradeInputPanelProps extends GradeInputPanelProps {
  control: Control<FormData>;
  errors: FieldErrors<FormData>;
  onWeightChange: (varietyId: string, gradeId: string, value: string) => void;
}

export const GradeInputPanel: React.FC<ExtendedGradeInputPanelProps> = React.memo(({
  selectedVariety,
  varieties,
  varietyBloomDays,
  allZeroOrNegativeWarning,
  allZeroOrNegativeWarningMessage,
  isSubmitting,
  editableVarietyIds,
  internalCustomName,
  mode,
  cuttingDay,
  onCustomVarietyNameChange,
  onCustomVarietyNameBlur,
  onBloomDayChange,
  control,
  errors,
  onWeightChange,
}) => {
  const receiveTranslation = useTranslations('receive');
  const locale = getCookieLocale() ?? 'th';

  const selectedVarietyData = useMemo(() => {
    return varieties.find((variety) => variety.id === selectedVariety);
  }, [varieties, selectedVariety]);

  const isOtherVariety = useMemo(() => {
    if (!selectedVarietyData) return false;
    return selectedVarietyData.value === 'other';
  }, [selectedVarietyData]);

  const isVarietyEditable = useMemo(() => {
    if (!selectedVariety) return false;
    // In CREATE mode, all varieties are editable
    if (mode === ModalMode.CREATE) return true;
    // In EDIT/CONFIRM modes, check editableVarietyIds
    return editableVarietyIds.has(selectedVariety);
  }, [selectedVariety, editableVarietyIds, mode]);

  const selectedVarietyBloomDay = useMemo(() => {
    if (selectedVariety && varietyBloomDays[selectedVariety]) {
      return varietyBloomDays[selectedVariety];
    }
    return null;
  }, [selectedVariety, varietyBloomDays]);

  const shouldShowZeroNegativeWarning = useMemo(() => {
    return mode !== ModalMode.CREATE && allZeroOrNegativeWarning;
  }, [mode, allZeroOrNegativeWarning]);

  const renderCustomVarietyNameInput = () => {
    if (!isOtherVariety) return null;

    return (
      <Controller
        name="customVarietyName"
        control={control}
        render={() => (
          <CustomVarietyNameInput
            value={internalCustomName || ''}
            onChange={onCustomVarietyNameChange}
            onBlur={onCustomVarietyNameBlur}
            disabled={mode !== ModalMode.CREATE} // Always disabled in EDIT and CONFIRM modes
            error={errors.customVarietyName?.message}
          />
        )}
      />
    );
  };

  const renderBloomDayInput = () => {
    if (!selectedVarietyData) return null;

    // In CREATE mode, show interactive bloom day input
    if (mode === ModalMode.CREATE) {
      return (
        <Controller
          key={selectedVarietyData.id} // Force re-mount when variety changes
          name={`varietyBloomDays.${selectedVarietyData.id}` as keyof FormData}
          control={control}
          render={({ field }) => (
            <BloomDayInput
              variety={selectedVarietyData}
              value={typeof field.value === 'number' ? field.value : null}
              cuttingDay={cuttingDay}
              onChange={(timestamp) => {
                field.onChange(timestamp);
                if (onBloomDayChange) {
                  onBloomDayChange(selectedVarietyData.id, timestamp);
                }
              }}
              disabled={isSubmitting}
              locale={locale}
            />
          )}
        />
      );
    }

    // In EDIT/CONFIRM modes, show read-only bloom day
    if (isVarietyEditable) {
      return (
        <FlowerBloomingDayInput
          value={selectedVarietyBloomDay}
          disabled={true}
        />
      );
    }

    return null;
  };

  const renderGradeInputs = () => {
    if (!selectedVarietyData) return null;

    return selectedVarietyData.grades.map((grade) => (
      <Controller
        key={`${selectedVarietyData.id}-${grade.id}`}
        name={`weights.${selectedVarietyData.id}-${grade.id}` as keyof FormData}
        control={control}
        render={({ field }) => (
          <GradeWeightInput
            variety={selectedVarietyData}
            grade={grade}
            value={field.value as string | undefined}
            onChange={(value) => {
              field.onChange(value);
              onWeightChange(selectedVarietyData.id, grade.id, value);
            }}
            onBlur={field.onBlur}
            disabled={isSubmitting || (mode !== ModalMode.CREATE && !isVarietyEditable)}
            error={errors.weights?.[`${selectedVarietyData.id}-${grade.id}`]?.message}
            isSubmitting={isSubmitting}
          />
        )}
      />
    ));
  };

  const renderEmptyState = () => {
    if (mode === ModalMode.CREATE) {
      return (
        <Box
          component="div"
          sx={{
            width: '100%',
            display: 'flex',
            alignItems: 'center',
            flexDirection: 'column',
            height: '100%',
            mt: 16,
          }}
        >
          <Image src={emptyStateIcon} width={160} height={160} unoptimized alt="empty-state-icon" />
          <Typography color="text.secondary" sx={{ mt: 4, textAlign: 'center', fontSize: '14px' }}>
            {receiveTranslation('varieties-help-text')}
          </Typography>
        </Box>
      );
    }

    return (
      <Typography color="text.secondary" sx={{ mt: 4, textAlign: 'center' }}>
        {receiveTranslation('varieties-help-text')}
      </Typography>
    );
  };

  return (
    <Box sx={{ width: '60%', p: 2, bgcolor: theme.palette.customColors.lightGray }}>
      <Typography variant="subtitle1" sx={{ mb: 1 }}>
        {receiveTranslation('grade-weight')}
      </Typography>
      <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 2 }}>
        {receiveTranslation('grade-help-text')}
      </Typography>

      {shouldShowZeroNegativeWarning && (
        <Alert
          severity="info"
          sx={{
            mb: 2,
            bgcolor: theme.palette.customColors.lightRed,
            color: theme.palette.customColors.secondary,
            p: '4px 5px',
            alignItems: 'center',
          }}
          icon={
            <InfoOutlinedIcon
              sx={{
                color: theme.palette.customColors.secondary,
                verticalAlign: 'middle',
                fontSize: '1.5rem',
                p: 0,
              }}
            />
          }
        >
          {allZeroOrNegativeWarningMessage}
        </Alert>
      )}

      <Box sx={{ mt: 2 }}>
        {selectedVariety && (
          <>
            {renderCustomVarietyNameInput()}
            {renderBloomDayInput()}
            {renderGradeInputs()}
          </>
        )}

        {!selectedVariety && varieties.length > 0 && renderEmptyState()}

        {varieties.length === 0 && (
          <Typography color="text.secondary" sx={{ mt: 4, textAlign: 'center' }}>
            {receiveTranslation('no-varieties-available')}
          </Typography>
        )}
      </Box>
    </Box>
  );
});

GradeInputPanel.displayName = 'GradeInputPanel';
