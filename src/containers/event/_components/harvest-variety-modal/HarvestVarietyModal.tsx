'use client';

import React, { FC, useCallback, useEffect, useMemo, useState, useRef } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { Box, CircularProgress, Dialog, DialogContent, IconButton, Typography } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useQuery } from '@tanstack/react-query';
import { useForm, useWatch } from 'react-hook-form';
import { useTranslations } from 'next-intl';

import { fetchVarietiesService } from 'services/resource.service';
import { theme } from 'styles/theme';
import { REGEX_INPUT_NAME_FIELD } from 'utils';
import { ensureVarietyInMap, findVarietyAndGrade, isValidValue } from 'utils/input-grade';

import { VarietySelectionPanel } from './VarietySelectionPanel';
import { GradeInputPanel } from './GradeInputPanel';
import { ModalActions } from './ModalActions';
import { useModalConfig, useFormSchema, useButtonState } from './hooks';
import { HarvestVarietyModalProps, FormData, CreateModeFormData, ConfirmModeFormData, EditModeFormData, ModalMode, VarietiesMap } from './types';

export const HarvestVarietyModal: FC<HarvestVarietyModalProps> = ({
  open,
  onClose,
  onSave,
  onSaveCreate,
  varieties: providedVarieties = [],
  initialWeights = {},
  initialCustomName = '',
  useProvidedVarieties = false,
  editableVarietyIds = new Set(),
  varietyBloomDays = {},
  mode,
  cuttingDay,
}) => {
  const receiveTranslation = useTranslations('receive');
  const commonT = useTranslations('common');
  const formTranslation = useTranslations('form');

  // Get mode-specific configuration
  const { config } = useModalConfig(mode);

  // State management
  const [selectedVariety, setSelectedVariety] = useState<string | null>(null);
  const [varietiesWithData, setVarietiesWithData] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [allZeroOrNegativeWarning, setAllZeroOrNegativeWarning] = useState<boolean>(false);
  const [internalCustomName, setInternalCustomName] = useState(initialCustomName);
  const [internalBloomDays, setInternalBloomDays] = useState(varietyBloomDays);

  // Track previous open state to detect when modal opens
  const prevOpenRef = useRef(open);

  const allZeroOrNegativeWarningMessage = receiveTranslation('receive-all-zero-or-negative-warning-message');

  // Data fetching (only for CREATE mode or when not using provided varieties)
  const shouldFetchVarieties = open && (mode === ModalMode.CREATE || !useProvidedVarieties);

  const {
    data: varietiesResponse,
    isLoading: isLoadingVarieties,
    error: varietiesError,
  } = useQuery({
    queryKey: ['varieties'],
    queryFn: fetchVarietiesService,
    enabled: shouldFetchVarieties,
  });

  const varieties = useMemo(() => {
    if (mode === ModalMode.CREATE || !useProvidedVarieties) {
      return varietiesResponse?.data || [];
    }
    return providedVarieties;
  }, [mode, useProvidedVarieties, providedVarieties, varietiesResponse]);

  // Form schema and setup
  const { schema, defaultValues } = useFormSchema(
    mode,
    varieties,
    initialWeights,
    initialCustomName,
    mode === ModalMode.CREATE ? internalBloomDays : varietyBloomDays
  );

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    setError,
    formState: { errors },
  } = useForm<FormData>({
    resolver: zodResolver(schema),
    defaultValues,
    mode: 'onChange',
  });

  // Watch form values
  const weights = useWatch({
    control,
    name: 'weights',
    defaultValue: initialWeights as Record<string, string | undefined>,
  });

  const customVarietyName = useWatch({
    control,
    name: 'customVarietyName',
    defaultValue: initialCustomName,
  });

  const confirmReceiveHarvest = useWatch({
    control,
    name: 'confirmReceiveHarvest',
    defaultValue: false,
  });

  const awareUndoAction = useWatch({
    control,
    name: 'awareUndoAction',
    defaultValue: false,
  });

  const watchedBloomDays = useWatch({
    control,
    name: 'varietyBloomDays',
    defaultValue: mode === ModalMode.CREATE ? internalBloomDays : {},
  });

  // Create form data for button state calculation
  const currentFormData = useMemo((): FormData => {
    const baseData = {
      weights: weights || {},
      customVarietyName: internalCustomName,
    };

    switch (mode) {
      case ModalMode.CREATE:
        return {
          ...baseData,
          varietyBloomDays: watchedBloomDays || internalBloomDays,
        } as CreateModeFormData;
      case ModalMode.CONFIRM:
        return {
          ...baseData,
          confirmReceiveHarvest: confirmReceiveHarvest || false,
          awareUndoAction: awareUndoAction || false,
        } as ConfirmModeFormData;
      case ModalMode.EDIT:
      default:
        return baseData as EditModeFormData;
    }
  }, [mode, weights, internalCustomName, watchedBloomDays, internalBloomDays, confirmReceiveHarvest, awareUndoAction]);

  // Loading state
  const isLoading = (shouldFetchVarieties && isLoadingVarieties) || isSubmitting;

  // Button state calculation
  const { isEnabled: isButtonEnabled } = useButtonState(
    mode,
    currentFormData,
    varieties,
    isLoading,
    varietiesResponse,
    useProvidedVarieties,
    allZeroOrNegativeWarning
  );

  // First variety selection
  const firstVariety = useMemo(() => {
    if (mode === ModalMode.CREATE) {
      if (varieties.length === 0) return null;

      // Find first variety that has both weight data and blooming date
      const varietyWithWeightAndBloom = varieties.find((variety) => {
        // Check if variety has weight data
        const hasWeight = variety.grades.some((grade) => {
          const weightKey = `${variety.id}-${grade.id}`;
          return weights[weightKey] && weights[weightKey].trim() !== '';
        });

        // Check if variety has blooming date (from variety definition or internal state)
        const hasBloomingDate =
          (variety.flowerBloomingDay && variety.flowerBloomingDay > 0) ||
          (internalBloomDays[variety.id] && internalBloomDays[variety.id] > 0);

        return hasWeight && hasBloomingDate;
      });

      // Return variety with both weight and blooming date, or fall back to first variety
      return varietyWithWeightAndBloom?.id || null;
    }
    return varieties.find((variety) => editableVarietyIds.has(variety.id))?.id ?? null;
  }, [varieties, editableVarietyIds, mode, weights, internalBloomDays]);

  // Event handlers
  const handleClose = useCallback(() => {
    onClose?.();
  }, [onClose]);

  const isVarietyEditable = useCallback(
    (varietyId: string) => {
      if (mode === ModalMode.CREATE) return true;
      return editableVarietyIds.has(varietyId);
    },
    [editableVarietyIds, mode]
  );

  const handleVarietySelect = useCallback((varietyId: string) => {
    setSelectedVariety(varietyId);
  }, []);

  const checkVarietyHasData = useCallback(
    (varietyId: string) => {
      const otherVariety = varieties.find((v) => v.id === varietyId && v.value === 'other');
      if (otherVariety && customVarietyName && customVarietyName.trim() !== '') {
        return true;
      }
      for (const [key, value] of Object.entries(weights)) {
        if (key.startsWith(varietyId) && value && value.trim() !== '') {
          return true;
        }
      }
      return false;
    },
    [varieties, customVarietyName, weights]
  );

  const handleCustomVarietyNameChange = useCallback(
    (value: string) => {
      setInternalCustomName(value);
      setValue('customVarietyName', value);
    },
    [setValue]
  );

  const handleCustomVarietyNameBlur = useCallback(() => {
    setValue('customVarietyName', internalCustomName);
  }, [setValue, internalCustomName]);

  const handleBloomDayChange = useCallback((varietyId: string, timestamp: number | null) => {
    setInternalBloomDays((prev) => ({
      ...prev,
      [varietyId]: timestamp || 0,
    }));
  }, []);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleWeightChange = useCallback((_varietyId: string, _gradeId: string, _value: string) => {
    // This is handled by the Controller in GradeInputPanel
  }, []);

  // API payload preparation
  const prepareApiPayload = useCallback(
    (formData: FormData, customName?: string) => {
      const varietiesMap: VarietiesMap = {};

      Object.entries(formData.weights || {}).forEach(([key, value]) => {
        if (!isValidValue(value)) return;

        const { varietyId, grade } = findVarietyAndGrade(key, varieties);

        if (!varietyId || !grade) return;

        const bloomDays =
          mode === ModalMode.CREATE ? (formData as CreateModeFormData).varietyBloomDays : varietyBloomDays;

        ensureVarietyInMap(varietyId, varietiesMap, bloomDays);

        varietiesMap[varietyId].grades.push({
          id: grade.id,
          weight: Number((value ?? '').trim()),
        });
      });

      const otherVariety = varieties.find((v) => v.value === 'other');

      if (otherVariety) {
        let otherGrade = otherVariety.grades.find((g) => g.value === 'D');

        if (!otherGrade && otherVariety.grades.length > 0) {
          otherGrade = otherVariety.grades[0];
        }

        if (otherGrade) {
          const currentName = customName || '';
          if (currentName.trim() !== '') {
            const bloomDays =
              mode === ModalMode.CREATE ? (formData as CreateModeFormData).varietyBloomDays : varietyBloomDays;

            // If variety already exists in map (has weights), update it with custom name
            if (varietiesMap[otherVariety.id]) {
              const _otherGrade = varietiesMap[otherVariety.id].grades.map((grade) => ({
                ...grade,
                name: currentName,
              }));
              varietiesMap[otherVariety.id] = {
                id: otherVariety.id,
                grades: _otherGrade,
                flowerBloomingDay: bloomDays[otherVariety.id] || 0,
                name: currentName,
              };
            } else {
              // If variety doesn't exist in map (no weights), create it with just the custom name
              varietiesMap[otherVariety.id] = {
                id: otherVariety.id,
                grades: [], // No grades since no weights were entered
                flowerBloomingDay: bloomDays[otherVariety.id] || 0,
                name: currentName,
              };
            }
          }
        }
      }

      const varietiesArray = Object.values(varietiesMap);

      return {
        update: {
          varieties: varietiesArray,
          ...(mode === ModalMode.CREATE && { customVarietyName: customName }),
        },
      };
    },
    [varieties, varietyBloomDays, mode]
  );

  // Form submission
  const onSubmit = useCallback(
    async (data: FormData) => {
      try {
        setIsSubmitting(true);
        setSubmitError(null);

        // Get current internal custom name value
        const currentCustomName = internalCustomName;

        // Validate custom variety name for CREATE mode
        if (mode === ModalMode.CREATE && data.customVarietyName) {
          const trimmedName = data.customVarietyName.trim();
          if (!trimmedName || !REGEX_INPUT_NAME_FIELD.test(trimmedName)) {
            setError('customVarietyName', {
              type: 'pattern',
              message: formTranslation('not-allow-characters'),
            });
            return;
          }
        }

        const payload = prepareApiPayload(
          {
            ...data,
            customVarietyName: currentCustomName,
          },
          currentCustomName
        );

        if (payload.update.varieties.length === 0) {
          handleClose();
          return;
        }

        if (mode === ModalMode.CREATE && onSaveCreate) {
          onSaveCreate(payload, varieties);
          handleClose();
        } else if (onSave) {
          await onSave(payload);
          handleClose();
        } else {
          handleClose();
        }
      } catch (error) {
        setSubmitError(error instanceof Error ? error.message : commonT('unknown-error'));
      } finally {
        setIsSubmitting(false);
      }
    },
    [
      mode,
      prepareApiPayload,
      internalCustomName,
      onSaveCreate,
      onSave,
      varieties,
      handleClose,
      setError,
      formTranslation,
      commonT,
    ]
  );

  // Effects - Initialize internal state when modal opens (only on open transition)
  useEffect(() => {
    const wasOpen = prevOpenRef.current;
    const isNowOpen = open;

    // Only initialize when modal transitions from closed to open
    if (!wasOpen && isNowOpen) {
      // Capture current prop values at the time of opening
      const currentInitialCustomName = initialCustomName;
      const currentVarietyBloomDays = varietyBloomDays;

      setInternalCustomName(currentInitialCustomName);
      // Always use the latest varietyBloomDays from parent - this is the source of truth
      setInternalBloomDays(currentVarietyBloomDays);
      setSubmitError(null);
      setAllZeroOrNegativeWarning(false);
    }

    // Update ref for next render
    prevOpenRef.current = open;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  // Effects - Reset form when modal opens (only on open transition)
  useEffect(() => {
    if (open) {
      // Capture current values to avoid dependency issues
      const currentInitialWeights = initialWeights;
      const currentMode = mode;

      const resetData = {
        weights: currentInitialWeights as Record<string, string | undefined>,
        // Use internal custom name to preserve user input during modal session
        customVarietyName: internalCustomName,
        ...(currentMode === ModalMode.CREATE && {
          // Use internal bloom days to preserve user selections during modal session
          varietyBloomDays: internalBloomDays,
        }),
        ...(currentMode === ModalMode.CONFIRM && {
          confirmReceiveHarvest: false,
          awareUndoAction: false,
        }),
      };

      reset(resetData as FormData);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  // Effects - Set selected variety when varieties are loaded
  useEffect(() => {
    if (open && varieties.length > 0 && !selectedVariety && firstVariety) {
      setSelectedVariety(firstVariety);
    }
  }, [open, varieties.length, selectedVariety, firstVariety]);

  // Memoize other variety ID to avoid infinite loops
  const otherVarietyId = useMemo(() => {
    const otherVariety = varieties.find((v) => v.value === 'other');
    return otherVariety?.id;
  }, [varieties]);

  useEffect(() => {
    const varietiesWithValues = new Set<string>();
    Object.entries(weights).forEach(([key, value]) => {
      if (value && value.trim() !== '') {
        const varietyId = key.split('-')[0];
        if (varietyId) {
          varietiesWithValues.add(varietyId);
        }
      }
    });

    if (customVarietyName && customVarietyName.trim() !== '' && otherVarietyId) {
      varietiesWithValues.add(otherVarietyId);
    }

    const newVarietiesWithData = Array.from(varietiesWithValues);
    setVarietiesWithData(newVarietiesWithData);
  }, [weights, customVarietyName, otherVarietyId]);

  useEffect(() => {
    if (selectedVariety && weights && config.showZeroNegativeWarning) {
      const selectedVarietyData = varieties.find((v) => v.id === selectedVariety);
      if (selectedVarietyData) {
        const gradeKeys = selectedVarietyData.grades.map((grade) => `${selectedVariety}-${grade.id}`);
        const enteredValues = gradeKeys.map((key) => weights[key]);

        // Filter out empty/undefined values to only check actual entered values
        const nonEmptyValues = enteredValues.filter((value): value is string =>
          value !== undefined && value !== null && value.trim() !== ''
        );

        const allValuesLessThanOrEqualToZero =
          nonEmptyValues.length > 0 &&
          nonEmptyValues.every((value) => {
            const numValue = parseFloat(value);
            return numValue <= 0;
          });

        setAllZeroOrNegativeWarning(allValuesLessThanOrEqualToZero);
      }
    } else {
      setAllZeroOrNegativeWarning(false);
    }
  }, [weights, selectedVariety, varieties, config.showZeroNegativeWarning]);

  // Render
  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      data-testid="receive-harvest-modal"
      slotProps={{
        paper: {
          sx: { borderRadius: 1 },
        },
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          p: 2,
          pb: 1,
        }}
      >
        <Typography component="div" variant="h6">
          {receiveTranslation(config.title as Parameters<typeof receiveTranslation>[0])}
        </Typography>
        <IconButton edge="end" onClick={handleClose} aria-label="close" disabled={isLoading}>
          <CloseIcon />
        </IconButton>
      </Box>

      {varietiesError && (
        <Box sx={{ p: 2, bgcolor: theme.palette.error.light, color: theme.palette.error.main }}>
          <Typography variant="body2">
            Error loading varieties:{' '}
            {varietiesError instanceof Error ? varietiesError.message : commonT('unknown-error')}
          </Typography>
        </Box>
      )}

      <DialogContent dividers sx={{ p: 0 }}>
        {isLoading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
            <CircularProgress />
          </Box>
        )}

        {!isLoading && (
          <Box component="form" sx={{ display: 'flex', height: '100%', px: 0 }}>
            <VarietySelectionPanel
              varieties={varieties}
              selectedVariety={selectedVariety}
              editableVarietyIds={editableVarietyIds}
              varietiesWithData={varietiesWithData}
              allZeroOrNegativeWarning={allZeroOrNegativeWarning}
              mode={mode}
              onVarietySelect={handleVarietySelect}
              checkVarietyHasData={checkVarietyHasData}
              isVarietyEditable={isVarietyEditable}
            />
            <GradeInputPanel
              selectedVariety={selectedVariety}
              varieties={varieties}
              varietyBloomDays={mode === ModalMode.CREATE ? internalBloomDays : varietyBloomDays}
              allZeroOrNegativeWarning={allZeroOrNegativeWarning}
              allZeroOrNegativeWarningMessage={allZeroOrNegativeWarningMessage}
              isSubmitting={isSubmitting}
              editableVarietyIds={editableVarietyIds}
              internalCustomName={internalCustomName}
              mode={mode}
              cuttingDay={cuttingDay}
              onCustomVarietyNameChange={handleCustomVarietyNameChange}
              onCustomVarietyNameBlur={handleCustomVarietyNameBlur}
              onBloomDayChange={handleBloomDayChange}
              control={control}
              errors={errors}
              onWeightChange={handleWeightChange}
            />
          </Box>
        )}
      </DialogContent>

      {submitError && (
        <Box sx={{ p: 2, bgcolor: theme.palette.error.light, color: theme.palette.error.main }}>
          <Typography variant="body2">{submitError}</Typography>
        </Box>
      )}

      <ModalActions
        mode={mode}
        isButtonEnabled={isButtonEnabled}
        isLoading={isLoading}
        isSubmitting={isSubmitting}
        onClose={handleClose}
        onSubmit={handleSubmit(onSubmit)}
        showCheckboxes={config.showCheckboxes}
        checkboxesProps={
          config.showCheckboxes
            ? {
                confirmReceiveHarvest,
                awareUndoAction,
                onConfirmReceiveHarvestChange: (checked: boolean) => setValue('confirmReceiveHarvest', checked),
                onAwareUndoActionChange: (checked: boolean) => setValue('awareUndoAction', checked),
              }
            : undefined
        }
      />
    </Dialog>
  );
};
