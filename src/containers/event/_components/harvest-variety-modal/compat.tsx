import React from 'react';
import { HarvestVarietyModal } from './HarvestVarietyModal';
import { 
  ReceiveHarvestModalCompatProps, 
  CreateVarietiesModalCompatProps,
  ModalMode 
} from './types';

// Backward compatibility wrapper for ReceiveHarvestModalV2
export const ReceiveHarvestModalCompat: React.FC<ReceiveHarvestModalCompatProps> = ({
  isEditReceiving,
  ...props
}) => {
  const mode = isEditReceiving ? ModalMode.EDIT : ModalMode.CONFIRM;
  
  return (
    <HarvestVarietyModal
      {...props}
      mode={mode}
    />
  );
};

// Backward compatibility wrapper for CreateVarietiesModal
export const CreateVarietiesModalCompat: React.FC<CreateVarietiesModalCompatProps> = ({
  onSave,
  ...props
}) => {
  return (
    <HarvestVarietyModal
      {...props}
      mode={ModalMode.CREATE}
      onSaveCreate={onSave}
    />
  );
};
