import { <PERSON><PERSON>, <PERSON>, Button, Chip, Paper, Typography } from '@mui/material';
import { Drawer } from 'components';
import dayjs from 'dayjs';
import { useTranslations } from 'next-intl';
import { FC } from 'react';
import { theme } from 'styles/theme';
import { formatNumberWithCommas } from 'utils';
import { formatDateWithLocale } from './common';
import { InfoOutline } from '@mui/icons-material';
import { useMasterDataStore } from 'store/useMaterDataStore';
import { DD_MMMM_YYYY_WITH_DASH } from 'constant/common';

interface OriginalDrawerProps {
  open: boolean;
  onClose: () => void;
  originalVariety?: {
    id: string;
    value?: string;
    displayText: string;
    flowerBloomingDay: number;
    grades: Array<{
      id: string;
      displayText: string;
      weight: number;
    }>;
    name?: string;
  };
  currentVariety?: {
    id: string;
    value?: string;
    displayText: string;
    flowerBloomingDay: number;
    grades: Array<{
      id: string;
      displayText: string;
      weight: number;
    }>;
  };
}

export const OriginalDrawer: FC<OriginalDrawerProps> = ({ onClose, open, originalVariety, currentVariety }) => {
  const { getGradeLabel, getVarietyLabel } = useMasterDataStore();
  const receivingTranslation = useTranslations('receive');
  const drawerTitle = `${receivingTranslation('original-draw-title')}`;

  return (
    <Drawer anchor="right" disableScrollLock drawerTitle={drawerTitle} open={open} onClose={onClose} hasActionBtn={false}>
      <Box
        sx={{
          width: '100%',
          display: 'flex',
          padding: '0px 8px',
          flexDirection: 'column',
          gap: '12px',
          alignSelf: 'stretch',
        }}
      >
        <Alert
          severity="info"
          icon={<InfoOutline />}
          sx={{
            p: '0px 8px',
            fontSize: '12px',
            m: 1,
            borderRadius: 2,
            '& .MuiAlert-message': {
              width: '100%',
            },
            background: theme.palette.customColors.primary100,
          }}
        >
          <Typography color="text.primary">{receivingTranslation('receive-alert')}</Typography>
        </Alert>
        <Box sx={{ p: 1, width: '100%', overflowY: 'auto', flex: 1 }}>
          {!originalVariety ? (
            <Typography color="text.secondary" sx={{ textAlign: 'center', mt: 4 }}>
              {receivingTranslation('receive-no-origin-data')}
            </Typography>
          ) : (
            <Paper
              key={originalVariety.id}
              elevation={0}
              sx={{
                mb: 2,
                p: 2,
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 2,
              }}
            >
              <Typography variant="h6">
                {originalVariety?.name ? originalVariety.name : getVarietyLabel(originalVariety.id)}
              </Typography>

              {originalVariety.flowerBloomingDay && (
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    mb: 2,
                    textDecorationColor: 'primary.main',
                    textDecorationThickness: '2px',
                  }}
                >
                  {receivingTranslation('receive-blooming-on')}{' '}
                  {formatDateWithLocale(dayjs.unix(originalVariety.flowerBloomingDay), DD_MMMM_YYYY_WITH_DASH)}
                </Typography>
              )}

              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {originalVariety.grades.map((grade) => {
                  const currentGrade = currentVariety?.grades.filter((item) => item.id === grade.id);
                  return (
                    <Chip
                      key={grade.id}
                      sx={{
                        backgroundColor: theme.palette.customColors.primary100,
                        borderColor: theme.palette.customColors.primary100,
                        textDecoration: currentGrade && grade.weight !== currentGrade[0]?.weight ? 'underline' : '',
                      }}
                      label={`${getGradeLabel(grade.id)} - ${formatNumberWithCommas(grade.weight)}`}
                      variant="outlined"
                      size="medium"
                    />
                  );
                })}
              </Box>
            </Paper>
          )}
        </Box>
      </Box>
      <Box sx={{ p: 2, mt: 'auto' }}>
        <Button
          fullWidth
          variant="outlined"
          onClick={onClose}
          sx={{
            borderRadius: 1,
            py: 1.5,
            color: theme.palette.customColors.blue500,
            borderColor: theme.palette.customColors.blue500,
            backgroundColor: theme.palette.customColors.blueTint,
            '&:hover': {
              backgroundColor: theme.palette.customColors.blue50,
              borderColor: theme.palette.customColors.blue700,
            },
          }}
        >
          {receivingTranslation('receive-close')}
        </Button>
      </Box>
    </Drawer>
  );
};
