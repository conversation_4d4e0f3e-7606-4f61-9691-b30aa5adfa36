'use client';

import { Done } from '@mui/icons-material';
import AgricultureOutlinedIcon from '@mui/icons-material/AgricultureOutlined';
import AgricultureIcon from '@mui/icons-material/Agriculture';
import MarkunreadMailboxIcon from '@mui/icons-material/MarkunreadMailbox';
import MarkunreadMailboxOutlinedIcon from '@mui/icons-material/MarkunreadMailboxOutlined';
import { Box, Card, CardContent, Grid, Modal, Typography } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { keyframes } from '@mui/system';
import logo from 'assets/icons/logo.svg';
import { TruckIcon, TruckSolidIcon } from 'assets/react-icons';
import { ClientOnly } from 'components/client-only';
import { motion } from 'framer-motion';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';
import React, { useEffect, useState } from 'react';
import { clientRoutes } from 'routes/client-routes';
import { useUserStore } from 'store/useUserStore';

// Define animations
const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const pulse = keyframes`
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
`;

const rotate = keyframes`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`;

const LoadingAnimation = () => {
  const theme = useTheme();

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'column',
        height: 'calc(100vh - 64px)',
        width: '100%',
      }}
    >
      <Box
        sx={{
          animation: `${rotate} 2s infinite linear, ${pulse} 3s infinite ease-in-out`,
          mb: 3,
        }}
      >
        <Image src={logo} width={100} height={100} alt="loading" />
      </Box>
      <Typography
        variant="h6"
        sx={{
          color: theme.palette.customColors.primary,
          animation: `${fadeIn} 1s ease-out`,
        }}
      >
        ...
      </Typography>
    </Box>
  );
};

const DashboardWelcome: React.FC<{ isLoading?: boolean }> = ({ isLoading }) => {
  const { user } = useUserStore();
  const commonT = useTranslations('common');
  const logoSize = 40;
  const theme = useTheme();
  const [isWelcome, setIsWelcome] = useState(false);
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);
  const now = new Date();
  const hours = now.getHours();
  const greeting = hours < 12 ? commonT('gm') : hours < 18 ? commonT('ga') : commonT('gn');

  useEffect(() => {
    const isWelcomeScreen = localStorage.getItem('isWelcome');
    if (isWelcomeScreen) {
      setIsWelcome(true);
      localStorage.removeItem('isWelcome');
      const timeoutId = setTimeout(() => {
        setIsWelcome(false);
      }, 3000);

      return () => clearTimeout(timeoutId);
    } else {
      setIsWelcome(false);
    }
  }, []);

  const mockQuickLinks = [
    {
      title: commonT('incoming-batch/lot'),
      path: clientRoutes.eventIncoming,
      icon: <AgricultureOutlinedIcon sx={{ fontSize: '40px' }} color="inherit" />,
      solidIcon: <AgricultureIcon sx={{ fontSize: '40px' }} color="primary" />,
    },
    {
      title: commonT('receiving-batch/lot'),
      path: clientRoutes.eventReceiving,
      icon: <MarkunreadMailboxOutlinedIcon sx={{ fontSize: '40px' }} color="inherit" />,
      solidIcon: <MarkunreadMailboxIcon sx={{ fontSize: '40px' }} color="primary" />,
    },
    {
      title: commonT('shipment'),
      path: clientRoutes.shipment,
      icon: <TruckIcon sx={{ fontSize: '40px' }} color="inherit" />,
      solidIcon: <TruckSolidIcon sx={{ fontSize: '40px' }} color="primary" />,
    },
  ];

  return (
    <ClientOnly>
      {isLoading ? (
        <LoadingAnimation />
      ) : (
        <Box
          sx={{
            p: { xs: '20px', md: '40px 20px' },
            width: '100%',
            minHeight: 'calc(100vh - 64px)',
            display: 'flex',
            justifyContent: 'flex-start',
            alignItems: 'center',
            flexDirection: 'column',
            overflow: 'auto',
          }}
        >
          <Box
            component={motion.div}
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
            sx={{
              textAlign: 'center',
              mb: 4,
            }}
          >
            <Box
              component="div"
              sx={{
                animation: `${pulse} 3s infinite ease-in-out`,
                display: 'inline-block',
              }}
            >
              <Image src={logo} width={150} height={150} alt="project_icon" />
            </Box>

            <Typography
              variant="h4"
              sx={{
                fontWeight: 600,
                mt: 2,
                animation: `${fadeIn} 1s ease-out`,
                background: theme.palette.customColors.primary,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              {greeting}, {`${user?.firstName ?? ''} ${user?.lastName ?? ''}`}!
            </Typography>

            <Typography
              variant="subtitle1"
              color="text.secondary"
              sx={{
                animation: `${fadeIn} 1s ease-out 0.3s forwards`,
                opacity: 0,
              }}
            >
              {commonT('welcome')}
            </Typography>
          </Box>

          <Grid
            container
            spacing={3}
            sx={{
              display: 'flex',
              justifyContent: 'center',
              mt: 2,
              px: { xs: 1, sm: 1 },
            }}
            width="100%"
          >
            {mockQuickLinks.map((link, index) => (
              <Grid
                size={{ md: 4, sm: 6, xs: 12 }}
                key={link.path}
                component={Link}
                href={link.path}
                sx={{
                  textDecoration: 'unset',
                  maxWidth: '400px',
                }}
              >
                <Card
                  component={motion.div}
                  initial={{ y: 50, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.1 * index }}
                  onMouseEnter={() => setHoveredCard(index)}
                  onMouseLeave={() => setHoveredCard(null)}
                  sx={{
                    height: '100%',
                    borderRadius: 2,
                    transition: 'transform 0.3s, box-shadow 0.3s',
                    '&:hover': {
                      transform: 'translateY(-5px)',
                      boxShadow: theme.shadows[10],
                      color: theme.palette.customColors.primary,
                    },
                    cursor: 'pointer',
                    background: theme.palette.customColors.gradientAppBgColor,
                  }}
                >
                  <CardContent sx={{ textAlign: 'center', p: 3 }}>
                    <Box sx={{ color: 'inherit', mb: 2 }}>{hoveredCard === index ? link.solidIcon : link.icon}</Box>
                    <Typography variant="h6" sx={{ mb: 1, fontWeight: 500, color: 'inherit' }}>
                      {link.title}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}
      <Modal open={isWelcome} aria-labelledby="modal-modal-title" aria-describedby="modal-modal-description">
        <Box
          sx={{
            position: 'absolute',
            top: '0',
            left: '0',
            right: '0',
            bottom: '0',
            width: '100vw',
            height: '100vh',
            bgcolor: 'background.paper',
            boxShadow: 24,
            gap: 2,
          }}
        >
          <Box
            sx={{
              display: 'flex',
              gap: 1,
              alignItems: 'center',
              height: 64,
              width: '100%',
              background: theme.palette.customColors.gradientAppBgColor,
              transition: theme.transitions.create(['opacity', 'width'], {
                easing: theme.transitions.easing.easeInOut,
                duration: theme.transitions.duration.complex,
              }),
              justifyContent: 'start',
              paddingLeft: 4,
              position: 'relative',
            }}
          >
            <Image unoptimized src={logo} width={logoSize} height={logoSize} alt="logo" />
            <Typography
              variant="body1"
              fontWeight={600}
              fontSize={18}
              color="black"
              noWrap
              sx={{
                transition: theme.transitions.create(['opacity', 'width'], {
                  easing: theme.transitions.easing.easeInOut,
                  duration: theme.transitions.duration.complex,
                }),
              }}
            >
              {commonT('app-name')}
            </Typography>
          </Box>
          <Box
            sx={{
              padding: '20px',
              display: 'flex',
              justifyContent: 'center',
              flexDirection: 'column',
              width: '100%',
              alignItems: 'center',
              gap: 1,
              height: '80vh',
            }}
          >
            <Done sx={{ color: theme.palette.customColors.primary, fontSize: '10rem' }} />
            <Box sx={{ fontWeight: '700', fontSize: '20px' }}>{commonT('welcome-yup')}</Box>
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', flexDirection: 'column' }}>
              <Typography>{commonT('created-account')}</Typography>
              <Typography>{commonT('account-welcome-desc')}</Typography>
            </Box>
          </Box>
          <Box
            sx={{
              position: 'absolute',
              bottom: '0',
              display: 'flex',
              justifyContent: 'center',
              alignContent: 'center',
              width: '100%',
              gap: 1,
            }}
          >
            <Typography>{commonT('powered-by')}</Typography>
            <Link href="">{commonT('bks')}</Link>
          </Box>
        </Box>
      </Modal>
    </ClientOnly>
  );
};

export default DashboardWelcome;
