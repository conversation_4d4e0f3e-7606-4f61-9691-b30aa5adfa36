/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';
import { Box, Button, Checkbox, FormControlLabel, Typography } from '@mui/material';
import { WarningDialog } from 'components';
import { basePathProd } from 'configs/app-config';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { FC, useEffect, useRef, useState } from 'react';
import sanitizeHtml from 'sanitize-html';
import { theme } from 'styles/theme';
import { getCookieLocale } from 'utils/cookie-client';
import { logger } from 'utils/logger';

type EventDetailProps = {
  onDeclined?: () => void;
  onAccept?: (data: any) => void;
};

export const PDPA: FC<EventDetailProps> = ({ onAccept, onDeclined }) => {
  const [isCheck, setIsCheck] = useState<boolean>(false);
  const [isAtEnd, setIsAtEnd] = useState(false);
  const containerRef = useRef<HTMLDivElement | null>(null);
  const [isDeclinedShow, setIsDeclinedShow] = useState<boolean>(false);

  const commonT = useTranslations('common');

  const checkScrollEnd = () => {
    const container = containerRef.current;
    if (!container) return;
    if (!container.scrollTop) {
      return;
    }
    const threshold = 5;
    const isScrolledToEnd = container.scrollTop + container.clientHeight >= container.scrollHeight - threshold;
    if (isScrolledToEnd) {
      setIsAtEnd(true);
    }
  };

  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScrollEnd);

      // Check initial state
      checkScrollEnd();

      // Clean up
      return () => {
        container.removeEventListener('scroll', checkScrollEnd);
      };
    }
  }, []);

  const handleDeclinedButton = () => {
    setIsDeclinedShow(true);
  };

  const handleCancelDecline = () => {
    setIsDeclinedShow(false);
  };
  const handleAcceptDecline = () => {
    setIsDeclinedShow(false);
    return onDeclined && onDeclined();
  };

  const [html, setHtml] = useState('');

  useEffect(() => {
    const locale = getCookieLocale();
    fetch(`${basePathProd}/assets/privacy/${locale === 'en' ? 'EN_Privacy' : 'TH_Privacy'}.html`)
      .then((response) => response.text())
      .then((data) => {
        setHtml(data);
      })
      .catch((error) => logger.error('Error loading HTML file:', { error }));
  }, []);

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          flexDirection: 'column',
          width: '100%',
          alignItems: 'center',
          paddingBottom: 1,
        }}
      >
        <div
          ref={containerRef}
          style={{
            height: '60vh',
            overflowY: 'scroll',
            width: '50vw',
            margin: 32,
            fontSize: '14px',
            boxShadow: '0px 2px 4px -2px rgba(0, 0, 0, 0.1), 0px 4px 6px -1px rgba(0, 0, 0, 0.1)',
            borderRadius: 2,
          }}
          dangerouslySetInnerHTML={{ __html: sanitizeHtml(html) || '' }}
        />

        <Box sx={{ display: 'flex', alignItems: 'center', flexDirection: 'column', gap: 3 }}>
          <Box>
            <FormControlLabel
              control={
                <Checkbox
                  value={isCheck}
                  onChange={(e) => {
                    setIsCheck(e.target.checked);
                  }}
                />
              }
              label={commonT('accept-policy')}
            />
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', gap: 2 }}>
            <Button onClick={handleDeclinedButton} variant="outlined" sx={{ width: '200px' }}>
              {commonT('decline')}
            </Button>
            <Button onClick={onAccept} variant="contained" disabled={!(isAtEnd && isCheck)} sx={{ width: '200px' }}>
              {commonT('accept')}
            </Button>
          </Box>

          <Typography sx={{ color: theme.palette.customColors.neutral500 }}>
            {commonT('please-scroll-down-to-read-all-sections')}
          </Typography>

          <Box
            sx={{
              position: 'absolute',
              bottom: '0',
              display: 'flex',
              justifyContent: 'center',
              alignContent: 'center',
              width: '100%',
              gap: 1,
            }}
          >
            <Typography>{commonT('powered-by')}</Typography>
            <Link href="">{commonT('bks')}</Link>
          </Box>
        </Box>
      </Box>
      <WarningDialog isOpen={isDeclinedShow} onConfirm={handleAcceptDecline} onCancel={handleCancelDecline} />
    </>
  );
};
