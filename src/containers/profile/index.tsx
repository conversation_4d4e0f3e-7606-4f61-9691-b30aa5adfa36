'use client';
import { Avatar, Box, Grid, Typography } from '@mui/material';
import { DetailRow } from 'components';
import { get } from 'lodash-es';
import { useTranslations } from 'next-intl';
import { FC, useMemo } from 'react';
import { theme } from 'styles/theme';
import { User } from 'types/user';
import { getImageUrl } from 'utils';
import { getCookieLocale } from 'utils/cookie-client';

type EventDetailProps = {
  user: User;
};

export const Profile: FC<EventDetailProps> = ({ user }) => {
  const profileT = useTranslations('profile');

  const avatarUrl = useMemo(() => {
    const filenameDisk = get(user, 'avatar.filenameDisk', '');

    return getImageUrl(filenameDisk);
  }, [user]);

  const locale = getCookieLocale() ?? 'th';

  return (
    <Box sx={{ padding: '20px', backgroundColor: theme.palette.customColors.lightGray, flex: 1 }}>
      <Typography variant="h5" component="h1" fontWeight="bold" sx={{ mb: { xs: 2, sm: 0 } }}>
        {profileT('my-profile')}
      </Typography>
      <Grid container spacing={2} sx={{ width: '100%' }}>
        {/* Detail */}
        <Grid
          size={{
            xs: 12,
            md: 12,
            lg: 12,
            xl: 6,
          }}
        >
          <Box sx={{ display: 'flex', gap: 2, marginTop: 2 }}>
            <Box
              sx={{
                flex: 1,
                borderRadius: '8px',
                border: '1px solid #0000001A',
                background: 'white',
                height: 'auto',
                padding: 2,
              }}
              component={'div'}
            >
              <Typography sx={{ fontWeight: 600 }} variant="body1" mb={2}>
                {profileT('personal-information')}
              </Typography>
              <Box
                sx={{
                  flex: 1,
                  display: 'flex',
                  gap: 1,
                  height: 'auto',
                  overflowY: 'auto',
                }}
              >
                <Box sx={{ display: 'flex', flex: 1, justifyContent: 'start', alignItems: 'center' }}>
                  {avatarUrl ? (
                    <Avatar sx={{ width: 180, height: 180, objectFit: 'cover' }} src={avatarUrl} alt="user-image" />
                  ) : (
                    <Avatar sx={{ width: 180, height: 180 }}>{user.lastName.split('')[0]}</Avatar>
                  )}
                </Box>
                <Box sx={{ flex: 1 }}>
                  <DetailRow title={profileT('first-name')} content={user.firstName} noBorder />
                  <DetailRow title={profileT('phone-number')} content={user.phoneNumber ? `0${user.phoneNumber}` : '--'} noBorder />
                </Box>
                <Box sx={{ flex: 1 }}>
                  <DetailRow title={profileT('last-name')} content={user.lastName} noBorder />
                  <DetailRow
                    sx={{ textTransform: 'capitalize' }}
                    title={profileT('role')}
                    content={user.profile.roleLabel[locale]}
                    noBorder
                  />
                </Box>
              </Box>
            </Box>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};
