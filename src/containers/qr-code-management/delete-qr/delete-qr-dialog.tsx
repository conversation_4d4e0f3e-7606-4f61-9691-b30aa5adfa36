'use client';

import { useTranslations } from 'next-intl';
import { FC, useState } from 'react';
import { Dialog } from 'components';
import { useDeleteAvailableQrMutate } from 'hooks/mutates/useDeleteAvailableQrMutate';

type DeleteDialogState = { open: boolean; deletedId: string; onConfirmDelete?: () => void };

const initialDeleteDialogState: DeleteDialogState = {
  open: false,
  deletedId: '',
};

export const useDeleteQrDialogProps = () => {
  const [dialogState, setDialogState] = useState<DeleteDialogState>(initialDeleteDialogState);

  const onOpen = (state: Omit<DeleteDialogState, 'open'>) => {
    setDialogState({ open: true, ...state });
  };

  const onClose = () => {
    setDialogState(initialDeleteDialogState);
  };

  return {
    dialogState,
    onOpen,
    onClose,
  };
};

export const DeleteQrDialog: FC<Omit<ReturnType<typeof useDeleteQrDialogProps>, 'onOpen'>> = (props) => {
  const { dialogState, onClose } = props;
  const qrT = useTranslations('qr');

  const { mutateAsync } = useDeleteAvailableQrMutate();

  const handleDelete = async (id: string) => {
    onClose();
    dialogState.onConfirmDelete?.();
    await mutateAsync(id);
  };

  return (
    <Dialog
      isOpen={dialogState.open}
      title={qrT('delete-this-qr-label')}
      content={qrT('delete-this-qr-content')}
      okButtonText={qrT('delete')}
      onConfirm={() => {
        if (dialogState.deletedId) {
          handleDelete(dialogState.deletedId);
        }
      }}
      onCancel={onClose}
      type="danger"
    />
  );
};
