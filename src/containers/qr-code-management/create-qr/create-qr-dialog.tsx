/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import CloseIcon from '@mui/icons-material/Close';
import {
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogProps,
  IconButton,
  Typography,
} from '@mui/material';
import { useTranslations } from 'next-intl';
import { FC, useRef, useState } from 'react';
import { FormProvider, useForm, useFormContext } from 'react-hook-form';
import { theme } from 'styles/theme';
import { CreateQrFormSchema, createQrSchema, initialQrSchema } from './schema';
import { CreateQrForm } from './create-qr-form';
import { useCreateAvailableQrMutate } from 'hooks/mutates/useCreateAvailableQrMutate';
import { removeEmptyFields } from 'utils';
import { CreateAvailableQrPayload } from 'types';
import { debounce } from 'lodash-es';

type CreateQrDialogState = {
  open: boolean;
};

const initialCreateQrDialogState: CreateQrDialogState = {
  open: false,
};

export const useCreateQrDialogProps = () => {
  const [dialogState, setDialogState] = useState<CreateQrDialogState>(initialCreateQrDialogState);

  const onOpen = () => {
    setDialogState({ open: true });
  };

  const onClose = () => {
    setDialogState(initialCreateQrDialogState);
  };

  return {
    dialogState,
    onOpen,
    onClose,
  };
};

export const CreateQrDialog: FC<Omit<ReturnType<typeof useCreateQrDialogProps>, 'onOpen'>> = (props) => {
  const qrT = useTranslations('qr');

  const form = useForm<CreateQrFormSchema>({
    resolver: zodResolver(createQrSchema(qrT)),
    mode: 'onChange',
    defaultValues: initialQrSchema,
  });

  return (
    <FormProvider {...form}>
      <CreateQrDialogInner {...props} />
    </FormProvider>
  );
};

const CreateQrDialogInner: FC<Omit<ReturnType<typeof useCreateQrDialogProps>, 'onOpen'>> = (props) => {
  const { dialogState, onClose } = props;

  const qrT = useTranslations('qr');
  const commonT = useTranslations('common');
  const ref = useRef<HTMLDivElement>(null);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [key, setKey] = useState('');

  const { handleSubmit, reset } = useFormContext<CreateQrFormSchema>();
  const { mutateAsync } = useCreateAvailableQrMutate();

  const resetForm = () => {
    reset(undefined, {
      keepDefaultValues: true,
    });
  };

  const handleBackdropClose: DialogProps['onClose'] = (_, reason) => {
    if (reason !== 'backdropClick') {
      handleClose();
    }
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const onSubmit = async (data: CreateQrFormSchema) => {
    try {
      setIsSubmitting(true);
      setSubmitError(null);

      const payload = removeEmptyFields({
        nameOfExportingCompany: data.company,
        orchardRegisterNumber: data.orchardNumber.replaceAll(/\D/g, ''),
        packingDate: data.packingDate.unix(),
        productType: data.productType,
        description: (data.description ?? '').trim(),
        exportTo: (data.exportTo ?? '').trim(),
      }) as CreateAvailableQrPayload;

      const res = await mutateAsync(payload);
      setKey(res.id);

      resetForm();
      debounce(() => {
        if (!data.createAnother) {
          onClose();
          return;
        }
        ref.current?.scrollTo({
          top: 0,
          left: 0,
          behavior: 'smooth',
        });
      }, 300)();
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : commonT('unknown-error'));
    } finally {
      setIsSubmitting(false);
    }
  };

  const isLoading = isSubmitting;

  return (
    <Dialog
      open={dialogState.open}
      onClose={handleBackdropClose}
      maxWidth="sm"
      fullWidth
      slotProps={{
        paper: {
          sx: { borderRadius: 1 },
        },
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          p: 2,
          pb: 1,
        }}
      >
        <Typography component="div" variant="h6">
          {qrT('create-qr-code')}
        </Typography>
        <IconButton edge="end" onClick={handleClose} aria-label="close" disabled={isLoading}>
          <CloseIcon />
        </IconButton>
      </Box>

      <DialogContent ref={ref} dividers sx={{ p: 3, pb: 0, border: 0 }}>
        <CreateQrForm key={key} />
      </DialogContent>

      {isLoading && (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            width: '100%',
            position: 'absolute',
            top: 0,
            left: 0,
          }}
        >
          <CircularProgress />
        </Box>
      )}
      {submitError && (
        <Box sx={{ p: 2, bgcolor: theme.palette.error.light, color: theme.palette.error.main }}>
          <Typography variant="body2">{submitError}</Typography>
        </Box>
      )}

      <DialogActions sx={{ px: 3, py: 2 }}>
        <Button
          onClick={handleClose}
          variant="outlined"
          size="large"
          disabled={isLoading}
          sx={{
            px: 3,
            fontSize: '0.875rem',
            fontWeight: 500,
            width: 'calc(50% - 4px)',
          }}
        >
          {qrT('cancel')}
        </Button>
        <Button
          onClick={handleSubmit(onSubmit)}
          variant="contained"
          color="primary"
          size="large"
          disabled={isLoading}
          startIcon={isSubmitting ? <CircularProgress size={20} color="inherit" /> : null}
          sx={{
            px: 3,
            fontSize: '0.875rem',
            fontWeight: 500,
            width: 'calc(50% - 4px)',
          }}
        >
          {qrT('create')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
