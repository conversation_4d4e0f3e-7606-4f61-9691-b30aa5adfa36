import dayjs, { Dayjs } from 'dayjs';
import { useTranslations } from 'next-intl';
import { z } from 'zod';

const orchardRegex = /\d{2}-\d{4}-\d{2}-\d{3}-\d{6}$|^\d{17}$/;

export const createQrSchema = (t: ReturnType<typeof useTranslations>) =>
  z.object({
    company: z
      .string({
        required_error: t('message-field-is-required', { field: t('name-of-the-exporting-company') }),
      })
      .min(1, t('message-field-is-required', { field: t('name-of-the-exporting-company') })),
    orchardNumber: z
      .string({ required_error: t('message-field-is-required', { field: t('orchard-register-number') }) })
      .min(1, t('message-field-is-required', { field: t('orchard-register-number') }))
      .regex(orchardRegex, t('message-orchard-format')),
    packingDate: z.custom<Dayjs>((val) => dayjs.isDayjs(val) && val.isValid(), {
      message: t('message-field-is-required', { field: t('packing-date') }),
    }),
    productType: z.string({ required_error: t('message-field-is-required', { field: t('product-type') }) }),
    exportTo: z
      .string({ required_error: t('message-field-is-required', { field: t('export-to') }) })
      .min(1, t('message-field-is-required', { field: t('export-to') })),
    description: z.string().optional(),

    createAnother: z.boolean().optional(),
  });

export type CreateQrFormSchema = z.infer<ReturnType<typeof createQrSchema>>;

export const initialQrSchema: Partial<CreateQrFormSchema> = {
  packingDate: dayjs().startOf('day'),
  exportTo: `THE PEOPLE'S REPUBLIC OF CHINA`,
};
