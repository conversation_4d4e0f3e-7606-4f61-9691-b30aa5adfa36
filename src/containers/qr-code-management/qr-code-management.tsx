'use client';

import AddIcon from '@mui/icons-material/Add';
import { Box, Button, IconButton, Typography } from '@mui/material';
import { type GridColDef, type GridRenderCellParams } from '@mui/x-data-grid';
import { formatDateWithLocale, renderStatus } from 'containers/event/_components';
import { useDeviceHeight } from 'hooks/useDeviceHeight';
import { useGetEventStatus } from 'hooks/useGetEventStatus';
import { headerHeight } from 'layouts/main/constant';
import { useTranslations } from 'next-intl';
import { MouseEventHandler, useEffect, useMemo } from 'react';
import { useToastStore } from 'store/useToastStore';
import { theme } from 'styles/theme';
import { EventStatusEnum, PackingHouse } from 'types';
import { formatGap, getStatus } from 'utils';
import toastMessages from 'utils/toastMessages';
import { EventDataTable } from 'components/event-table';
import { DeleteOutlineOutlined, RemoveRedEyeOutlined } from '@mui/icons-material';
import { DeleteQrDialog, useDeleteQrDialogProps } from './delete-qr/delete-qr-dialog';
import { omit } from 'lodash-es';
import { CreateQrDialog, useCreateQrDialogProps } from './create-qr/create-qr-dialog';
import { useViewQrDialogProps, ViewQrDialog } from './view-qr/view-qr-dialog';
import { useMasterDataStore } from 'store/useMaterDataStore';
import { queryKeys } from 'hooks/queries/_key';
import { useFeatureFlag } from 'hooks/useFeatureFlag';
import { redirect } from 'next/navigation';
import { basePathProd } from 'configs/app-config';

const DEFAULT_VALUE = '--';

export const QrCodeManagement = () => {
  const { ENABLED_QR_MANAGEMENT } = useFeatureFlag();

  if (!ENABLED_QR_MANAGEMENT) {
    return redirect(`${basePathProd}/not-found`);
  }

  return <QrCodeManagement_ />;
};

const QrCodeManagement_ = () => {
  const { toast: toastNotification, resetToast } = useToastStore();
  const qrT = useTranslations('qr');
  const filterT = useTranslations('filter');
  const commonT = useTranslations('common');

  const { getProductTypeLabel } = useMasterDataStore();

  const deviceHeight = useDeviceHeight();

  const deleteQrDialogProps = useDeleteQrDialogProps();
  const createQrDialogProps = useCreateQrDialogProps();
  const viewQrDialogProps = useViewQrDialogProps();

  const { getStatusLabel } = useGetEventStatus();

  useEffect(() => {
    if (toastNotification) {
      toastMessages[toastNotification.type](toastNotification.message);
      resetToast();
    }
  }, [resetToast, toastNotification]);

  const handleClickCreate = () => {
    createQrDialogProps.onOpen();
  };

  const viewQrDetail = (viewedId: string) => {
    viewQrDialogProps.onOpen({
      viewedId: viewedId,
      onDelete: () => deleteQrDialogProps.onOpen({ deletedId: viewedId, onConfirmDelete: viewQrDialogProps.onClose }),
    });
  };

  const renderAction = (params: GridRenderCellParams<Partial<PackingHouse>>) => {
    const handleViewClick: MouseEventHandler = (event) => {
      event.stopPropagation();
      event.preventDefault();
      viewQrDetail(params.row.id!);
    };

    const handleClickDelete: MouseEventHandler = (event) => {
      event.stopPropagation();
      event.preventDefault();
      deleteQrDialogProps.onOpen({ deletedId: params.row.id! });
    };

    const productStatus = params.row.status;
    const status = getStatus('qr', productStatus || 'available');

    return (
      <Box>
        <IconButton size="small" onClick={handleViewClick}>
          <RemoveRedEyeOutlined fontSize="small" />
        </IconButton>
        <IconButton
          disabled={status !== EventStatusEnum.AVAILABLE}
          size="small"
          color="error"
          onClick={handleClickDelete}
        >
          <DeleteOutlineOutlined fontSize="small" />
        </IconButton>
      </Box>
    );
  };

  const customTableHeight = useMemo<number>(() => {
    if (deviceHeight) {
      const paddingHeight = 210;

      return deviceHeight - headerHeight - paddingHeight;
    }

    return 0;
  }, [deviceHeight]);

  const columns: GridColDef<Partial<PackingHouse>>[] = [
    {
      field: 'qrcodeId',
      headerName: qrT('qr-code-id'),
      width: 200,
      renderHeader: () => <Typography sx={{ fontWeight: 'bold' }}>{qrT('qr-code-id')}</Typography>,
      sortable: true,
      valueFormatter: (value) => {
        return value ?? DEFAULT_VALUE;
      },
    },
    {
      field: 'orchardRegisterNumber',
      headerName: qrT('orchard-register-number'),
      width: 300,
      renderHeader: () => <Typography sx={{ fontWeight: 'bold' }}>{qrT('orchard-register-number')}</Typography>,
      sortable: true,
      valueFormatter: (value) => {
        return value ? `${commonT('prefix-plot')} ${formatGap(value)}` : DEFAULT_VALUE;
      },
    },
    {
      field: 'productTypeLabelEn',
      headerName: qrT('product-type'),
      width: 180,
      renderHeader: () => <Typography sx={{ fontWeight: 'bold' }}>{qrT('product-type')}</Typography>,
      sortable: true,
      renderCell: (params) => {
        const type = params.row.productType;
        return type ? getProductTypeLabel(type) : DEFAULT_VALUE;
      },
    },
    {
      field: 'status',
      headerName: qrT('status'),
      width: 172,
      renderHeader: () => <Typography sx={{ fontWeight: 'bold' }}>{qrT('status')}</Typography>,
      renderCell: (params: GridRenderCellParams<Partial<PackingHouse>>) => {
        const { eventStatus, eventStatusLabel } = getStatusLabel(params.row.status!, 'qr');
        return renderStatus(eventStatus, eventStatusLabel);
      },
      sortable: true,
    },
    {
      field: 'shipmentAssigned',
      headerName: qrT('shipment-assigned'),
      width: 240,
      sortable: false,
      valueFormatter: (value) => {
        return value || DEFAULT_VALUE;
      },
    },
    {
      field: 'packageAssigned',
      headerName: qrT('package-assigned'),
      width: 200,
      sortable: false,
      valueFormatter: (value) => {
        return value || DEFAULT_VALUE;
      },
    },
    {
      field: 'dateCreated',
      headerName: qrT('created-date'),
      width: 168,
      renderHeader: () => <Typography sx={{ fontWeight: 'bold' }}>{qrT('created-date')}</Typography>,
      sortable: true,
      valueFormatter: (value) => {
        return value ? formatDateWithLocale(value * 1000) : DEFAULT_VALUE;
      },
    },
    {
      field: 'actions',
      headerName: qrT('actions'),
      flex: 1,
      renderHeader: () => <Typography sx={{ fontWeight: 'bold' }}>{qrT('actions')}</Typography>,
      renderCell: renderAction,
      sortable: false,
      minWidth: 100,
    },
  ];

  const filterStatusOptions = [
    {
      label: filterT('status-available'),
      value: 'available',
    },
    {
      label: filterT('status-assigned'),
      value: 'assigned',
    },
  ];

  return (
    <>
      <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
        <Box
          component="main"
          sx={{ flexGrow: 1, p: { xs: 2, sm: 3 }, backgroundColor: theme.palette.customColors.lightGray }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3, flexWrap: 'wrap' }}>
            <Typography variant="h5" component="h1" fontWeight="bold" sx={{ mb: { xs: 2, sm: 0 } }}>
              {qrT('qr-management')}
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleClickCreate}
              sx={{
                backgroundColor: theme.palette.customColors.primaryMain,
                '&:hover': {
                  backgroundColor: theme.palette.customColors.primaryHover,
                },
                width: { xs: '100%', sm: 'auto' },
              }}
            >
              {qrT('create-qr-code')}
            </Button>
          </Box>
          <EventDataTable
            filterStatusOptions={filterStatusOptions}
            tableHeight={customTableHeight}
            columns={columns}
            eventType={'qr'}
            queryKey={queryKeys.QR}
            defaultSortModel={[
              {
                field: 'dateCreated',
                sort: 'desc',
              },
            ]}
            onRowClick={(params) => viewQrDetail(params.row.id!)}
          />
        </Box>
      </Box>

      <DeleteQrDialog {...omit(deleteQrDialogProps, 'onOpen')} />
      <CreateQrDialog {...omit(createQrDialogProps, 'onOpen')} />
      <ViewQrDialog {...omit(viewQrDialogProps, 'onOpen')} />
    </>
  );
};
