/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { Box, Grid, SxProps } from '@mui/material';
import { useTranslations } from 'next-intl';
import { FC } from 'react';
import { colors } from 'styles/colors';
import Image from 'next/image';
import dateSvg from 'assets/icons/calendar.svg';
import boxSvg from 'assets/icons/product-type.svg';
import { QrDetail } from 'types';
import { useMasterDataStore } from 'store/useMaterDataStore';
import { formatDateWithLocale } from 'containers/event/_components';

type Props = {
  qrDetail: QrDetail;
};

export const QrExtraContents: FC<Props> = (props) => {
  const { qrDetail } = props;

  const qrT = useTranslations('qr');

  const { getProductTypeLabel } = useMasterDataStore();

  return (
    <Grid container spacing={0.5}>
      <Grid size={6}>
        <Box sx={boxContentSx}>
          <Image src={dateSvg} alt="" width={16} height={16} />
          {qrT('packing-date')}: {formatDateWithLocale(qrDetail.packingDate * 1000)}
        </Box>
      </Grid>
      <Grid size={6}>
        <Box sx={boxContentSx}>
          <Image src={boxSvg} alt="" width={16} height={16} />
          {qrT('product-type')}: {getProductTypeLabel(qrDetail.productType)}
        </Box>
      </Grid>
      {(qrDetail.description ?? '').trim().length ? (
        <Grid size={12}>
          <Box sx={boxContentSx}>{qrDetail.description}</Box>
        </Grid>
      ) : null}
    </Grid>
  );
};

const boxContentSx: SxProps = {
  background: colors.primary50,
  borderRadius: '2px',
  padding: '4px 8px',
  display: 'flex',
  alignItems: 'center',
  gap: '4px',
  color: colors.neutral500,
  fontSize: '14px',
  fontWeight: 400,
};
