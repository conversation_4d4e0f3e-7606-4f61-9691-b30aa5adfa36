/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { DeleteOutlineOutlined } from '@mui/icons-material';
import CloseIcon from '@mui/icons-material/Close';
import {
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  Divider,
  IconButton,
  Stack,
  Typography,
} from '@mui/material';
import { FC, useMemo, useState } from 'react';
import { colors } from 'styles/colors';
import Image from 'next/image';
import qrOnlySvg from 'assets/icons/qr-only.svg';
import qrLabelSvg from 'assets/icons/qr-label.svg';
import { useGetEventStatus } from 'hooks/useGetEventStatus';
import { renderStatus } from 'containers/event/_components';
import { useQrDetailQuery } from 'hooks/queries/useQrDetailQuery';
import { EventStatusEnum, QrDetail } from 'types';
import { formatDate } from 'utils';
import { useMasterDataStore } from 'store/useMaterDataStore';
import { useGenerateQrUrlMutate } from 'hooks/queries/useGenerateQrUrlMutate';
import QrCodeReview from 'components/qr-code-review/qr-code-review';
import { formatOrchardNo } from 'utils/format';
import { QrExtraContents } from './qr-extra-contents';

type ViewQrDialogState = {
  open: boolean;
  viewedId: string;
  onDelete?: () => void;
};

const initialViewQrDialogState: ViewQrDialogState = {
  open: false,
  viewedId: '',
};

export const useViewQrDialogProps = () => {
  const [dialogState, setDialogState] = useState<ViewQrDialogState>(initialViewQrDialogState);

  const onOpen = (state: Omit<ViewQrDialogState, 'open'>) => {
    setDialogState({ open: true, ...state });
  };

  const onClose = () => {
    setDialogState(initialViewQrDialogState);
  };

  return {
    dialogState,
    onOpen,
    onClose,
  };
};

type Props = Omit<ReturnType<typeof useViewQrDialogProps>, 'onOpen'>;

export const ViewQrDialog: FC<Props> = (props) => {
  const { dialogState, onClose } = props;

  const [url, setUrl] = useState('');

  const { mutate } = useGenerateQrUrlMutate();
  const { data, isFetching } = useQrDetailQuery(dialogState.viewedId, (qrDetail) => {
    if (!qrDetail) return;
    mutate(
      {
        quantity: 1,
        nameOfExportingCompany: [qrDetail.nameOfExportingCompany || ''],
      },
      {
        onSuccess: (res) => {
          setUrl(res[0].qrUrl);
        },
      }
    );
  });

  const renderDialogContent = () => {
    if (isFetching || !data || !url) {
      return (
        <>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              px: 3,
              pt: 2,
              pb: 1,
            }}
          >
            <Typography component="div" variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              QR--
            </Typography>
            <IconButton edge="end" onClick={onClose} aria-label="close" disabled={isFetching}>
              <CloseIcon />
            </IconButton>
          </Box>
          <DialogContent dividers sx={{ px: 3, border: 0 }}>
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '480px' }}>
              <CircularProgress />
            </Box>
          </DialogContent>
        </>
      );
    }

    return <ViewQrDialogInner {...props} qrDetail={data} url={url} />;
  };

  return (
    <Dialog
      open={dialogState.open}
      onClose={onClose}
      fullWidth
      slotProps={{
        paper: {
          sx: { borderRadius: 1, width: '680px', maxWidth: 'unset', m: 0 },
        },
      }}
    >
      {renderDialogContent()}
    </Dialog>
  );
};

const ViewQrDialogInner: FC<
  Props & {
    qrDetail: QrDetail;
    url: string;
  }
> = (props) => {
  const { dialogState, onClose, qrDetail, url } = props;

  const { getStatusLabel } = useGetEventStatus();
  const { getProductTypeLabel } = useMasterDataStore();
  const { eventStatus, eventStatusLabel } = getStatusLabel(qrDetail.status ?? 'available', 'qr');

  const qrExportData = useMemo(
    () => ({
      qrUrl: url,
      exportCompany: qrDetail.nameOfExportingCompany,
      orchardRegisterNumber: `AC ${formatOrchardNo(qrDetail.orchardRegisterNumber)}`,
      packingDate: formatDate(qrDetail.packingDate * 1000),
      boxType: getProductTypeLabel(qrDetail.productType),
      exportTo: qrDetail.exportTo ?? '',
      packingHouseRegisterNumber: qrDetail.packingHouseDoaNumber ?? '',
      batchNumber: qrDetail.batchlot ?? '',
      productOf: 'Thailand',
      brand: 'Fresh Durian',
      variety: '',
      grade: '',
      totalBoxes: '',
      netWeight: '',
    }),
    [getProductTypeLabel, qrDetail, url]
  );

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          px: 3,
          pt: 2,
          pb: 1,
        }}
      >
        <Typography
          component="div"
          sx={{ display: 'flex', alignItems: 'center', gap: 1, fontSize: '20px', fontWeight: 600 }}
        >
          {qrDetail.qrcodeId || 'QR--'} {renderStatus(eventStatus, eventStatusLabel)}
        </Typography>
        <IconButton edge="end" onClick={onClose} aria-label="close">
          <CloseIcon />
        </IconButton>
      </Box>

      <DialogContent dividers sx={{ px: 3, pb: 3, border: 0 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <QrExtraContents qrDetail={qrDetail} />
          <Divider sx={{ borderColor: '#F2F4F7' }} />
          <Box>
            <QrCodeReview.Container data={qrExportData} capturedEventName="qr_management_qr_code_review">
              <QrCodeReview.Label />
              <Stack
                direction="row"
                spacing={1}
                sx={{
                  mt: 3,
                  width: '100%',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <Button
                  onClick={dialogState.onDelete}
                  variant="outlined"
                  color="error"
                  size="small"
                  disabled={qrDetail.status === EventStatusEnum.ASSIGNED}
                  sx={{
                    width: '120px',
                    height: '42px',
                    background: colors.error50,
                  }}
                >
                  <DeleteOutlineOutlined fontSize="small" />
                </Button>
                <QrCodeReview.Download
                  startIcon={<Image src={qrLabelSvg} alt="" width={22} height={22} />}
                  size="small"
                  sx={{
                    flex: 1,
                    background: colors.primary50,
                    height: '42px',
                  }}
                />
                <QrCodeReview.DownloadQrOnly
                  startIcon={<Image src={qrOnlySvg} alt="" width={22} height={22} />}
                  size="small"
                  sx={{
                    flex: 1,
                    background: colors.primary50,
                    height: '42px',
                  }}
                />
              </Stack>
            </QrCodeReview.Container>
          </Box>
        </Box>
      </DialogContent>
    </>
  );
};
