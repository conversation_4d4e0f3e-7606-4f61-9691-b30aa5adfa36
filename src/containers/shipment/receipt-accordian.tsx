import CloseFullscreenIcon from '@mui/icons-material/CloseFullscreen';
import OpenInFullIcon from '@mui/icons-material/OpenInFull';
import { Box, Fade, Stack, useTheme } from '@mui/material';
import Accordion from '@mui/material/Accordion';
import AccordionDetails from '@mui/material/AccordionDetails';
import AccordionSummary from '@mui/material/AccordionSummary';
import Typography from '@mui/material/Typography';
import { DD_MMMM_YYYY_WITH_DASH } from 'constant/common';
import { formatDateWithLocale } from 'containers/event/_components';
import { useTranslations } from 'next-intl';
import * as React from 'react';
import { Receipt } from 'types';
import { formatNumberWithCommas } from 'utils';
import { parseFormattedNumber } from 'utils/convert';
import { getTranslateLabelByLocale } from 'utils/cookie-client';
import { formatOrchardNo } from 'utils/format';

type ShipmentAccordionProps = {
  data?: Receipt;
};

const DEFAULT_VALUE = '--';

export const ShipmentAccordion: React.FC<ShipmentAccordionProps> = ({ data }) => {
  const theme = useTheme();
  const ocrT = useTranslations('ocr');

  const ocrInformationT = useTranslations('ocr-information');
  const commonT = useTranslations('common');
  const [expanded, setExpanded] = React.useState<string | false>('ocr-panel');
  const handleChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpanded(isExpanded ? panel : false);
  };

  const totalWeight = formatNumberWithCommas(parseFormattedNumber(data?.totalWeightKg?.toString() ?? '0'));

  const isExpand = expanded === 'ocr-panel';

  const defaultValue = '--';

  const arrayItems = [
    {
      label: ocrT('destination-country'),
      value: data?.destinationCountry || defaultValue,
    },
    {
      label: ocrT('transport-mode'),
      value: data?.transportationMode || defaultValue,
    },
    {
      label: ocrT('border-checkpoint-name'),
      value: data?.borderCheckpointName || defaultValue,
    },
    {
      label: ocrT('total-weight'),
      value: `${totalWeight} ${commonT('kg')}`,
    },
    {
      label: ocrT('number-of-boxes'),
      value: formatNumberWithCommas(data?.numberOfBoxes) || defaultValue,
    },
    {
      label: ocrT('export-date'),
      value: data?.exportDate ? formatDateWithLocale(data.exportDate, DD_MMMM_YYYY_WITH_DASH) : defaultValue,
    },
    {
      label: ocrT('exporting-company'),
      value: data?.nameOfExportingCompany || defaultValue,
    },
    {
      label: ocrT('container-number'),
      value: data?.containerNumber || defaultValue,
    },
    {
      label: ocrT('truck-number'),
      value: data?.truckRegistrationNumber || defaultValue,
    },
    {
      label: ocrT('province-truck-registration-title'),
      value: getTranslateLabelByLocale(data?.truckProvinceRegistrationNumberObj?.label) || defaultValue,
    },
    {
      label: ocrT('trailer-number'),
      value: data?.trailerRegistrationNumber || defaultValue,
    },
    {
      label: ocrT('province-trailer-registration-title'),
      value: getTranslateLabelByLocale(data?.trailerProvinceRegistrationNumberObj?.label) || defaultValue,
    },
    {
      label: ocrT('orchard-no'),
      value: data?.orchardRegisterNumber
        ? `${ocrInformationT('prefix-orchard-no')} ${formatOrchardNo(data?.orchardRegisterNumber ?? '')}`
        : defaultValue,
    },
  ];

  return (
    <Accordion
      disableGutters
      expanded={expanded === 'ocr-panel'}
      onChange={handleChange('ocr-panel')}
      square={false}
      sx={{
        '& .MuiAccordionSummary-expandIconWrapper': {
          transition: 'none',
          transform: 'none',
        },
        '& .Mui-expanded .MuiAccordionSummary-expandIconWrapper': {
          transform: 'none',
        },
        boxShadow: 'none',
      }}
    >
      <AccordionSummary
        expandIcon={
          <Box position="relative" width={24} height={24}>
            <Fade in={expanded !== 'ocr-panel'} timeout={200}>
              <OpenInFullIcon fontSize="small" sx={{ position: 'absolute', top: 0, left: 0 }} />
            </Fade>

            <Fade in={expanded === 'ocr-panel'} timeout={200}>
              <CloseFullscreenIcon fontSize="small" sx={{ position: 'absolute', top: 0, left: 0 }} />
            </Fade>
          </Box>
        }
        sx={{
          boxShadow: 'none',
          backgroundColor: theme.palette.customColors.primaryBrand100,
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          borderBottomLeftRadius: isExpand ? 0 : '8px',
          borderBottomRightRadius: isExpand ? 0 : '8px',
        }}
      >
        <Typography variant="body1" fontWeight={600}>
          {data?.receiptNumber}
        </Typography>
      </AccordionSummary>
      <AccordionDetails
        sx={{
          padding: '0px',
          backgroundColor: theme.palette.customColors.primary50,
          borderBottomLeftRadius: '8px',
          borderBottomRightRadius: '8px',
          p: '16px',
          display: 'flex',
          flexDirection: 'column',
          gap: '12px',
        }}
      >
        {arrayItems.map((item) => (
          <Stack
            key={item.label}
            direction="row"
            justifyContent="space-between"
            sx={{
              paddingBottom: '8px',
              borderBottom: `1px solid ${theme.palette.customColors.neutralBorder}`,
            }}
            gap={1}
          >
            <Typography variant="caption" color="text.secondary">
              {item.label}
            </Typography>
            <Typography
              variant="caption"
              fontWeight={600}
              sx={{
                textWrap: 'wrap',
                wordBreak: 'break-word',
                maxWidth: '100%',
                textAlign: 'right',
              }}
            >
              {item.value ?? DEFAULT_VALUE}
            </Typography>
          </Stack>
        ))}
      </AccordionDetails>
    </Accordion>
  );
};
