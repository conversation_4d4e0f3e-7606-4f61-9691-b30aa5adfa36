'use client';
import { Box, Grid, Typography } from '@mui/material';
import { DetailRow } from 'components';
import { DD_MMMM_YYYY_WITH_DASH } from 'constant/common';
import { formatDateWithLocale } from 'containers/event/_components';
import { useTranslations } from 'next-intl';
import { FC } from 'react';
import { theme } from 'styles/theme';
import { PackingHouseInfo } from 'types';

type InfoProps = {
  packingHouseInfo: PackingHouseInfo;
};

export const PackingHouseInformation: FC<InfoProps> = ({ packingHouseInfo: data }) => {
  const packingHouseInfoT = useTranslations('profile');

  return (
    <Box sx={{ padding: '20px', backgroundColor: theme.palette.customColors.lightGray, flex: 1 }}>
      <Typography variant="h5" component="h1" fontWeight="bold" sx={{ mb: { xs: 2, sm: 0 } }}>
        {packingHouseInfoT('packing-house-information')}
      </Typography>
      <Grid container spacing={2} sx={{ width: '100%' }}>
        {/* Detail */}
        <Grid
          size={{
            xs: 12,
            md: 12,
            lg: 12,
            xl: 6,
          }}
        >
          <Box sx={{ display: 'flex', gap: 2, marginTop: 2 }}>
            <Box
              sx={{
                flex: 1,
                borderRadius: '8px',
                border: '1px solid #0000001A',
                background: 'white',
                height: 'auto',
              }}
              component={'div'}
            >
              <Typography sx={{ padding: 2 }} variant="body1" fontWeight={600} fontSize="20px">
                {packingHouseInfoT('general')}
              </Typography>
              <Box
                sx={{
                  flex: 1,
                  padding: 2,
                  display: 'flex',
                  gap: 1,
                  height: 'auto',
                  overflowY: 'auto',
                }}
              >
                <Box sx={{ flex: 1 }}>
                  <DetailRow title={packingHouseInfoT('packing-house-name')} content={data?.name} noBorder />
                  <DetailRow title={packingHouseInfoT('packing-house-address')} content={data?.address} noBorder />
                  <DetailRow
                    title={packingHouseInfoT('owner-name')}
                    content={data?.ownerName?.trim() ?? '--'}
                    noBorder
                  />
                  <DetailRow
                    title={packingHouseInfoT('owner-nick-name')}
                    content={data?.ownerNickname?.trim() ?? '--'}
                    noBorder
                  />
                  <DetailRow
                    title={packingHouseInfoT('owner-phone-number')}
                    content={data?.ownerPhone ? `0${data.ownerPhone}` : '--'}
                    noBorder
                  />
                </Box>
              </Box>
            </Box>
          </Box>
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 12,
            lg: 12,
            xl: 6,
          }}
        >
          <Box sx={{ display: 'flex', gap: 2, marginTop: 2 }}>
            <Box
              sx={{
                flex: 1,
                borderRadius: '8px',
                border: '1px solid #0000001A',
                background: 'white',
                height: 'auto',
              }}
              component={'div'}
            >
              <Typography sx={{ padding: 2 }} variant="body1" fontWeight={600} fontSize="20px">
                {packingHouseInfoT('gmp-cert')}
              </Typography>
              <Box
                sx={{
                  flex: 1,
                  padding: 2,
                  display: 'flex',
                  gap: 1,
                  height: 'auto',
                  overflowY: 'auto',
                }}
              >
                <Box sx={{ flex: 1 }}>
                  <DetailRow title={packingHouseInfoT('gmp-number')} content={data?.gmpNumber} noBorder />
                  <Box sx={{ display: 'flex' }}>
                    <DetailRow
                      title={packingHouseInfoT('gmp-valid-from')}
                      content={
                        data?.gmpValidFrom > 0 &&
                        formatDateWithLocale(data?.gmpValidFrom * 1000 || 0, DD_MMMM_YYYY_WITH_DASH)
                      }
                      noBorder
                    />
                    <DetailRow
                      title={packingHouseInfoT('gmp-valid-to')}
                      content={
                        data?.gmpValidTo > 0 &&
                        formatDateWithLocale(data?.gmpValidTo * 1000 || 0, DD_MMMM_YYYY_WITH_DASH)
                      }
                      noBorder
                    />
                  </Box>
                </Box>
              </Box>
            </Box>
          </Box>
          <Box sx={{ display: 'flex', gap: 2, marginTop: 2 }}>
            <Box
              sx={{
                flex: 1,
                borderRadius: '8px',
                border: '1px solid #0000001A',
                background: 'white',
                height: 'auto',
              }}
              component={'div'}
            >
              <Typography sx={{ padding: 2 }} variant="body1" fontWeight={600} fontSize="20px">
                {packingHouseInfoT('doa-cert')}
              </Typography>
              <Box
                sx={{
                  flex: 1,
                  padding: 2,
                  display: 'flex',
                  gap: 1,
                  height: 'auto',
                  overflowY: 'auto',
                }}
              >
                <Box sx={{ flex: 1 }}>
                  <DetailRow title={packingHouseInfoT('doa-number')} content={data?.doaNumber} noBorder />
                  <Box sx={{ display: 'flex' }}>
                    <DetailRow
                      title={packingHouseInfoT('doa-valid-from')}
                      content={
                        data?.doaValidFrom > 0 &&
                        formatDateWithLocale(data?.doaValidFrom * 1000 || 0, DD_MMMM_YYYY_WITH_DASH)
                      }
                      noBorder
                    />
                    <DetailRow
                      title={packingHouseInfoT('doa-valid-to')}
                      content={
                        data?.doaValidTo > 0 &&
                        formatDateWithLocale(data?.doaValidTo * 1000 || 0, DD_MMMM_YYYY_WITH_DASH)
                      }
                      noBorder
                    />
                  </Box>
                </Box>
              </Box>
            </Box>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};
