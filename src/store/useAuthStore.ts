import { trim } from 'lodash-es';
import {
  sendOtpService,
  loginOtpService,
  getNumberAcceptPDPAStatus,
  updateNumberAcceptPDPAStatus,
} from 'services/auth.service';
import { clearAllBrowserStorage, formatThaiPhone } from 'utils';
import { authRoutes } from 'routes/client-routes';

import { create, resetAllStores } from './index';
import { clearAuthService, setAuthService } from 'services/internal.service';
import { basePathProd } from 'configs/app-config';
import toastMessages from 'utils/toastMessages';
import { logger } from 'utils/logger';
import { capturePosthog } from 'utils/posthog';
import { sendEvent } from 'utils/gtag';
import { get as _get } from 'lodash-es';
interface AuthTokenResponse {
  data: {
    accessToken: string;
  };
}

interface OtpResponse {
  otp: string;
}

export enum LoginStepEnum {
  RequestOtp = 'RequestOtp',
  VerifyPapd = 'VerifyPapd',
  VerifyPhone = 'VerifyPhone',
}

interface AuthState {
  loginStep: LoginStepEnum;

  phoneNumber: string;
  remainingTime: number;
  otp: string;
  errorOtp: boolean;

  showCountDown: boolean;

  sampleOtp: string;

  papdAccept: boolean;

  setLoginStep: (step: LoginStepEnum, phoneNumber?: string) => void;
  setShowCountDown: (value: boolean) => void;

  setOtp: (otp: string) => void;
  setErrorOtp: (value: boolean) => void;

  requestOtp: (phone: string) => Promise<void>;
  loginByOtp: (otp: string) => Promise<void>;
  setRemainingTime: (time: number) => void;
  checkStatus: (phone: string, message: string) => Promise<boolean>;
  updateStatus: (phone: string, status: boolean) => Promise<void>;

  resetState: () => void;

  logout: () => Promise<void>;
  _hasHydrated?: boolean;
}

const defaultState = {
  loginStep: LoginStepEnum.VerifyPhone,
  phoneNumber: '',
  expiresIn: 0,
  remainingTime: 0,
  showCountDown: false,
  errorOtp: false,
  papdAccept: true,
  sampleOtp: '',
  otp: '',
  _hasHydrated: false,
};

export const useAuthStore = create<AuthState>((set, get) => {
  const getFormattedPhone = () => {
    const { phoneNumber } = get();
    return formatThaiPhone(trim(phoneNumber));
  };

  return {
    ...defaultState,
    setOtp: (otp) => {
      set({ otp });
    },

    setShowCountDown: (value) => set({ showCountDown: value }),
    setRemainingTime: (time) => set({ remainingTime: time }),

    requestOtp: async (phone: string) => {
      try {
        const phoneFormatted = formatThaiPhone(trim(phone));
        const response: OtpResponse = await sendOtpService({
          phoneNumber: phoneFormatted,
        });

        const defaultExpireIn = 60;

        set({
          sampleOtp: response.otp,
          showCountDown: true,
          remainingTime: defaultExpireIn,
          phoneNumber: phone,
        });
      } catch (error) {
        logger.error('Error requesting OTP:', { meta: error });
        throw error;
      }
    },
    loginByOtp: async (otp: string): Promise<void> => {
      try {
        const response: AuthTokenResponse = await loginOtpService({
          phoneNumber: getFormattedPhone(),
          otp,
        });

        await setAuthService(response.data.accessToken);

        capturePosthog('login_success');

        sendEvent('login_success');
      } catch (error) {
        logger.error('Error login by OTP:', { meta: error });
        capturePosthog('login_error');
        sendEvent('login_error');
        throw error;
      }
    },

    resetState: () => {
      set({ ...defaultState });
    },

    checkStatus: async (phone: string, message: string) => {
      try {
        const phoneFormatted = formatThaiPhone(trim(phone));
        const response = await getNumberAcceptPDPAStatus({ phoneNumber: phoneFormatted });
        set({ papdAccept: response.accepted });
        return response.accepted;
      } catch (error) {
        if (_get(error, 'status', 0) >= 500) {
          toastMessages.error('Server error, please try again later.');
          throw error;
        }

        logger.error('Error checking PDPA status:', { error });
        toastMessages.error(message);
        throw error;
      }
    },

    updateStatus: async (phone: string, status: boolean) => {
      try {
        const phoneFormatted = formatThaiPhone(trim(phone));
        await updateNumberAcceptPDPAStatus({ phoneNumber: phoneFormatted, accepted: status });
        set({ papdAccept: true });
      } catch (error) {
        logger.error('Error updating PDPA status:', { error });
        throw error;
      }
    },

    setLoginStep: (step: LoginStepEnum, phoneNumber?: string) => {
      if (step === LoginStepEnum.VerifyPapd) {
        set({ loginStep: step, phoneNumber });
      } else if (step === LoginStepEnum.RequestOtp) {
        if (!phoneNumber) {
          set({ loginStep: step });
          return;
        }
        set({ loginStep: step, phoneNumber });
      }
    },

    logout: async () => {
      resetAllStores();
      await clearAllBrowserStorage();
      await clearAuthService();
      if (location) {
        location.href = `${basePathProd}${authRoutes.login}`;
      }
    },

    setErrorOtp: (hasError) => {
      set({
        errorOtp: hasError,
      });
    },
  };
});
