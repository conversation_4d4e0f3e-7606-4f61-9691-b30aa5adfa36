import { create } from './index';

type GlobalState = {
  isOpen: boolean;
  openMenus: Record<string, boolean>;
  loadingPage: boolean;
  _hasHydrated: boolean;
  isTablet: boolean;
  locale?: string;
  hasFailedUserInfo: boolean;
  currentPathname?: string;
  previousPathname?: string;
};

type GlobalAction = {
  toggle: () => void;
  open: () => void;
  close: () => void;
  setOpenMenus: (key: string) => void;
  setLoadingPage: (value: boolean) => void;
  setIsTablet: (value: boolean) => void;
  setLocale: (locale: string) => void;
  setFailedUserInfo: (value: boolean) => void;
  setCurrentPathname: (pathname: string) => void;
};

const defaultState: GlobalState = {
  isOpen: false,
  openMenus: {
    'durian-procurement': true,
    settings: true,
  },
  loadingPage: false,
  hasFailedUserInfo: false,
  _hasHydrated: false,
  isTablet: true,
};

export const useGlobalStore = create<GlobalState & GlobalAction>(
  (set, get) => ({
    ...defaultState,
    toggle: () => set((state) => ({ isOpen: !state.isOpen })),
    open: () => set({ isOpen: true }),
    close: () => set({ isOpen: false }),

    setFailedUserInfo: (value: boolean) => {
      set({ hasFailedUserInfo: value });
    },

    setOpenMenus: (key: string) => {
      const { openMenus: previousOpenMenus } = get();
      set({
        openMenus: { ...previousOpenMenus, [key]: !previousOpenMenus[key] },
      });
    },

    setLoadingPage: (value) => {
      set({ loadingPage: value });
    },

    setIsTablet: (value) => {
      set({ isTablet: value });
    },
    setLocale: (locale) => {
      set({ locale });
    },
    setCurrentPathname: (pathname) => {
      const { currentPathname = '' } = get();
      set({ previousPathname: currentPathname, currentPathname: pathname });
    },
  }),
  {
    persistKey: 'global-store',
    useSession: true,
    partialize: () => {
      return {};
    },
    merge: (persistedState, currentState) => {
      return {
        ...currentState,
        ...persistedState,
      };
    },
  }
);
