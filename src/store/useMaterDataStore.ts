'use client';

import { create } from 'store';
import { BrandName, DurianVariety, TranslateLabel, ProductType, Province } from 'types';
import { flatMap } from 'lodash-es';
import { getCookieLocale } from 'utils/cookie-client';

export const otherVarietyValue = 'other';

const defaultState: MasterDataState = {
  brandNames: [],
  productTypes: [],
  varieties: [],
  grades: [],
  provinces: [],
};

type MasterDataState = {
  brandNames: BrandName[];
  productTypes: ProductType[];
  varieties: DurianVariety[];
  grades: DurianVariety['grades'];
  provinces: Province[];
  _hasHydrated?: boolean;
};

type MasterDataActionState = {
  updateMasterData: (brandNames: BrandName[], productTypes: ProductType[], provinces: Province[]) => void;
  getBrandLabel: (value: string) => string;
  getProductTypeLabel: (value: string) => string;
  setVarieties: (varieties: DurianVariety[]) => void;
  getVarietyLabel: (id: string) => string;
  getGradeLabel: (id: string) => string;
  reset: () => void;
  getTranslatedLabel: <T extends { id: string; label: TranslateLabel }>(collection: T[], id: string) => string;
};

export const useMasterDataStore = create<MasterDataState & MasterDataActionState>(
  (set, get) => {
    return {
      ...defaultState,
      getTranslatedLabel: <T extends { id: string; label: TranslateLabel }>(collection: T[], id: string): string => {
        const cookieLocale = getCookieLocale() as keyof TranslateLabel;
        return collection.find((item) => item.id === id)?.label[cookieLocale] ?? '';
      },
      updateMasterData: (brandNames, productTypes, provinces = []) => {
        set({ brandNames, productTypes, provinces });
      },

      getBrandLabel: (value): string => {
        const { brandNames, getTranslatedLabel } = get();
        return getTranslatedLabel(brandNames, value);
      },

      getProductTypeLabel: (value): string => {
        if (!value) return '';

        const { productTypes, getTranslatedLabel } = get();

        return getTranslatedLabel(productTypes, value);
      },

      setVarieties: (varieties: DurianVariety[]) => {
        const grades = flatMap(varieties, (it) => it.grades);
        set({ varieties, grades });
      },

      getVarietyLabel: (id: string) => {
        const { varieties, getTranslatedLabel } = get();
        return getTranslatedLabel(varieties, id);
      },

      getGradeLabel: (id: string) => {
        const { grades, getTranslatedLabel } = get();
        return getTranslatedLabel(grades, id);
      },

      reset: () => {
        set({ ...defaultState });
      },
    };
  }
);
