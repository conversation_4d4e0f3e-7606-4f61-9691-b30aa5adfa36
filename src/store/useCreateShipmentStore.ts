import { cloneDeep, flatMap, uniqBy } from 'lodash-es';
import { getQrCodeBatchLotService, uploadOcaDocumentPtpService } from 'services/common.service';
import { uploadFileService } from 'services/internal.service';
import { create } from 'store';
import {
  DurianInformationForm,
  DurianVariety,
  FormShipmentStepEnum,
  PackingHouse,
  PackingHouseDetail,
  QrDetail,
  Receipt,
  ShipmentIdentityType,
  UploadFile,
} from 'types';
import { delayPromise, getImageUrl } from 'utils';
import { mapperOcrResponseToToOcrRecord } from 'utils/mapper';
import _get from 'lodash-es/get';
import { capturePosthog } from 'utils/posthog';
import { sendEvent } from 'utils/gtag';
import { checkMatchedQrLabel, mapperInitEditForm } from 'app/(main)/shipment/_util/util';

export interface ShipmentPhotosLoading {
  [key: string]: boolean;
}

export const defaultState: ShipmentState = {
  formStep: FormShipmentStepEnum.SelectedProductIds,
  informationFormValues: [],
  openModalInformation: false,
  additionalFiles: [] as UploadFile[],
  ocrFile: undefined,
  receivingIds: [],
  isUploadOcrError: false,
  shipmentIdentity: {
    name: '',
  },
  manualInputOrc: false,
  _hasHydrated: false,
  receivingDetails: [],
  shipmentPhotos: [],
  sessionFormData: {
    shipmentName: '',
    orcFile: undefined,
  },
  varietiesList: [],
  isEditForm: false,
  showWarningDialog: false,
};

type ShipmentState = {
  formStep: FormShipmentStepEnum;
  informationFormValues: DurianInformationForm[];
  openModalInformation: boolean;
  additionalFiles: UploadFile[];
  ocrFile?: Receipt;
  receivingIds: string[];
  isUploadOcrError: boolean;
  shipmentIdentity: ShipmentIdentityType;
  manualInputOrc: boolean;
  _hasHydrated: boolean;
  receivingDetails: PackingHouse[];
  shipmentPhotos: UploadFile[];
  sessionFormData: {
    shipmentName?: string;
    orcFile?: Receipt;
  };
  varietiesList: DurianVariety[];
  isEditForm: boolean;
  showWarningDialog: boolean;
  selectedPackingId?: string;
};

type ShipmentActionState = {
  updateStep: (formStep: FormShipmentStepEnum) => void;
  updateOpenPackagingModal: (value: boolean, selectedPackingId?: string) => void;

  // Form
  updateInformationForm: (informationFormValues: DurianInformationForm, id?: string) => void;
  deleteInformationForm: (id: string) => void;
  updateShipmentIdentity: (name: string) => void;

  updateUploadFile: (fileUploaded: UploadFile) => Promise<void>;
  deleteUploadFile: (id: string) => void;

  updateOcrFile: (file: File) => Promise<void>;
  updateOrcForm: (formValues?: Receipt) => void;
  deleteOcrFile: () => void;

  setReceivingIds: (ids: string[]) => void;
  setIsUploadOcrError: (value: boolean) => void;

  generateQrCodeBatchLot: () => Promise<void>;
  resetStore: () => void;
  initEditForm: (values: PackingHouseDetail, success?: () => void) => Promise<void>;

  setManualInput: (value: boolean) => void;
  uploadFile: (file: File) => Promise<string>;
  setReceivingDetails: (receivingDetail: PackingHouse[]) => void;

  uploadShipmentPhoto: (file: UploadFile) => Promise<void>;
  deleteShipmentPhoto: (id: string) => void;

  setVarietiesList: (varieties: DurianVariety[]) => void;

  setShowWarningDialog: (value: boolean) => void;

  generateSingleQrCodeBatchLot: (id?: string) => Promise<void>;

  removeQrCode: (id: string) => void;

  selectAvailableQr: (packingId: string, qrUrl: string, data: QrDetail) => void;
};

export const useCreateShipmentStore = create<ShipmentState & ShipmentActionState>(
  (set, get) => ({
    ...defaultState,
    updateStep: (formStep) => {
      set({
        formStep,
      });
    },
    updateInformationForm: (infoForm, id?: string) => {
      const { informationFormValues } = get();

      if (id) {
        set({
          informationFormValues: informationFormValues.map((it) => {
            if (it.id === id) {
              const newPackingInfo = structuredClone({ ...it, ...infoForm });

              if (!it.qrCodeId && newPackingInfo.qrDetail?.productType) {
                newPackingInfo.qrDetail.productType = infoForm.boxType;
              }

              return newPackingInfo;
            }
            return it;
          }),
          selectedPackingId: undefined,
        });
        return;
      }

      set({
        informationFormValues: informationFormValues.concat(infoForm),
      });
    },

    deleteInformationForm: (id) => {
      const { informationFormValues } = get();
      set({
        informationFormValues: informationFormValues.filter((it) => it.id !== id),
      });
    },

    updateOpenPackagingModal: (value, selectedPackingId) => {
      set({
        selectedPackingId,
        openModalInformation: value,
      });
    },
    updateUploadFile: async (fileUploaded) => {
      const { uploadFile } = get();

      if (!fileUploaded.file) {
        return;
      }

      set((state) => ({
        additionalFiles: [
          ...state.additionalFiles,
          {
            ...fileUploaded,
            isUploading: true,
          },
        ],
      }));

      try {
        const fileUploadedResponse = await uploadFile(fileUploaded.file);

        set((state) => ({
          additionalFiles: state.additionalFiles.map((it) => {
            if (it.id === fileUploaded.id) {
              return {
                ...it,
                isUploading: false,
                url: fileUploadedResponse,
              };
            }
            return it;
          }),
        }));
      } catch (error) {
        set((state) => ({
          additionalFiles: state.additionalFiles.filter((it) => it.id !== fileUploaded.id),
        }));
        throw error;
      }
    },
    deleteUploadFile: (id) => {
      const { additionalFiles: uploadFiles } = get();
      set({
        additionalFiles: uploadFiles.filter((it) => it.id !== id),
      });
    },

    uploadFile: async (file) => {
      const formData = new FormData();
      formData.append('file', file);
      const response = await uploadFileService(formData);
      sendEvent('uploaded_photos');
      const filenameData = response.data.filenameDisk;

      return filenameData;
    },

    updateOcrFile: async (file) => {
      const formData = new FormData();
      formData.append('file', file);

      const response = await uploadFileService(formData);
      const filenameData = response.data.filenameDisk;

      if (!filenameData) throw new Error('uploadFile');

      const imageUrl = getImageUrl(filenameData);

      if (!imageUrl) throw new Error('image url not found');

      const { data: ocrUploadResponse } = await uploadOcaDocumentPtpService(imageUrl);

      const formattedData: Receipt = mapperOcrResponseToToOcrRecord(
        ocrUploadResponse.suggest,
        ocrUploadResponse.source,
        filenameData
      );

      capturePosthog('get_ephyto_success');

      set({
        ocrFile: { ...formattedData },
      });
    },

    deleteOcrFile: () => {
      set({ ocrFile: undefined });
    },

    updateOrcForm: (values) => {
      set({ ocrFile: values });
    },

    setReceivingIds: (ids) => {
      set({
        receivingIds: [...ids],
      });
    },

    setIsUploadOcrError: (isUploadOcrError) => {
      set({ isUploadOcrError });
    },

    updateShipmentIdentity: (name) => {
      set({
        shipmentIdentity: {
          name,
        },
      });
    },
    generateQrCodeBatchLot: async () => {
      const { informationFormValues, ocrFile } = get();

      const newPackingAmount = informationFormValues.filter((it) => !it.qrId).length;

      if (newPackingAmount === 0) {
        return Promise.resolve();
      }

      const qrcodeBatchlotsResponse = await getQrCodeBatchLotService(newPackingAmount, ocrFile?.nameOfExportingCompany);

      const qrCodeBatchlots = qrcodeBatchlotsResponse.data.map((it) => ({
        qrId: it.qrId,
        batchlot: it.batchlot,
        qrUrl: it.qrUrl,
        id: it.id,
      }));

      let indexOfQr = 0;

      const informationFormValuesUpdated = informationFormValues.map((it) => {
        if (it.qrId && it.qrUrl) {
          return it;
        }

        const qrCode = qrCodeBatchlots[indexOfQr];
        indexOfQr += 1;

        return {
          ...it,
          batchlot: qrCode ? qrCode.batchlot : '',
          qrUrl: qrCode ? qrCode.qrUrl : '',
          qrId: qrCode ? qrCode.qrId : '',
        };
      });

      set({
        informationFormValues: informationFormValuesUpdated,
      });
    },

    resetStore: () => {
      set(cloneDeep(defaultState));
    },

    initEditForm: async (values: PackingHouseDetail, success) => {
      const {
        formStep,
        receivingIds,
        shipmentIdentity,
        informationFormValues,
        ocrFile,
        additionalFiles,
        shipmentPhotos,
      } = mapperInitEditForm(values);

      set({
        formStep,
        receivingIds,
        shipmentIdentity,
        informationFormValues,
        ocrFile,
        additionalFiles,
        shipmentPhotos,
        isEditForm: true,
      });

      await delayPromise(100);
      success?.();
    },
    setManualInput: (manualInputOrc) => {
      set({ manualInputOrc });
    },

    setReceivingDetails: (receivingDetails) => {
      if (receivingDetails && receivingDetails.length > 0) {
        const { setVarietiesList } = get();

        const varietiesReceived =
          flatMap(receivingDetails, (it) => it.varieties).map((it, idx) => {
            return {
              ...it,
              // id: `${it.id}-${it.name ?? ''}-${idx}`,
              originalId: it.id,
              label: it.name
                ? {
                    th: it.name ?? '',
                    en: it.name ?? '',
                  }
                : it.label,
              index: idx,
              isOther: !!it.name,
              name: it.name ?? '',
              idx: idx,
            };
          }) ?? [];

        const varietiesList: DurianVariety[] = [];
        varietiesReceived.forEach((variety) => {
          const existingVariety = varietiesList.find((v) =>
            v.isOther ? v.id === variety.id && v.name === variety.name : v.id === variety.id
          );
          if (existingVariety) {
            if (_get(variety, 'isOther', false)) {
              existingVariety.grades.push(...variety.grades);
            } else {
              const existingGradeIds = existingVariety.grades.map((g) => g.id);

              variety.grades.forEach((grade) => {
                if (!existingGradeIds.includes(grade.id)) {
                  existingVariety.grades.push(grade);
                }
              });
            }
          } else {
            const grades = uniqBy(variety.grades, (grade) => grade.id);
            const newVariety = { ...variety, grades };
            varietiesList.push(newVariety);
          }
        });

        setVarietiesList(varietiesList);
      }

      set({ receivingDetails: [...receivingDetails] });
    },
    uploadShipmentPhoto: async (fileUpload) => {
      const { uploadFile } = get();

      if (!fileUpload.file) {
        return;
      }

      set((state) => ({
        shipmentPhotos: [
          ...state.shipmentPhotos,
          {
            ...fileUpload,
            isUploading: true,
          },
        ],
      }));

      try {
        const fileUploadedResponse = await uploadFile(fileUpload.file);

        set((state) => ({
          shipmentPhotos: state.shipmentPhotos.map((it) => {
            if (it.id === fileUpload.id) {
              return {
                ...it,
                isUploading: false,
                url: fileUploadedResponse,
              };
            }
            return it;
          }),
        }));
      } catch (error) {
        set((state) => ({
          shipmentPhotos: state.shipmentPhotos.filter((it) => it.id !== fileUpload.id),
        }));
        throw error;
      }
    },

    deleteShipmentPhoto: (id) => {
      const { shipmentPhotos } = get();

      set({ shipmentPhotos: shipmentPhotos.filter((photo) => photo.id !== id) });
    },

    setVarietiesList: (varieties) => {
      set({ varietiesList: [...varieties] });
    },
    setShowWarningDialog: (value) => {
      set({ showWarningDialog: value });
    },

    generateSingleQrCodeBatchLot: async (id) => {
      const { informationFormValues, ocrFile } = get();

      const nameOfExportingCompany = ocrFile?.nameOfExportingCompany ?? '';

      if (!nameOfExportingCompany) {
        throw new Error('Name of exporting company is required to generate QR code batch lot');
      }

      const qrcodeBatchlotsResponse = await getQrCodeBatchLotService(1, ocrFile?.nameOfExportingCompany);

      const newInformationFormValues = informationFormValues.map((it) => {
        if (it.id === id) {
          return {
            ...it,
            batchlot: qrcodeBatchlotsResponse.data[0].batchlot,
            qrUrl: qrcodeBatchlotsResponse.data[0].qrUrl,
            qrId: qrcodeBatchlotsResponse.data[0].qrId,
            qrCodeId: undefined,
            qrDetail: undefined,
            notMatchedQrLabel: false,
            notMatchedQrLabels: [],
          };
        }
        return it;
      });

      set({
        informationFormValues: newInformationFormValues,
      });
    },

    removeQrCode: (id) => {
      const { informationFormValues } = get();

      const updatedInformationFormValues = informationFormValues.map((it) => {
        if (it.id === id) {
          return {
            ...it,
            qrUrl: '',
            qrId: '',
            qrDetail: undefined,
            qrCodeId: undefined,
          };
        }
        return it;
      });

      set({
        informationFormValues: updatedInformationFormValues,
      });
    },

    selectAvailableQr: (packingId, qrUrl, data) => {
      const { informationFormValues, ocrFile } = get();

      const updatedInformationFormValues = informationFormValues.map((it) => {
        if (it.id === packingId) {

          const newPacking = {
            ...it,
            qrId: data.id,
            qrUrl,
            qrDetail: data,
            productType: data.productType,
            qrCodeId: data.qrcodeId,
          };

          const arrayMatched = checkMatchedQrLabel(newPacking, ocrFile, data);

          const notMatchedQrLabel = arrayMatched.some((field) => !field.value);

          return {
            ...newPacking,
            notMatchedQrLabel,
            notMatchedQrLabels: arrayMatched,
          };
        }
        return it;
      });

      set({
        informationFormValues: updatedInformationFormValues,
      });
    },
  }),
  {
    persistKey: 'create-shipment-store',
    useSession: true,
    partialize: (state) =>
      state.isEditForm
        ? {}
        : {
            formStep: state.formStep,
            informationFormValues: state.informationFormValues,
            additionalFiles: state.additionalFiles,
            receivingIds: state.receivingIds,
            ocrFile: state.ocrFile,
            shipmentIdentity: state.shipmentIdentity,
            shipmentPhotos: state.shipmentPhotos,
            sessionFormData: state.sessionFormData,
            manualInputOrc: state.manualInputOrc,
          },
    merge: (persistedState, currentState) => {
      if (persistedState.isEditForm) {
        return {
          ...currentState,
        };
      }

      return {
        ...currentState,
        ...persistedState,
      };
    },
  }
);
