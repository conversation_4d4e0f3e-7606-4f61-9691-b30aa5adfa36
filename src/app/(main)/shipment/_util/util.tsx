import { convertStringToTimeUnix } from 'components/qr-code-review/util';
import { transportationModeValues } from 'constant/shipment';
import dayjs from 'dayjs';
import { UpdateShipment } from 'hooks/mutates/useUpdateShipmentMutate';
import { flatten, isEmpty } from 'lodash-es';
import {
  CreateAvailableQrPayload,
  CreateShipment,
  DurianInformationForm,
  EventStatusEnum,
  FormShipmentStepEnum,
  PackingHouseDetail,
  Receipt,
  UploadFile,
  VarietyOptionValue,
} from 'types';
import { safeJSONParse } from 'utils';
import { getMessageByLocale } from 'utils/getMessage';

export const renderTitle = (formStep: FormShipmentStepEnum) => {
  const messageT = getMessageByLocale();

  switch (formStep) {
    case FormShipmentStepEnum.ShipmentIdentity:
      return messageT('shipment.shipment-identity');
    case FormShipmentStepEnum.DurianInformationStep:
      return messageT('shipment.information-step');
    case FormShipmentStepEnum.ReceiptStep:
      return messageT('shipment.receipt-step');
    case FormShipmentStepEnum.UploadDocumentStep:
      return messageT('shipment.add-document-step');
    case FormShipmentStepEnum.QrReviewStep:
      return messageT('shipment.qr-review');
    default:
      break;
  }
};

export const mapperInitEditForm = (values: PackingHouseDetail) => {
  const isDraft = values?.status === 'draft';

  const { sourceMaterials, name, files, packing, pq7ReceiptDetails } = values;

  const receivingIds = sourceMaterials?.map((it) => it.id) ?? [];

  const formStep = isDraft ? FormShipmentStepEnum.ShipmentIdentity : FormShipmentStepEnum.QrReviewStep;

  const shipmentPhotos: UploadFile[] = [];

  const fileProductsLength = files?.product.length ?? 0;

  for (let i = 0; i < fileProductsLength; i += 1) {
    const shipmentPhoto = files?.product[i];

    if (!shipmentPhoto) continue;

    shipmentPhotos.push({
      id: shipmentPhoto.id,
      name: shipmentPhoto.filenameDownload,
      file: undefined,
      size: shipmentPhoto.filesize,
      type: shipmentPhoto.mimeType,
      url: shipmentPhoto.filenameDisk ?? '',
    });
  }

  const packagingForm: DurianInformationForm[] = [];

  let ocrFile: Receipt = {} as Receipt;
  if (pq7ReceiptDetails?.receiptNumber) {
    ocrFile = {
      numberOfBoxes: pq7ReceiptDetails.numberOfBoxes,
      destinationCountry: pq7ReceiptDetails.destinationCountry,
      exportDate: pq7ReceiptDetails.exportDate,
      totalWeightKg: (pq7ReceiptDetails.totalWeightKg ? pq7ReceiptDetails.totalWeightKg.toString() : '') ?? '',
      receiptNumber: (pq7ReceiptDetails.receiptNumber ? pq7ReceiptDetails.receiptNumber.toString() : '') ?? '',
      transportationMode: transportationModeValues.includes(pq7ReceiptDetails.transportationMode)
        ? pq7ReceiptDetails.transportationMode
        : '',
      id: pq7ReceiptDetails.id,
      truckNumber: pq7ReceiptDetails.truckRegistrationNumber ?? '',
      trailerNumber: pq7ReceiptDetails.trailerRegistrationNumber ?? '',
      orchardNo: pq7ReceiptDetails.orchardRegisterNumber ?? '',
      sourceFrom: pq7ReceiptDetails?.ephytoResponse ? 'ephyto' : 'ocr',
      containerNumber: pq7ReceiptDetails.containerNumber ?? '',
      borderCheckpointName: pq7ReceiptDetails.borderCheckpointName ?? '',
      ephytoResponse: pq7ReceiptDetails.ephytoResponse,
      nameOfExportingCompany: pq7ReceiptDetails.nameOfExportingCompany ?? '',
      truckProvinceRegistrationNumber: pq7ReceiptDetails.truckProvinceRegistrationNumber ?? '',
      trailerProvinceRegistrationNumber: pq7ReceiptDetails.trailerProvinceRegistrationNumber ?? '',
    };
  }

  if (packing && !isEmpty(packing)) {
    packing.forEach((pack) => {
      const variety = pack.varieties[0];
      const varietyName = pack.varietyName;

      const varietyFormatted = {
        ...variety,
        label: varietyName
          ? {
              th: varietyName,
              en: varietyName,
            }
          : variety.label,
        originalId: variety.id,
        name: varietyName ?? '',
      };

      const qrCode = pack.qrCodes?.[0];

      const hasSelectedFromQr = !!qrCode.qrcodeId;

      const packingDate = convertStringToTimeUnix(qrCode?.packingDate) ?? 0;

      const packingData: DurianInformationForm = {
        brand: pack.brandNameInfo.id,
        boxType: pack.productTypeInfo.id,
        variety: variety ? JSON.stringify(varietyFormatted) : '',
        grade: variety?.grades[0]?.id ?? '',
        netWeight: pack.weightKg.toString(),
        totalBoxes: pack.numberOfBoxes.toString(),
        id: pack.id,
        packingDate: pack.packingDate ? dayjs(pack.packingDate * 1000) : null,
        batchlot: pack.batchlot ?? '',
        qrUrl: qrCode?.qrUrl ?? '',
        qrId: qrCode?.id ?? '',
        qrCodeId: qrCode?.qrcodeId,
        qrDetail: hasSelectedFromQr
          ? {
              nameOfExportingCompany: qrCode?.nameOfExportingCompany ?? '',
              orchardRegisterNumber: qrCode?.orchardRegisterNumber ?? '',
              packingDate: packingDate,
              exportTo: qrCode?.exportTo ?? '',
              productType: qrCode?.productType ?? '',
            }
          : undefined,
        notMatchedQrLabel: false,
      };

      if (hasSelectedFromQr) {
        const arrayMatched = checkMatchedQrLabel(packingData, ocrFile, {
          nameOfExportingCompany: qrCode?.nameOfExportingCompany,
          orchardRegisterNumber: qrCode?.orchardRegisterNumber,
          packingDate: packingDate,
          exportTo: qrCode?.exportTo,
          productType: qrCode?.productType,
        });

        const notMatchedQrLabel = arrayMatched.some((field) => !field.value);

        packingData.notMatchedQrLabel = notMatchedQrLabel;
        packingData.notMatchedQrLabels = arrayMatched;
      }

      packagingForm.push(packingData);
    });
  }

  const additionalDocuments = files?.shipment;

  const additionalDocumentsFormatted: UploadFile[] = [];

  if (additionalDocuments?.length) {
    additionalDocuments?.forEach((additionFile) => {
      const imageUrl = additionFile.filenameDisk;

      additionalDocumentsFormatted.push({
        id: additionFile.id,
        name: additionFile.filenameDownload,
        file: undefined,
        size: additionFile.filesize,
        type: additionFile.mimeType,
        url: imageUrl ?? '',
      });
    });
  }

  return {
    formStep,
    receivingIds,
    shipmentIdentity: {
      name,
    },
    informationFormValues: [...packagingForm],
    ocrFile: { ...ocrFile },
    additionalFiles: [...additionalDocumentsFormatted],
    shipmentPhotos,
    isEditForm: true,
  };
};

export const checkMatchedQrLabel = (
  packingProduct?: DurianInformationForm,
  ocrFile?: Receipt,
  qrData?: CreateAvailableQrPayload
): { field: string; value: boolean }[] => {
  const t = getMessageByLocale();

  const results: { field: string; value: boolean }[] = [];

  const matchField = (label: string, value1?: string | null, value2?: string | null) => {
    results.push({ field: label, value: value1 === value2 });
  };

  // Fallback when data is missing
  const defaultFields = [t('qr.exporter'), t('qr.gap-no'), t('qr.packing-date'), t('qr.destination')];

  if (!packingProduct || !ocrFile || !qrData || !packingProduct.qrCodeId) {
    return defaultFields.map((field) => ({ field, value: true }));
  }

  matchField(t('qr.exporter'), qrData.nameOfExportingCompany, ocrFile.nameOfExportingCompany);

  matchField(t('qr.gap-no'), qrData.orchardRegisterNumber, ocrFile.orchardNo);

  const packingDateQr = qrData.packingDate ? dayjs(qrData.packingDate) : null;
  const packingDateForm = packingProduct.packingDate ? dayjs(packingProduct.packingDate) : null;
  results.push({
    field: t('qr.packing-date'),
    value: packingDateQr && packingDateForm ? packingDateQr.isSame(packingDateForm, 'day') : true, // default to true if date is missing
  });

  matchField(t('qr.destination'), qrData.exportTo, ocrFile.destinationCountry);

  return results;
};

export const checkAllMatchedQrLabel = (
  packingProducts: DurianInformationForm[]
): { field: string; value: boolean }[] => {
  const resultMap = new Map<string, boolean>();

  const packingProductNotMatchedLabels = flatten(
    packingProducts.map((product) => product.notMatchedQrLabels).filter((it) => it)
  ).flat();

  packingProductNotMatchedLabels.forEach((label) => {
    if (!label?.field) return;

    if (resultMap.has(label.field)) {
      resultMap.set(label.field, resultMap.get(label?.field) || label.value);
    } else {
      resultMap.set(label.field, label.value);
    }
  });

  return Array.from(resultMap.entries()).map(([field, value]) => ({ field, value }));
};

export const getCurrentLocation = async (): Promise<{ lat: number; lng: number }> => {
  const defaultLocation = { lat: 13.7563, lng: 100.5018 }; // Bangkok, Thailand

  if (!navigator.geolocation) {
    return defaultLocation;
  }

  return new Promise((resolve) => {
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        resolve({ lat: latitude, lng: longitude });
      },
      () => {
        resolve(defaultLocation); // fallback on error
      },
      {
        timeout: 5000,
      }
    );
  });
};

type MapperEditShipmentForm = {
  id: string;
  asDraft?: boolean;
  shipmentName: string;
  receivingIds: string[];
  ocrFormValue?: Receipt;
  informationFormValues?: DurianInformationForm[];
  fileIds?: string[];
  photoIds?: string[];
};

export const mapperEditShipmentForm = async ({
  id,
  asDraft,
  shipmentName,
  receivingIds,
  ocrFormValue,
  informationFormValues,
  fileIds,
  photoIds,
}: MapperEditShipmentForm): Promise<UpdateShipment> => {
  const location = await getCurrentLocation();

  const updateForm: UpdateShipment = {
    formValues: {
      status: asDraft ? EventStatusEnum.DRAFT : EventStatusEnum.PUBLISHED,
      shipmentName,
      receivingProductIds: receivingIds,
      receipt: ocrFormValue?.receiptNumber
        ? {
            numberOfBoxes: Number(ocrFormValue.numberOfBoxes),
            destinationCountry: ocrFormValue.destinationCountry,
            exportDate: dayjs(ocrFormValue.exportDate).unix(),
            totalWeightKg: Number(ocrFormValue.totalWeightKg),
            receiptNumber: ocrFormValue.receiptNumber,
            transportationMode: ocrFormValue.transportationMode ?? '',
            containerNumber: ocrFormValue.containerNumber ?? '',
            truckRegistrationNumber: ocrFormValue.truckNumber ?? '',
            trailerRegistrationNumber: ocrFormValue.trailerNumber ?? '',
            orchardRegisterNumber: ocrFormValue.orchardNo ?? '',
            borderCheckpointName: ocrFormValue.borderCheckpointName ?? '',
            nameOfExportingCompany: ocrFormValue.nameOfExportingCompany ?? '',
            truckProvinceRegistrationNumber: ocrFormValue.truckProvinceRegistrationNumber ?? '',
            trailerProvinceRegistrationNumber: ocrFormValue.trailerProvinceRegistrationNumber ?? '',
          }
        : undefined,
      latitude: location.lat,
      longitude: location.lng,
      packages: informationFormValues?.map((packageInfo) => {
        const varietyParse = safeJSONParse<VarietyOptionValue>(packageInfo.variety);
        const packingDate = packageInfo.packingDate ? dayjs(packageInfo.packingDate).unix() : undefined;

        return {
          brandName: packageInfo.brand,
          productType: packageInfo.boxType,
          varietyGradeJoinId: packageInfo.grade,
          weightKg: Number(packageInfo.netWeight),
          numberOfBoxes: Number(packageInfo.totalBoxes),
          qrCode: packageInfo.qrId ?? '',
          batchlot: packageInfo.batchlot ?? '',
          varietyId: varietyParse?.originalId ?? '',
          varietyName: varietyParse?.name,
          gradeId: packageInfo.grade,
          packingDate,
        };
      }),
      documentIds: fileIds?.length ? [...fileIds] : undefined,
      shipmentPhotoIds: photoIds?.length ? [...photoIds] : undefined,
    },
    shipmentId: id,
  };

  return updateForm;
};

type MapperCreateShipmentForm = {
  asDraft?: boolean;
  shipmentName: string;
  receivingIds: string[];
  ocrFormValue?: Receipt;
  informationFormValues?: DurianInformationForm[];
  fileIds?: string[];
  photoIds?: string[];
};
export const mapperCreateShipmentForm = async ({
  asDraft,
  shipmentName,
  receivingIds,
  ocrFormValue,
  informationFormValues,
  fileIds,
  photoIds,
}: MapperCreateShipmentForm): Promise<CreateShipment> => {
  const location = await getCurrentLocation();

  const updateForm: CreateShipment = {
      status: asDraft ? EventStatusEnum.DRAFT : EventStatusEnum.PUBLISHED,
      shipmentName,
      receivingProductIds: receivingIds,
      receipt: ocrFormValue?.receiptNumber
        ? {
            numberOfBoxes: Number(ocrFormValue.numberOfBoxes),
            destinationCountry: ocrFormValue.destinationCountry,
            exportDate: dayjs(ocrFormValue.exportDate).unix(),
            totalWeightKg: Number(ocrFormValue.totalWeightKg),
            receiptNumber: ocrFormValue.receiptNumber,
            transportationMode: ocrFormValue.transportationMode ?? '',
            containerNumber: ocrFormValue.containerNumber ?? '',
            truckRegistrationNumber: ocrFormValue.truckNumber ?? '',
            trailerRegistrationNumber: ocrFormValue.trailerNumber ?? '',
            orchardRegisterNumber: ocrFormValue.orchardNo ?? '',
            borderCheckpointName: ocrFormValue.borderCheckpointName ?? '',
            nameOfExportingCompany: ocrFormValue.nameOfExportingCompany ?? '',
            truckProvinceRegistrationNumber: ocrFormValue.truckProvinceRegistrationNumber ?? '',
            trailerProvinceRegistrationNumber: ocrFormValue.trailerProvinceRegistrationNumber ?? '',
          }
        : undefined,
      latitude: location.lat,
      longitude: location.lng,
      packages: informationFormValues?.map((packageInfo) => {
        const varietyParse = safeJSONParse<VarietyOptionValue>(packageInfo.variety);
        const packingDate = packageInfo.packingDate ? dayjs(packageInfo.packingDate).unix() : undefined;

        return {
          brandName: packageInfo.brand,
          productType: packageInfo.boxType,
          varietyGradeJoinId: packageInfo.grade,
          weightKg: Number(packageInfo.netWeight),
          numberOfBoxes: Number(packageInfo.totalBoxes),
          qrCode: packageInfo.qrId ?? '',
          batchlot: packageInfo.batchlot ?? '',
          varietyId: varietyParse?.originalId ?? '',
          varietyName: varietyParse?.name,
          gradeId: packageInfo.grade,
          packingDate,
        };
      }),
      documentIds: fileIds?.length ? [...fileIds] : undefined,
      shipmentPhotoIds: photoIds?.length ? [...photoIds] : undefined,
    };

  return updateForm;
};
