import { NextPage } from 'next';
import dynamic from 'next/dynamic';
import { redirect } from 'next/navigation';

import { createServerEventService } from 'app/(main)/_util/api-server';
import { AxiosError } from 'axios';
import camelcaseKeys from 'camelcase-keys';
import { errorRoute } from 'routes/client-routes';
import { PackingHouseDetail } from 'types';

const EditShipment = dynamic(() => import('../../_container/edit-page'));

type IndexProps = {
  params: Promise<{
    id: string;
  }>;
};

const Index: NextPage<IndexProps> = async ({ params }) => {
  const { id } = await params;

  const api = await createServerEventService();

  let shipmentDetail: PackingHouseDetail;

  try {
    const { data: response } = await api.get(`/v1/search/events/shipment/${id}`);

    if (!response.data) {
      return redirect(errorRoute.notFound);
    }

    shipmentDetail = camelcaseKeys(response.data, { deep: true });
  } catch (error) {
    if (error instanceof AxiosError) {
      if (error.status === 404) {
        return redirect(errorRoute.notFound);
      }
    }

    throw error;
  }

  return <EditShipment shipmentDetail={shipmentDetail} />;
};

export default Index;
