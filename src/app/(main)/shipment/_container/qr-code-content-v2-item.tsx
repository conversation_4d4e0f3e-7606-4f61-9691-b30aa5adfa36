import WarningAmberIcon from '@mui/icons-material/WarningAmber';
import { Grid, Grow, Stack, Typography } from '@mui/material';
import qrGenerateIconUrl from 'assets/icons/generate-icon.svg';
import selectQrIconUrl from 'assets/icons/status.svg';
import PackingQrCode from 'components/qr-code-review/qr-code-review';
import { formatDateWithLocale } from 'containers/event/_components';
import { get, omit } from 'lodash-es';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useCreateShipmentStore } from 'store/useCreateShipmentStore';
import { useMasterDataStore } from 'store/useMaterDataStore';
import { useUserStore } from 'store/useUserStore';
import { DurianInformationForm, DurianVariety, FormShipmentStepEnum } from 'types';
import { formatDate, formatNumberWithCommas, safeJSONParse } from 'utils';
import { getLabelByLocale } from 'utils/cookie-client';
import { formatOrchardNo } from 'utils/format';

import { SelectAvailableQrDialog, useSelectAvailableQrDialogProps } from 'components/select-available-qr';
import { FC, useMemo, useState } from 'react';
import { PackagingDurianBox } from '../_components';
import { QrCodeMenuItems } from '../_components/qrcode-menu-item';

export const QrCodeReviewContentItem: FC<{
  item: DurianInformationForm;
}> = ({ item: it }) => {
  const {
    generateSingleQrCodeBatchLot,
    removeQrCode,
    selectAvailableQr: setQrIdPacking,
    updateStep,
    ocrFile,
  } = useCreateShipmentStore();
  const { getGradeLabel, getProductTypeLabel, getBrandLabel } = useMasterDataStore();
  const selectAvailableQrDialogProps = useSelectAvailableQrDialogProps();

  const { informationFormValues } = useCreateShipmentStore();

  const [loading, setLoading] = useState(false);

  const user = useUserStore((state) => state.user);

  const shipmentT = useTranslations('shipment');

  const supplier = get(user, 'profile.supplierId', {});

  const packingHouse = get(supplier, 'packingHouse');

  const doaNumber = get(packingHouse, 'doaNumber.number', '');
  const variety = safeJSONParse<DurianVariety>(it.variety);
  const packedOnText = it.packingDate ? formatDateWithLocale(it.packingDate) : '';

  const onClickGenerateQrCode = async (id?: string) => {
    if (!id) {
      throw new Error('Batch lot ID is required to generate QR code');
    }

    setLoading(true);
    await generateSingleQrCodeBatchLot(id);
    setLoading(false);
  };

  const onClickSelectQrCode = () => {
    selectAvailableQrDialogProps.onOpen({
      onSelect: (selectParam) => {
        const { qrUrl, data } = selectParam;
        setQrIdPacking(it.id, qrUrl, data);
      },
    });
  };

  const dataSticker = useMemo(() => {
    if (it.qrCodeId && it.qrDetail) {
      return {
        exportCompany: it.qrDetail.nameOfExportingCompany,
        orchardRegisterNumber: it.qrDetail.orchardRegisterNumber,
        packingHouseRegisterNumber: doaNumber,
        packingDate: it.qrDetail.packingDate * 1000,
        exportTo: it.qrDetail.exportTo,
        productType: it.qrDetail.productType,
      };
    }

    if (!ocrFile) return null;

    return {
      exportCompany: ocrFile.nameOfExportingCompany ?? '',
      orchardRegisterNumber: ocrFile.orchardRegisterNumber ?? '',
      packingHouseRegisterNumber: doaNumber,
      packingDate: it.packingDate,
      exportTo: ocrFile.destinationCountry ?? '',
      productType: it.boxType ?? '',
    };
  }, [doaNumber, it.packingDate, it.boxType, it.qrCodeId, it.qrDetail, ocrFile]);

  const isWarning = it.notMatchedQrLabel;

  const goToMakeChanges = () => {
    updateStep(FormShipmentStepEnum.DurianInformationStep);
  };

  const disabledIds = useMemo(() => {
    return informationFormValues.filter((item) => item.qrCodeId).map((item) => item.qrCodeId);
  }, [informationFormValues]) as string[];

  return (
    <Grid
      size={{
        xs: 12,
      }}
      spacing={2}
    >
      <Stack justifyContent="space-between" flexDirection="row" alignItems="flex-start" sx={{ width: '100%' }}>
        <Stack flexGrow={1} maxWidth="800px" width="100%" flexDirection="column" gap={1}>
          <Typography variant="caption" color="text.secondary" mb="8px">
            {shipmentT('information-step')}
          </Typography>
          <PackagingDurianBox
            brandName={getBrandLabel(it.brand)}
            variety={getLabelByLocale(variety)}
            grade={getGradeLabel(it.grade)}
            netWeight={formatNumberWithCommas(it.netWeight)}
            totalBox={formatNumberWithCommas(it.totalBoxes)}
            typeOfBox={getProductTypeLabel(it.boxType)}
            packingDate={packedOnText}
          />
        </Stack>

        {((dataSticker && it.qrId) || loading) && (
          <Grow in timeout={500}>
            <Stack width="510px" flexDirection="column" gap={1}>
              <Stack flexDirection="row" justifyContent="space-between" alignItems="center">
                <Typography variant="caption" color="text.secondary">
                  {shipmentT('qrcode-box-label')}
                </Typography>

                <QrCodeMenuItems
                  autoGen={it.qrCodeId ? () => onClickGenerateQrCode(it.id) : undefined}
                  removeQrCode={() => {
                    removeQrCode(it.id);
                  }}
                  reselectQrCode={onClickSelectQrCode}
                />
              </Stack>

              <PackingQrCode
                isWarning={isWarning}
                hideDownloadButton
                flexAlign="flex-end"
                loading={loading}
                data={{
                  batchNumber: it?.batchlot ?? '',
                  boxType: getProductTypeLabel(dataSticker?.productType ?? ''),
                  qrUrl: it.qrUrl ?? '',
                  packingDate: dataSticker?.packingDate ? formatDate(dataSticker.packingDate) : '',
                  exportTo: dataSticker?.exportTo ?? '',
                  orchardRegisterNumber: dataSticker?.orchardRegisterNumber
                    ? `AC ${formatOrchardNo(dataSticker?.orchardRegisterNumber)}`
                    : undefined,
                  exportCompany: dataSticker?.exportCompany ?? '',
                  packingHouseRegisterNumber: dataSticker?.packingHouseRegisterNumber ?? '',
                }}
              />
              {isWarning && (
                <Stack
                  flexDirection="row"
                  alignItems={'center'}
                  sx={{
                    color: (theme) => theme.palette.customColors.warning500,
                    fontSize: '16px',
                    textAlign: 'center',
                  }}
                >
                  <WarningAmberIcon fontSize="small" />
                  <Typography ml="4px" component="span" variant="caption" color="inherit">
                    {`${shipmentT('qrcode-item-warning-label')},`}
                  </Typography>
                  <Typography
                    sx={{ cursor: 'pointer' }}
                    component="span"
                    fontWeight="bold"
                    variant="caption"
                    color="primary"
                    ml="4px"
                    onClick={goToMakeChanges}
                  >
                    {` ${shipmentT('qrcode-make-change')}.`}
                  </Typography>
                </Stack>
              )}
            </Stack>
          </Grow>
        )}

        {!it.qrId && !loading && (
          <Grow in timeout={500}>
            <Stack width="510px" flexDirection="column" gap={1}>
              <Stack flexDirection="row" justifyContent="space-between" alignItems="center">
                <Typography variant="caption" color="text.secondary">
                  {shipmentT('qrcode-box-label')}
                </Typography>

                <QrCodeMenuItems disable />
              </Stack>

              <Stack flexDirection="row" gap={2}>
                <Stack
                  component={'div'}
                  onClick={() => onClickGenerateQrCode(it.id)}
                  flexDirection={'column'}
                  gap={1}
                  width="50%"
                  alignItems="center"
                  justifyContent="center"
                  sx={{
                    border: '1px solid',
                    borderColor: (theme) => theme.palette.customColors.neutralBorder,
                    height: '110px',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    '&:hover': {
                      borderColor: (theme) => theme.palette.customColors.primary,
                      backgroundColor: (theme) => theme.palette.action.hover,
                      '& .qr-generate-img': {
                        transform: 'rotate(90deg)',
                        transition: 'transform 0.4s cubic-bezier(0.4,0,0.2,1)',
                      },
                    },
                    '& .qr-generate-img': {
                      transform: 'rotate(0deg)',
                      transition: 'transform 0.4s cubic-bezier(0.4,0,0.2,1)',
                    },
                  }}
                >
                  <Image
                    src={qrGenerateIconUrl}
                    alt="QR Code Generate Icon"
                    width={24}
                    height={24}
                    className="qr-generate-img"
                  />
                  <Typography variant="caption" color="text.secondary">
                    {shipmentT('qrcode-item-auto-generate-label')}
                  </Typography>
                </Stack>
                <Stack
                  component={'div'}
                  onClick={onClickSelectQrCode}
                  flexDirection={'column'}
                  gap={1}
                  width="50%"
                  alignItems="center"
                  justifyContent="center"
                  sx={{
                    border: '1px solid',
                    borderColor: (theme) => theme.palette.customColors.neutralBorder,
                    height: '110px',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    '&:hover': {
                      borderColor: (theme) => theme.palette.customColors.primary,
                      backgroundColor: (theme) => theme.palette.action.hover,
                      '& .qr-select-img': {
                        transform: 'scale(1.2)',
                      },
                    },
                    '& .qr-select-img': {
                      transform: 'scale(1)',
                    },
                  }}
                >
                  <Image
                    className="qr-select-img"
                    src={selectQrIconUrl}
                    alt="QR Code Select Icon"
                    width={24}
                    height={24}
                  />
                  <Typography variant="caption" color="text.secondary">
                    {shipmentT('qrcode-item-manual-select-label')}
                  </Typography>
                </Stack>
              </Stack>
            </Stack>
          </Grow>
        )}
      </Stack>
      <SelectAvailableQrDialog
        {...omit(
          {
            ...selectAvailableQrDialogProps,
            dialogState: {
              ...selectAvailableQrDialogProps.dialogState,
              disabledIds,
            },
          },
          'onOpen'
        )}
      />
    </Grid>
  );
};
