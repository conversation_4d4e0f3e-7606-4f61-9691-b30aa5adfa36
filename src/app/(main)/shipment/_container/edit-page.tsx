'use client';

import { Box, Button, IconButton, styled, Typography, useTheme } from '@mui/material';
import { useTranslations } from 'next-intl';

import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import DeleteIcon from '@mui/icons-material/Delete';
import { Breadcrumbs, Stepper } from 'components';
import { PromptDialog } from 'components/prompt-dialog';
import { ShipmentPromptDialog } from 'components/shipment-promp-dialog/shipment-prompt-dialog';
import dayjs from 'dayjs';
import { useUpdateShipmentMutate } from 'hooks/mutates/useUpdateShipmentMutate';
import { useGetReceivingByIdsQuery } from 'hooks/queries/useGetReceivingByIdsQuery';
import { useDeviceHeight } from 'hooks/useDeviceHeight';
import { useFeatureFlag } from 'hooks/useFeatureFlag';
import { useGetEventStatus } from 'hooks/useGetEventStatus';
import { headerHeight } from 'layouts/main/constant';
import { LoadingLayout } from 'layouts/main/loading-layout';
import cloneDeep from 'lodash-es/cloneDeep';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { clientRoutes } from 'routes/client-routes';
import { useCreateShipmentStore } from 'store/useCreateShipmentStore';
import {
  EventStatusEnum,
  FormShipmentStepEnum,
  PackingHouseDetail,
  Receipt,
  ShipmentIdentityType
} from 'types';
import { removeImgSuffix } from 'utils';
import { capturePosthog } from 'utils/posthog';
import toastMessages from 'utils/toastMessages';
import { v4 } from 'uuid';
import { DetailsDrawer, FormContent, SelectPackagingInformationDrawer } from '../_components';
import { defaultFormReceiptValues } from '../_constant/form';
import { QrCodeReviewContent } from '../_container/qr-code-review-content';
import { ShipmentIdentityContent } from '../_container/shipment-identity-content';
import { UploadDocumentsContent } from '../_container/upload-documents-content';
import { defaultReceiptFormValues, UploadReceiptContent } from '../_container/upload-receipt-content';
import { mapperEditShipmentForm, renderTitle } from '../_util/util';
import { useShipmentFormNavigation } from '../hooks/useShipmentFormNavigation';
import { SelectPackagingInformationContent } from './select-packaging-information-content';

const Root = styled(Box)`
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1;
  padding: 0;
`;

const ActionButtonGroup = styled(Box)`
  width: 100%;
  min-height: fit-content;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  align-items: flex-end;
  box-shadow: 1;
  position: relative;
`;

const StepContent = styled(Box)`
  width: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  background-color: ${({ theme }) => theme.palette.customColors.white};
`;

type IndexProps = {
  shipmentDetail: PackingHouseDetail;
};

const Index = (props: IndexProps) => {
  const { shipmentDetail } = props;
  const param = useParams<{ id: string }>();

  const id = param?.id ?? '';

  const shipmentNameTitle = shipmentDetail?.name ?? '';

  const shipmentT = useTranslations('shipment');
  const commonT = useTranslations('common');

  const router = useRouter();
  const { showWarningDialog, setShowWarningDialog } = useCreateShipmentStore();
  const [warningNotMatchDialog, setWarningNotMatchDialog] = useState<boolean>(false);

  const [warningNotMatchQrLabels, setWarningNotMatchQrLabels] = useState<string[]>([]);

  const {
    formStep,
    informationFormValues,
    ocrFile,
    additionalFiles,
    updateStep,
    updateOrcForm,
    generateQrCodeBatchLot,
    updateShipmentIdentity,
    resetStore,
    setIsUploadOcrError,
    initEditForm,
    receivingIds,
    setManualInput,
    manualInputOrc,
    setReceivingDetails,
    shipmentPhotos,
  } = useCreateShipmentStore();

  const [loadingForm, setLoadingForm] = useState<boolean>(false);

  const { ENABLED_SHIPMENT_MAP_QR } = useFeatureFlag();

  const shipmentForm = useForm<ShipmentIdentityType>({
    defaultValues: {
      name: shipmentDetail.name ?? '',
    },
    mode: 'onBlur',
  });

  const { data: receivingData } = useGetReceivingByIdsQuery([...receivingIds]);

  useEffect(() => {
    if (formStep === FormShipmentStepEnum.SelectedProductIds) {
      updateStep(FormShipmentStepEnum.ShipmentIdentity);
    }
  }, [formStep, updateStep]);

  useEffect(() => {
    if (receivingData?.length) {
      setReceivingDetails(receivingData);
    }
  }, [receivingData, setReceivingDetails]);

  const { getStatusLabel } = useGetEventStatus();

  const unauthorizedT = useTranslations('unauthorize');

  const { eventStatus } = getStatusLabel(shipmentDetail.status, shipmentDetail.type);

  const isDisabledEdit = useMemo(() => {
    const isWaiting = eventStatus === 'waiting';

    const isDraft = eventStatus === 'draft';

    if (isWaiting || isDraft) {
      return false;
    }

    return true;
  }, [eventStatus]);

  if (isDisabledEdit) {
    toastMessages.error(unauthorizedT('unauthorized-description'));
    router.push(clientRoutes.shipment);
  }

  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      e.preventDefault();
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  const receiptFormOptions = useForm<Receipt>({
    defaultValues: {
      ...defaultReceiptFormValues,
    },
    mode: 'onSubmit',
  });

  const deviceHeight = useDeviceHeight();

  const minHeight = deviceHeight - headerHeight - 420;

  const { watch: watchReceiptForm } = receiptFormOptions;

  const receiptNumber = watchReceiptForm('receiptNumber');

  useEffect(() => {
    if (shipmentDetail) {
      resetStore();
      initEditForm(cloneDeep(shipmentDetail));
    }
  }, [initEditForm, resetStore, shipmentDetail]);

  const { mutateAsync, isPending } = useUpdateShipmentMutate({
    onSuccess: async () => {
      capturePosthog('shipment_created_success');
      toastMessages.success(shipmentT('update-success'));
      router.push(clientRoutes.shipment);
    },
    onError: () => {
      setLoadingForm(false);
      toastMessages.error(shipmentT('update-fail'));
    },
  });

  const [openDetailModal, setOpenDetailModal] = useState<boolean>(false);
  const theme = useTheme();

  const steps = [
    shipmentT('shipment-identity'),
    shipmentT('information-step'),
    shipmentT('receipt-step'),
    shipmentT('add-document-step'),
    shipmentT('qr-review'),
  ];

  const checkDisableNextButton = () => {
    let isValid = true;

    switch (formStep) {
      case FormShipmentStepEnum.DurianInformationStep:
        isValid = informationFormValues.length > 0;
        break;
      case FormShipmentStepEnum.QrReviewStep:
        isValid = informationFormValues.every((it) => it.qrId);
        break;
      case FormShipmentStepEnum.ReceiptStep:
        isValid = !!receiptNumber;
        break;
      case FormShipmentStepEnum.UploadDocumentStep:
      default:
        isValid = true;
        break;
    }

    return isValid;
  };

  const onSubmitShipment = async (asDraft?: boolean) => {
    const isValid = await shipmentForm.trigger();

    if (!isValid) {
      updateStep(FormShipmentStepEnum.ShipmentIdentity);
      return;
    }

    const fileIds = additionalFiles.map((it) => it.url)?.map((it) => removeImgSuffix(it));

    setLoadingForm(true);

    const photoIds = shipmentPhotos?.map((it) => removeImgSuffix(it.url));

    const ocrFormValue = receiptFormOptions.getValues();

    const updateForm = await mapperEditShipmentForm({
      id,
      asDraft,
      shipmentName: shipmentForm.getValues().name,
      receivingIds,
      ocrFormValue,
      informationFormValues,
      fileIds,
      photoIds,
    });
    await mutateAsync(updateForm);
  };

  const handleSubmitReceiptForm = (values: Receipt) => {
    const ocrId = ocrFile?.id ?? v4();

    const exportDateForm = dayjs(values.exportDate);

    updateOrcForm({
      ...values,
      id: ocrId,
      sourceFrom: values.sourceFrom ?? 'ephyto',
      exportDate: exportDateForm.valueOf(),
    });

    setIsUploadOcrError(false);
  };

  const onGoBack = () => {
    updateStep(formStep - 1);
  };

  const onConfirmDialog = () => {
    router.push(clientRoutes.shipment);
  };

  const confirmSubmitShipment = async () => {
    await onSubmitShipment();
  };

  const breadcrumbs = [
    {
      label: commonT('shipment'),
      href: clientRoutes.shipment,
    },
    {
      label: shipmentNameTitle,
      href: clientRoutes.createShipment,
    },
  ];

  const isReceipt = formStep === FormShipmentStepEnum.ReceiptStep;

  const continueBtnLabel = useMemo(() => {
    switch (formStep) {
      case FormShipmentStepEnum.UploadDocumentStep:
        return shipmentT('generate-qr-code');
      case FormShipmentStepEnum.QrReviewStep:
        return commonT('submit');
      case FormShipmentStepEnum.ReceiptStep:
      case FormShipmentStepEnum.DurianInformationStep:
      default:
        return commonT('continue');
    }
  }, [commonT, formStep, shipmentT]);

  const { onNext } = useShipmentFormNavigation({
    formStep,
    updateStep,
    informationFormValues,
    onSubmitShipment,
    generateQrCodeBatchLot,
    ENABLED_SHIPMENT_MAP_QR,
    setWarningNotMatchQrLabels,
    setWarningNotMatchDialog,
    setLoadingForm,
    receiptFormOptions,
    handleSubmitReceiptForm,
    shipmentForm,
    updateShipmentIdentity,
  });

  return (
    <>
      <LoadingLayout loading={loadingForm}>
        <Box component="div" id="edit-shipment-wrapper" sx={{ flex: 1, p: '20px' }}>
          <Breadcrumbs items={breadcrumbs} />
          <Root>
            <Box sx={{ display: 'flex', gap: '8px', height: '60px', alignItems: 'center' }}>
              <IconButton onClick={() => setShowWarningDialog(true)}>
                <ArrowBackIcon />
              </IconButton>
              <Typography variant="h6" my="12px" fontWeight="bold">
                {`${shipmentT('edit-record')} ${shipmentNameTitle}`}
              </Typography>
              <Typography variant="h6" my="12px" fontWeight="bold" color="text.secondary">
                -
              </Typography>
              <Typography variant="h6" my="12px" fontWeight="bold" color="text.secondary">
                {shipmentDetail?.batchlot}
              </Typography>
            </Box>

            <Box
              sx={{
                display: 'flex',
                gap: '12px',
                alignItems: 'center',
                backgroundColor: theme.palette.customColors.primary100,
                p: '12px 16px',
                boxShadow: 1,
                borderRadius: 2,
              }}
            >
              <Typography>
                {shipmentT.markup('batch-lot-selected', {
                  x: receivingIds?.length ?? 0,
                })}
              </Typography>
              <Typography
                component="a"
                onClick={() => setOpenDetailModal(true)}
                variant="body1"
                color="primary"
                sx={{
                  cursor: 'pointer',
                  '&:hover': {
                    textDecoration: 'underline',
                  },
                }}
              >
                {commonT('view-detail')}
              </Typography>
            </Box>

            <Stepper activeStep={formStep - 1} steps={steps} />
            <StepContent sx={{ boxShadow: 1, p: '20px 16px', position: 'relative', borderRadius: 2 }}>
              <Box component="div">
                <Typography variant="body1" fontWeight="bold">
                  {renderTitle(formStep)}
                </Typography>

                {isReceipt && (ocrFile?.receiptNumber || manualInputOrc) && (
                  <IconButton
                    color="error"
                    size="medium"
                    onClick={() => {
                      updateOrcForm(undefined);
                      receiptFormOptions.reset({ ...defaultFormReceiptValues });
                      setManualInput(false);
                    }}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      width: '50px',
                      position: 'absolute',
                      right: '12px',
                      top: '16px',
                      cursor: 'pointer',
                      justifyContent: 'flex-end',
                    }}
                  >
                    <DeleteIcon fontSize="inherit" />
                  </IconButton>
                )}

                {/* Content form */}
                <Box
                  component="div"
                  sx={{
                    width: '100%',
                    flex: 1,
                    minHeight: `${minHeight}px`,
                    position: 'relative',
                    display: 'flex',
                    flexDirection: 'column',
                  }}
                >
                  <FormContent active={formStep === FormShipmentStepEnum.ShipmentIdentity} minHeight={minHeight}>
                    <ShipmentIdentityContent formOptions={shipmentForm} />
                  </FormContent>
                  <FormContent active={formStep === FormShipmentStepEnum.DurianInformationStep} minHeight={minHeight}>
                    <SelectPackagingInformationContent />
                  </FormContent>
                  <FormContent active={formStep === FormShipmentStepEnum.ReceiptStep} minHeight={minHeight}>
                    <UploadReceiptContent receiptFormOptions={receiptFormOptions} />
                  </FormContent>
                  <FormContent active={formStep === FormShipmentStepEnum.UploadDocumentStep} minHeight={minHeight}>
                    <UploadDocumentsContent minHeight={minHeight} />
                  </FormContent>
                  <FormContent active={formStep === FormShipmentStepEnum.QrReviewStep} minHeight={minHeight}>
                    <QrCodeReviewContent />
                  </FormContent>
                </Box>
              </Box>
            </StepContent>

            <ActionButtonGroup>
              {shipmentDetail?.status === EventStatusEnum.DRAFT && (
                <Button
                  variant="text"
                  onClick={() => {
                    setShowWarningDialog(true);
                  }}
                  sx={{
                    color: theme.palette.customColors.neutral700,
                    position: 'absolute',
                    top: 0,
                    left: '-10px',
                  }}
                >
                  {shipmentT('discard')}
                </Button>
              )}

              {shipmentDetail?.status === EventStatusEnum.DRAFT && (
                <Button
                  sx={{ width: '200px', color: theme.palette.customColors.neutral700 }}
                  onClick={() => {
                    onSubmitShipment(true);
                  }}
                >
                  {commonT('save-as-draft')}
                </Button>
              )}

              <Button
                variant="outlined"
                color="primary"
                sx={{ width: '200px' }}
                onClick={onGoBack}
                disabled={formStep === FormShipmentStepEnum.ShipmentIdentity || isPending}
              >
                {commonT('back')}
              </Button>
              <Button
                variant="contained"
                sx={{ width: '200px' }}
                disabled={!checkDisableNextButton()}
                loading={isPending || loadingForm}
                onClick={onNext}
              >
                {continueBtnLabel}
              </Button>
            </ActionButtonGroup>
          </Root>
          <SelectPackagingInformationDrawer />
          {receivingIds.length !== 0 && <DetailsDrawer open={openDetailModal} toggle={setOpenDetailModal} />}
        </Box>

        <PromptDialog
          open={showWarningDialog}
          onClose={() => setShowWarningDialog(false)}
          onConfirm={onConfirmDialog}
          confirmText={commonT('confirm')}
          title={shipmentT('warning-cancel-draft-shipment-title')}
          content={shipmentT('warning-cancel-draft-shipment')}
        />
      </LoadingLayout>
      <ShipmentPromptDialog
        open={warningNotMatchDialog}
        onClose={() => setWarningNotMatchDialog(false)}
        onConfirm={confirmSubmitShipment}
        title={shipmentT('warning-not-match-title')}
        contents={warningNotMatchQrLabels}
      />
    </>
  );
};

export default Index;
