import { Box, Grid } from '@mui/material';
import PackingQrCode from 'components/qr-code-review/qr-code-review';
import { useFeatureFlag } from 'hooks/useFeatureFlag';
import { get } from 'lodash-es';
import { useCreateShipmentStore } from 'store/useCreateShipmentStore';
import { useMasterDataStore } from 'store/useMaterDataStore';
import { useUserStore } from 'store/useUserStore';
import { formatDate } from 'utils';
import { formatOrchardNo } from 'utils/format';
import { QrCodeReviewContent as QrCodeReviewContentV2 } from './qr-code-review-content-v2';

const QrCodeReviewContentV1 = () => {
  const { informationFormValues, ocrFile } = useCreateShipmentStore();
  const { getProductTypeLabel } = useMasterDataStore();
  const user = useUserStore((state) => state.user);

  const supplier = get(user, 'profile.supplierId', {});

  const packingHouse = get(supplier, 'packingHouse');

  const doaNumber = get(packingHouse, 'doaNumber.number', '');

  return (
    <Box sx={{ width: '100%', overflowY: 'auto', height: 'auto' }}>
      <Grid container spacing={2} mt="16px">
        {informationFormValues.map((it) => {
          return (
            <Grid
              key={it.id}
              size={{
                xs: 12,
              }}
            >
              <PackingQrCode
                data={{
                  batchNumber: it.batchlot ?? '',
                  boxType: getProductTypeLabel(it?.boxType),
                  qrUrl: it.qrUrl ?? '',
                  packingDate: formatDate(it?.packingDate ?? ''),
                  exportTo: ocrFile?.destinationCountry,
                  orchardRegisterNumber: ocrFile?.orchardNo ? `AC ${formatOrchardNo(ocrFile?.orchardNo)}` : undefined,
                  exportCompany: ocrFile?.nameOfExportingCompany,
                  packingHouseRegisterNumber: doaNumber,
                }}
              />
            </Grid>
          );
        })}
      </Grid>
    </Box>
  );
};

export const QrCodeReviewContent = () => {
  const { ENABLED_SHIPMENT_MAP_QR } = useFeatureFlag();

  if (ENABLED_SHIPMENT_MAP_QR) {
    return <QrCodeReviewContentV2 />;
  }

  return <QrCodeReviewContentV1 />;
};
