import AddIcon from '@mui/icons-material/Add';
import { Button, Grid, Typography, useTheme } from '@mui/material';
import { useTranslations } from 'next-intl';
import { useCreateShipmentStore } from 'store/useCreateShipmentStore';

import { useMasterDataStore } from 'store/useMaterDataStore';
import { formatNumberWithCommas, safeJSONParse } from 'utils';
import { getLabelByLocale } from 'utils/cookie-client';
import { PackagingDurianBox } from '../_components/packaging-durian-box';
import { formatDateWithLocale } from 'containers/event/_components';
import { VarietyOptionValue } from 'types';

export const SelectPackagingInformationContent = () => {
  const shipmentT = useTranslations('shipment');
  const { informationFormValues, updateOpenPackagingModal, deleteInformationForm } = useCreateShipmentStore();
  const { getBrandLabel, getProductTypeLabel, getGradeLabel } = useMasterDataStore();

  const theme = useTheme();

  return (
    <Grid container spacing={2} mt="16px" sx={{ width: '100%' }}>
      {informationFormValues.map((it, index) => {
        const variety = safeJSONParse<VarietyOptionValue>(it.variety);
        const packedOnText = it.packingDate ? formatDateWithLocale(it.packingDate) : '';
        return (
          <Grid
            key={`${it.variety}-${index}`}
            size={{
              md: 12,
              lg: 6,
              xl: 6,
            }}
          >
            <PackagingDurianBox
              isWarning={it.notMatchedQrLabel}
              brandName={getBrandLabel(it.brand)}
              variety={getLabelByLocale(variety)}
              grade={getGradeLabel(it.grade)}
              onEdit={() => updateOpenPackagingModal(true, it.id)}
              onDelete={() => deleteInformationForm(it.id ?? '')}
              netWeight={formatNumberWithCommas(it.netWeight)}
              totalBox={formatNumberWithCommas(it.totalBoxes)}
              typeOfBox={getProductTypeLabel(it.boxType)}
              packingDate={packedOnText}
            />
          </Grid>
        );
      })}

      <Grid
        size={{
          md: 12,
          lg: 6,
          xl: 6,
        }}
      >
        <Button
          component="div"
          sx={{
            width: '100%',
            height: '120px',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            gap: '8px',
            bgcolor: theme.palette.customColors.primary50,
            borderRadius: '8px',
            boxSizing: 'border-box',
            color: theme.palette.customColors.primary,
            cursor: 'pointer',
            border: `2px dashed ${theme.palette.customColors.primary}`,
          }}
          onClick={() => updateOpenPackagingModal(true)}
        >
          <AddIcon />
          <Typography ml="8px">{shipmentT('add-packaging-btn-label')}</Typography>
        </Button>
      </Grid>
    </Grid>
  );
};
