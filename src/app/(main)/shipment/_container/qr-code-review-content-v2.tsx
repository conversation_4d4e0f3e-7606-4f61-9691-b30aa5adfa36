import { Box, Grid } from '@mui/material';

import { useCreateShipmentStore } from 'store/useCreateShipmentStore';

import { QrCodeReviewContentItem } from './qr-code-content-v2-item';

export const QrCodeReviewContent = () => {
  const { informationFormValues } = useCreateShipmentStore();

  return (
    <Box sx={{ width: '100%', overflowY: 'auto', height: 'auto' }}>
      <Grid container spacing={2} mt="16px">
        {informationFormValues.map((it) => {
          return <QrCodeReviewContentItem key={it.id} item={it} />;
        })}
      </Grid>
    </Box>
  );
};
