'use client';

import { Box, Button, IconButton, Typography, useTheme } from '@mui/material';
import { useTranslations } from 'next-intl';

import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import DeleteIcon from '@mui/icons-material/Delete';
import { Breadcrumbs, Stepper } from 'components';
import { ShipmentPromptDialog } from 'components/shipment-promp-dialog/shipment-prompt-dialog';
import { useCreateShipmentMutate } from 'hooks/mutates/useCreateShipmentMutate';
import { useGetReceivingByIdsQuery } from 'hooks/queries/useGetReceivingByIdsQuery';
import { useDeviceHeight } from 'hooks/useDeviceHeight';
import { useFeatureFlag } from 'hooks/useFeatureFlag';
import { headerHeight } from 'layouts/main/constant';
import { LoadingLayout } from 'layouts/main/loading-layout';
import { useRouter } from 'next/navigation';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { clientRoutes } from 'routes/client-routes';
import { useCreateShipmentStore } from 'store/useCreateShipmentStore';
import { FormShipmentStepEnum, Receipt, ShipmentIdentityType } from 'types';
import { removeImgSuffix } from 'utils';
import { capturePosthog } from 'utils/posthog';
import toastMessages from 'utils/toastMessages';
import { v4 } from 'uuid';
import { FormContent, SelectPackagingInformationDrawer } from '../_components';
import { ActionButtonGroup, Root, StepContent } from '../_components/components.style';
import { DetailsDrawer } from '../_components/details-drawer';
import { QrCodeReviewContent } from '../_container/qr-code-review-content';
import { ShipmentIdentityContent } from '../_container/shipment-identity-content';
import { UploadDocumentsContent } from '../_container/upload-documents-content';
import { defaultReceiptFormValues, UploadReceiptContent } from '../_container/upload-receipt-content';
import { mapperCreateShipmentForm, renderTitle } from '../_util/util';
import { useShipmentFormNavigation } from '../hooks/useShipmentFormNavigation';
import { SelectPackagingInformationContent } from './select-packaging-information-content';

const defaultShipmentForm = {
  name: '',
};

export const ShipmentFormStep = () => {
  const shipmentT = useTranslations('shipment');
  const commonT = useTranslations('common');

  const [loadingPage, setLoadingPage] = useState(false);
  const { setShowWarningDialog } = useCreateShipmentStore();
  const [loadingForm, setLoadingForm] = useState<boolean>(false);

  const { ENABLED_SHIPMENT_MAP_QR } = useFeatureFlag();

  const {
    formStep,
    informationFormValues,
    ocrFile,
    additionalFiles,
    updateStep,
    updateOrcForm,
    generateQrCodeBatchLot,
    updateShipmentIdentity,
    setIsUploadOcrError,
    receivingIds,
    setManualInput,
    manualInputOrc,
    setReceivingDetails,
    shipmentPhotos,
    shipmentIdentity,
  } = useCreateShipmentStore();

  const shipmentForm = useForm<ShipmentIdentityType>({
    defaultValues: {
      ...defaultShipmentForm,
      ...shipmentIdentity,
    },
    mode: 'onBlur',
  });

  const { data: receivingData } = useGetReceivingByIdsQuery([...receivingIds]);

  useEffect(() => {
    if (receivingData?.length) {
      setReceivingDetails(receivingData);
    }
  }, [receivingData, setReceivingDetails]);

  const stepContentRef = useRef<HTMLDivElement>(null);

  const receiptFormOptions = useForm<Receipt>({
    defaultValues: {
      ...defaultReceiptFormValues,
    },
    mode: 'onSubmit',
  });

  const router = useRouter();
  const deviceHeight = useDeviceHeight();

  const minHeight = deviceHeight - headerHeight - 420;

  const { mutateAsync, isPending } = useCreateShipmentMutate({
    onSuccess: async () => {
      capturePosthog('shipment_created_success');
      toastMessages.success(shipmentT('create-success'));
      router.push(clientRoutes.shipment);
    },
    onError: () => {
      toastMessages.error(shipmentT('create-failed'));
      setLoadingPage(false);
    },
  });

  const [openDetailModal, setOpenDetailModal] = useState<boolean>(false);
  const theme = useTheme();

  const steps = [
    shipmentT('shipment-identity'),
    shipmentT('information-step'),
    shipmentT('receipt-step'),
    shipmentT('add-document-step'),
    shipmentT('qr-review'),
  ];

  const checkDisableNextButton = () => {
    let isValid = true;

    switch (formStep) {
      case FormShipmentStepEnum.DurianInformationStep:
        isValid = informationFormValues.length > 0;
        break;
      case FormShipmentStepEnum.QrReviewStep:
        isValid = informationFormValues.every((it) => it.qrId);
        break;
      case FormShipmentStepEnum.ReceiptStep:
        isValid = !!ocrFile;
        break;
      case FormShipmentStepEnum.UploadDocumentStep:
      default:
        isValid = true;
        break;
    }

    return isValid;
  };

  const onSubmitShipment = async (asDraft?: boolean) => {
    const isValid = await shipmentForm.trigger();
    if (!isValid) {
      updateStep(FormShipmentStepEnum.ShipmentIdentity);
      return;
    }
    const fileIds = additionalFiles.map((it) => it.url)?.map((it) => removeImgSuffix(it));
    const photoIds = shipmentPhotos?.map((it) => removeImgSuffix(it.url));
    setLoadingPage(true);
    const ocrFormValue = receiptFormOptions.getValues();

    const createShipmentForm = await mapperCreateShipmentForm({
      asDraft,
      shipmentName: shipmentForm.getValues().name ?? '',
      receivingIds,
      ocrFormValue,
      informationFormValues,
      fileIds,
      photoIds,
    });

    await mutateAsync(createShipmentForm);
  };

  const handleSubmitReceiptForm = (values: Receipt) => {
    const id = ocrFile?.id ?? v4();

    updateOrcForm({
      ...values,
      id,
      sourceFrom: values.sourceFrom ?? 'ephyto',
    });

    setIsUploadOcrError(false);
  };
  const [warningNotMatchDialog, setWarningNotMatchDialog] = useState<boolean>(false);

  const [warningNotMatchQrLabels, setWarningNotMatchQrLabels] = useState<string[]>([]);

  const { onNext } = useShipmentFormNavigation({
    formStep,
    updateStep,
    informationFormValues,
    onSubmitShipment,
    generateQrCodeBatchLot,
    ENABLED_SHIPMENT_MAP_QR,
    setWarningNotMatchQrLabels,
    setWarningNotMatchDialog,
    setLoadingForm,
    receiptFormOptions,
    handleSubmitReceiptForm,
    shipmentForm,
    updateShipmentIdentity,
  });

  const onGoBack = () => {
    if (formStep === FormShipmentStepEnum.ShipmentIdentity) {
      const shipmentIdentityValues = shipmentForm.getValues();
      updateShipmentIdentity(shipmentIdentityValues?.name ?? '');
    }

    updateStep(formStep - 1);
  };

  const breadcrumbs = [
    {
      label: commonT('shipment'),
      href: clientRoutes.shipment,
    },
    {
      label: commonT('create-shipment-title'),
      href: clientRoutes.createShipment,
    },
  ];

  const isReceipt = formStep === FormShipmentStepEnum.ReceiptStep;

  const continueBtnLabel = useMemo(() => {
    switch (formStep) {
      case FormShipmentStepEnum.UploadDocumentStep:
        return shipmentT('generate-qr-code');
      case FormShipmentStepEnum.QrReviewStep:
        return commonT('submit');
      case FormShipmentStepEnum.ReceiptStep:
      case FormShipmentStepEnum.DurianInformationStep:
      default:
        return commonT('continue');
    }
  }, [commonT, formStep, shipmentT]);

  const actualStep = formStep - 1;

  const confirmSubmitShipment = async () => {
    await onSubmitShipment();
  };

  return (
    <LoadingLayout loading={loadingForm}>
      <Box component="div" id="create-shipment-wrapper" sx={{ flex: 1, p: '20px' }}>
        <Breadcrumbs items={breadcrumbs} />
        <Root>
          <Box sx={{ display: 'flex', gap: '8px', height: '60px', alignItems: 'center' }}>
            <IconButton onClick={() => setShowWarningDialog(true)}>
              <ArrowBackIcon />
            </IconButton>
            <Typography variant="h6" my="12px">
              {commonT('create-shipment-title')}
            </Typography>
          </Box>
          <Box
            sx={{
              display: 'flex',
              gap: '12px',
              alignItems: 'center',
              backgroundColor: theme.palette.customColors.primary100,
              p: '12px 16px',
              boxShadow: 1,
              borderRadius: 2,
            }}
          >
            <Typography>
              {shipmentT.markup('batch-lot-selected', {
                x: receivingIds?.length ?? 0,
              })}
            </Typography>{' '}
            <Typography
              component="a"
              onClick={() => setOpenDetailModal(true)}
              variant="body1"
              color="primary"
              sx={{
                cursor: 'pointer',
                '&:hover': {
                  textDecoration: 'underline',
                },
              }}
            >
              {commonT('view-detail')}
            </Typography>
          </Box>
          <Stepper activeStep={actualStep} steps={steps} />
          <StepContent
            sx={{ boxShadow: 1, p: '20px 16px', position: 'relative', height: `fit-content`, flex: 'unset' }}
          >
            <Typography variant="body1" fontWeight="bold">
              {renderTitle(formStep)}
            </Typography>
            {isReceipt && (ocrFile?.receiptNumber || manualInputOrc) && (
              <IconButton
                color="error"
                size="medium"
                onClick={() => {
                  updateOrcForm(undefined);
                  receiptFormOptions.reset({ ...defaultReceiptFormValues });
                  setManualInput(false);
                }}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  width: '50px',
                  position: 'absolute',
                  right: '12px',
                  top: '16px',
                  cursor: 'pointer',
                  justifyContent: 'flex-end',
                }}
              >
                <DeleteIcon fontSize="inherit" />
              </IconButton>
            )}
            <Box
              component="div"
              ref={stepContentRef}
              sx={{
                width: '100%',
                minHeight: `${minHeight}px`,
                position: 'relative',
                display: 'flex',
              }}
            >
              <FormContent active={formStep === FormShipmentStepEnum.ShipmentIdentity} minHeight={minHeight}>
                <ShipmentIdentityContent formOptions={shipmentForm} />
              </FormContent>
              <FormContent active={formStep === FormShipmentStepEnum.DurianInformationStep} minHeight={minHeight}>
                <SelectPackagingInformationContent />
              </FormContent>
              <FormContent active={formStep === FormShipmentStepEnum.ReceiptStep} minHeight={minHeight}>
                <UploadReceiptContent receiptFormOptions={receiptFormOptions} />
              </FormContent>
              <FormContent active={formStep === FormShipmentStepEnum.UploadDocumentStep} minHeight={minHeight}>
                <UploadDocumentsContent minHeight={minHeight} />
              </FormContent>
              <FormContent active={formStep === FormShipmentStepEnum.QrReviewStep} minHeight={minHeight}>
                <QrCodeReviewContent />
              </FormContent>
            </Box>
          </StepContent>
          <ActionButtonGroup>
            <Button
              variant="text"
              onClick={() => {
                setShowWarningDialog(true);
              }}
              sx={{
                color: theme.palette.customColors.neutral700,
                position: 'absolute',
                top: 0,
                left: '-10px',
              }}
            >
              {shipmentT('discard')}
            </Button>

            <Button
              variant="text"
              sx={{ width: '200px', color: theme.palette.customColors.neutral700 }}
              onClick={() => {
                onSubmitShipment(true);
              }}
            >
              {commonT('save-as-draft')}
            </Button>
            <Button variant="outlined" sx={{ width: '200px' }} onClick={onGoBack} disabled={isPending}>
              {commonT('back')}
            </Button>
            <Button
              variant="contained"
              sx={{ width: '200px' }}
              disabled={!checkDisableNextButton()}
              loading={isPending || loadingPage}
              onClick={onNext}
            >
              {continueBtnLabel}
            </Button>
          </ActionButtonGroup>
        </Root>
      </Box>
      <DetailsDrawer open={openDetailModal} toggle={setOpenDetailModal} />
      <SelectPackagingInformationDrawer />
      <ShipmentPromptDialog
        open={warningNotMatchDialog}
        onClose={() => setWarningNotMatchDialog(false)}
        onConfirm={confirmSubmitShipment}
        title={shipmentT('warning-not-match-title')}
        contents={warningNotMatchQrLabels}
      />
    </LoadingLayout>
  );
};
