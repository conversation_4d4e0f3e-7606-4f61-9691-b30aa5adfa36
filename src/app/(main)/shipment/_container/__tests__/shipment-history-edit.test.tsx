import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import ShipmentHistoryEdit from '../shipment-history-edit';
import { PackingHouseDetail } from 'types';

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: jest.fn(),
}));

// Mock components
jest.mock('components', () => ({
  Breadcrumbs: ({ items }: { items: { label: string; href: string; showConfirmation?: boolean; onClick?: () => void }[] }) => (
    <nav data-testid="breadcrumbs">
      {items.map((item, index) => (
        <span key={`breadcrumb-${index}`}>{item.label}</span>
      ))}
    </nav>
  ),
}));

jest.mock('../../_components/history-sort-buttons', () => ({
  HistorySortButtons: ({ sortOrder, onSortChange }: { sortOrder: 'newest' | 'oldest'; onSortChange: (order: 'newest' | 'oldest') => void }) => (
    <div data-testid="sort-buttons">
      <button onClick={() => onSortChange('newest')}>Newest First</button>
      <button onClick={() => onSortChange('oldest')}>Oldest First</button>
      <span data-testid="current-sort">{sortOrder}</span>
    </div>
  ),
}));

jest.mock('../../_components/history-display', () => ({
  HistoryDisplay: ({ entries, sortOrder }: {
    entries: Array<{ id: string; timestamp: string; user: { name: string }; action: string }>;
    sortOrder: 'newest' | 'oldest';
  }) => (
    <div data-testid="history-display">
      <span data-testid="sort-order">{sortOrder}</span>
      <span data-testid="entries-count">{entries.length}</span>
    </div>
  ),
}));

// Mock mock data
jest.mock('../../../../../data/mock-shipment-history', () => ({
  mockShipmentHistoryData: {
    shipmentId: 'SM211225000001',
    entries: [
      {
        id: '1',
        timestamp: '2025-01-15T14:32:00Z',
        user: { id: 'user1', name: 'Test User' },
        action: 'updated',
        actionLabel: { en: 'updated', th: 'อัปเดต' },
      },
    ],
  },
}));

const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockUseTranslations = useTranslations as jest.MockedFunction<typeof useTranslations>;

describe('ShipmentHistoryEdit', () => {
  const mockPush = jest.fn();
  const mockBack = jest.fn();
  const mockShipmentT = jest.fn();
  const mockCommonT = jest.fn();

  const mockShipmentDetail: PackingHouseDetail = {
    id: 'SM211225000001',
    status: 'draft',
    type: 'shipment',
    name: 'Test Shipment',
    description: 'Test shipment description',
    address: 'Test Address',
    country: 'Thailand',
    dateCreated: '2025-01-15T14:32:00Z',
    images: [],
    logo: { id: 'logo1', filenameDisk: 'logo.jpg', filenameDownload: 'logo.jpg' },
    farm: {
      address: 'Farm Address',
      id: 'farm-1',
      name: 'Test Farm',
    },
    gln: '1234567890123',
    positionLongitude: 100.5,
    positionLatitude: 13.7,
    role: 'packing_house_staff',
    batchlot: 'BATCH001',
    packingHouse: {
      id: 'ph-1',
      eventStatus: 'draft',
      eventType: 'shipment',
      name: 'Test Packing House',
      description: 'Test packing house description',
      dateCreated: '2025-01-15T14:32:00Z',
      eventTimeUnix: '1705363200',
      eventTime: '2025-01-15T14:32:00Z',
      address: 'Packing House Address',
      country: 'Thailand',
      images: [],
      logo: { id: 'ph-logo1', filenameDisk: 'ph-logo.jpg', filenameDownload: 'ph-logo.jpg' },
      gln: '9876543210987',
      productId: 'prod-1',
      status: 'draft',
      type: 'shipment',
      positionLongitude: 100.6,
      positionLatitude: 13.8,
      farm: {
        address: 'Farm Address',
        gln: null,
        id: 'farm-1',
        name: 'Test Farm',
        gap: 'GAP123',
      },
      role: 'packing_house_staff',
      userCreated: {
        id: 'user-1',
        firstName: 'John',
        lastName: 'Doe',
        role: 'farmer',
        profile: {
          nickname: 'Johnny',
        },
      },
      meta: {
        startTime: '2025-01-15T14:32:00Z',
        endTime: '2025-01-15T16:32:00Z',
        weight: 100,
        trunkPlateNumber: 'ABC-123',
        sourceMaterial: 'durian',
        flowerBloomingDay: '2025-01-10',
      },
      batchlot: 'BATCH001',
      originBatchlot: 'ORIGIN-BATCH001',
      varieties: [],
    },
    userCreated: {
      id: 'user-1',
      firstName: 'John',
      lastName: 'Doe',
      role: 'farmer',
      profile: {
        nickname: 'Johnny',
      },
    },
    packing: [],
    meta: {
      startTime: '2025-01-15T14:32:00Z',
      endTime: '2025-01-15T16:32:00Z',
      weight: 100,
      trunkPlateNumber: 'ABC-123',
      sourceMaterial: 'durian',
      flowerBloomingDay: '2025-01-10',
    },
    varieties: [],
    immutable: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseRouter.mockReturnValue({
      push: mockPush,
      back: mockBack,
      replace: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      prefetch: jest.fn(),
    });

    mockShipmentT.mockImplementation((key: string) => {
      const translations: Record<string, string> = {
        'shipment': 'Shipment',
        'history-edit-title': 'Edit history',
        'shipment-id': 'Shipment ID',
        'shipment-name': 'Shipment name',
      };
      return translations[key] || key;
    });

    mockCommonT.mockImplementation((key: string) => key);

    mockUseTranslations.mockImplementation((namespace?: string) => {
      const baseFn = namespace === 'shipment' ? mockShipmentT : mockCommonT;
      // Create a function with the required properties to match next-intl's translation function interface
      const translationFn = Object.assign(baseFn, {
        rich: jest.fn(),
        markup: jest.fn(),
        raw: jest.fn(),
        has: jest.fn(() => true),
      });
      return translationFn;
    });
  });

  it('renders page title and breadcrumbs', () => {
    render(
      <ShipmentHistoryEdit
        shipmentId="SM211225000001"
        shipmentDetail={mockShipmentDetail}
      />
    );

    expect(screen.getByTestId('breadcrumbs')).toBeInTheDocument();
    expect(screen.getByRole('heading', { name: 'Edit history' })).toBeInTheDocument();
  });

  it('displays shipment information in breadcrumbs', () => {
    render(
      <ShipmentHistoryEdit
        shipmentId="SM211225000001"
        shipmentDetail={mockShipmentDetail}
      />
    );

    // The shipment information is displayed in breadcrumbs
    expect(screen.getByText('shipments')).toBeInTheDocument();
    expect(screen.getByText('Test Shipment')).toBeInTheDocument();
    // Check for breadcrumb specifically
    const breadcrumbs = screen.getByTestId('breadcrumbs');
    expect(breadcrumbs).toHaveTextContent('Edit history');
  });

  it('renders sort buttons and history display', () => {
    render(
      <ShipmentHistoryEdit
        shipmentId="SM211225000001"
        shipmentDetail={mockShipmentDetail}
      />
    );

    expect(screen.getByTestId('sort-buttons')).toBeInTheDocument();
    expect(screen.getByTestId('history-display')).toBeInTheDocument();
  });

  it('handles sort order change', async () => {
    render(
      <ShipmentHistoryEdit
        shipmentId="SM211225000001"
        shipmentDetail={mockShipmentDetail}
      />
    );

    // Initial sort order should be 'newest'
    expect(screen.getByTestId('current-sort')).toHaveTextContent('newest');

    // Click oldest first button
    fireEvent.click(screen.getByText('Oldest First'));

    // Wait for state update
    await waitFor(() => {
      expect(screen.getByTestId('sort-order')).toHaveTextContent('oldest');
    });
  });

  it('handles back button click', () => {
    render(
      <ShipmentHistoryEdit
        shipmentId="SM211225000001"
        shipmentDetail={mockShipmentDetail}
      />
    );

    // Find the back button by its icon
    const backButton = screen.getByTestId('ArrowBackIcon').closest('button');
    expect(backButton).toBeInTheDocument();
    fireEvent.click(backButton!);

    expect(mockBack).toHaveBeenCalledTimes(1);
  });



  it('passes correct data to history display', async () => {
    render(
      <ShipmentHistoryEdit
        shipmentId="SM211225000001"
        shipmentDetail={mockShipmentDetail}
      />
    );

    await waitFor(() => {
      expect(screen.getByTestId('entries-count')).toHaveTextContent('1');
    });
  });

  it('uses correct translation keys', () => {
    render(
      <ShipmentHistoryEdit
        shipmentId="SM211225000001"
        shipmentDetail={mockShipmentDetail}
      />
    );

    expect(mockUseTranslations).toHaveBeenCalledWith('shipment');
    expect(mockShipmentT).toHaveBeenCalledWith('history-edit-title');
    expect(mockShipmentT).toHaveBeenCalledWith('shipments');
  });
});
