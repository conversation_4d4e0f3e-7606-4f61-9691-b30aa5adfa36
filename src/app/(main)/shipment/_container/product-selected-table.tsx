'use client';

import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import { Box, Button, Typography } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import type { GridColDef, GridRowSelectionModel } from '@mui/x-data-grid';
import { Breadcrumbs, EventDataTable, PromptDialog } from 'components';
import { MAX_SELECTIONS } from 'constant/event';
import { renderRecordBy, renderRecordOn, renderUpdatedBy, renderUpdatedOn } from 'containers/event/_components';
import { useDeviceHeight } from 'hooks/useDeviceHeight';
import { headerHeight } from 'layouts/main/constant';
import { isEqual } from 'lodash-es';
import { useTranslations } from 'next-intl';
import { type FC, useEffect, useMemo, useRef, useState } from 'react';
import { clientRoutes } from 'routes/client-routes';
import { useCreateShipmentStore } from 'store/useCreateShipmentStore';
import { FormShipmentStepEnum } from 'types';
import { formatNumberWithCommas } from 'utils';
import { sendEvent } from 'utils/gtag';
import { capturePosthog } from 'utils/posthog';

const DEFAULT_VALUE = '--';

export const CreateShipmentTable: FC = () => {
  const shipmentT = useTranslations('shipment');
  const receiveTranslation = useTranslations('receive');
  const theme = useTheme();

  const [selectedBatches, setSelectedBatches] = useState<GridRowSelectionModel>({ type: 'include', ids: new Set() });
  const confirmBatchLotChange = useTranslations('confirm-batch-lot-change');
  const commonTranslation = useTranslations('common');
  const receiveT = useTranslations('receive');
  const [showPromptMessage, setShowPromptMessage] = useState(false);

  const { setShowWarningDialog } = useCreateShipmentStore();

  const firstRender = useRef(true);

  const commonT = useTranslations('common');

  const refProductIds = useRef<string[]>(null);

  const deviceHeight = useDeviceHeight();

  const { setReceivingIds, updateStep, receivingIds, resetStore } = useCreateShipmentStore();

  const receivingIdsString = receivingIds.join(',');

  useEffect(() => {
    if (receivingIdsString && firstRender.current) {
      firstRender.current = false;
      const ids = new Set(receivingIdsString.split(','));
      setSelectedBatches({ type: 'include', ids });
    }
  }, [receivingIdsString]);

  const handleSubmit = async () => {
    const arraySelectedIds = Array.from(selectedBatches.ids) as string[];

    const selectedIds: string[] = [...arraySelectedIds];
    refProductIds.current = [...selectedIds];

    if (receivingIds.length && !isEqual(selectedIds, receivingIds)) {
      setShowPromptMessage(true);
      return;
    }
    capturePosthog('number_of_selected_batches', {
      num: selectedIds.length,
    });
    setReceivingIds(selectedIds);

    updateStep(FormShipmentStepEnum.ShipmentIdentity);
  };

  const onConfirm = async () => {
    resetStore();

    if (refProductIds.current) {
      setReceivingIds(refProductIds.current);
      sendEvent('selected_received_batch');
      capturePosthog('number_of_selected_batches', {
        num: refProductIds.current.length,
      });
    }

    updateStep(FormShipmentStepEnum.ShipmentIdentity);
  };

  const handleRowSelectionChange = (newSelection: GridRowSelectionModel) => {
    setSelectedBatches(newSelection);
  };

  const columns: GridColDef[] = [
    {
      field: 'batchlot',
      headerName: commonTranslation('receiving-batch/lot'),
      width: 250,
      renderHeader: () => <Typography sx={{ fontWeight: 'bold' }}>{shipmentT('batch_lot')}</Typography>,
      sortable: false,
    },
    {
      field: 'name',
      headerName: shipmentT('batch_name'),
      width: 300,
      renderHeader: () => <Typography sx={{ fontWeight: 'bold' }}>{shipmentT('farm_name')}</Typography>,
      sortable: false,
    },
    {
      field: 'recordBy',
      headerName: receiveTranslation('recorded-by'),
      width: 150,
      renderCell: renderRecordBy,
      sortable: false,
    },
    {
      field: 'farmName',
      headerName: shipmentT('farm_name'),
      width: 250,
      renderHeader: () => <Typography sx={{ fontWeight: 'bold' }}>{shipmentT('farm_name')}</Typography>,
      sortable: false,
      valueGetter: (_, row) => {
        return row?.farm?.name || '';
      },
    },
    {
      field: 'totalVarietiesWeight',
      headerName: `${shipmentT('total_weight')} (${commonTranslation('kg')})`,
      width: 200,
      align: 'left',
      valueFormatter: (value) => {
        return formatNumberWithCommas(value) || DEFAULT_VALUE;
      },
      sortable: true,
    },
    {
      field: 'userUpdated',
      headerName: receiveT('modified-by'),
      width: 150,
      renderCell: renderUpdatedBy,
      sortable: true,
    },
    {
      field: 'dateUpdated',
      headerName: receiveT('modified-on'),
      width: 168,
      renderCell: renderUpdatedOn,
      sortable: true,
    },
    {
      field: 'dateCreated',
      headerName: receiveT('receiving-date'),
      minWidth: 180,
      flex: 1,
      renderHeader: () => (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography sx={{ fontWeight: 'bold' }}>{shipmentT('recorded_on')}</Typography>
        </Box>
      ),
      renderCell: renderRecordOn,
      sortable: true,
    },
  ];

  const breadcrumbs = [
    {
      label: commonTranslation('shipment'),
      href: clientRoutes.shipment,
    },
    {
      label: commonTranslation('create-shipment-title'),
      href: clientRoutes.createShipment,
    },
  ];

  const customTableHeight = useMemo<number>(() => {
    if (deviceHeight) {
      const paddingHeight = 300;

      return deviceHeight - headerHeight - paddingHeight;
    }

    return 600;
  }, [deviceHeight]);

  return (
    <>
      <Box
        component="div"
        id="create-shipment-select-product"
        sx={{ boxSizing: 'border-box', display: 'flex', width: '100%', flex: 1, flexDirection: 'column', p: '20px' }}
      >
        <Breadcrumbs items={breadcrumbs} />

        <Box sx={{ display: 'flex', width: '100%', justifyContent: 'space-between' }} my="16px">
          <Typography variant="h6" fontWeight="600">
            {commonTranslation('create-shipment-title')}
          </Typography>

          <Box sx={{ display: 'flex', gap: '12px' }}>
            <Button
              variant="outlined"
              onClick={() => {
                setShowWarningDialog(true);
              }}
              sx={{
                width: '200px',
              }}
            >
              {shipmentT('discard')}
            </Button>
            <Button
              disabled={selectedBatches.ids.size < 1}
              variant="contained"
              onClick={handleSubmit}
              sx={{
                width: '200px',
              }}
            >
              {shipmentT('continue')}
            </Button>
          </Box>
        </Box>

        <Typography
          variant="caption"
          sx={{
            borderRadius: '8px',
            backgroundColor: theme.palette.customColors.primary100,
            p: '8px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            mb: '16px',
            boxShadow: 1,
          }}
        >
          <ErrorOutlineIcon sx={{ color: theme.palette.customColors.primary }} />
          {shipmentT('select-product-placeholder')}
        </Typography>

        <EventDataTable
          hasFilterUserRole
          rowSelectionModel={selectedBatches}
          tableHeight={customTableHeight}
          checkboxSelection
          columns={columns}
          eventType={'receiving'}
          queryKey={'create-shipment-receiving-event-data'}
          onRowSelectionModelChange={handleRowSelectionChange}
          maxRowSelection={MAX_SELECTIONS}
          defaultSortModel={[
            {
              field: 'dateCreated',
              sort: 'desc',
            },
          ]}
        />
      </Box>
      <PromptDialog
        open={showPromptMessage}
        onConfirm={onConfirm}
        onClose={() => {
          setShowPromptMessage(false);
        }}
        title={confirmBatchLotChange('title')}
        content={confirmBatchLotChange('description')}
        confirmText={commonT('confirm')}
      />
    </>
  );
};
