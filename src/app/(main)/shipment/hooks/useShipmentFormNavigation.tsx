import { UseFormReturn } from "react-hook-form";
import { DurianInformationForm, FormShipmentStepEnum, Receipt, ShipmentIdentityType } from "types";
import { checkAllMatchedQrLabel } from "../_util/util";

type UseShipmentFormNavigationProps = {
  formStep: FormShipmentStepEnum;
  updateStep: (nextStep: number) => void;
  informationFormValues: DurianInformationForm[];
  onSubmitShipment: () => Promise<void>;
  generateQrCodeBatchLot: () => Promise<void>;
  ENABLED_SHIPMENT_MAP_QR: boolean;
  setWarningNotMatchQrLabels: (labels: string[]) => void;
  setWarningNotMatchDialog: (open: boolean) => void;
  setLoadingForm: (loading: boolean) => void;
  receiptFormOptions: {
    trigger: () => Promise<boolean>;
    getValues: () => Receipt;
  };
  handleSubmitReceiptForm: (data: Receipt) => void;
  shipmentForm: UseFormReturn<ShipmentIdentityType, unknown, ShipmentIdentityType>;
  updateShipmentIdentity: (value: string) => void;
};

export const useShipmentFormNavigation = ({
  formStep,
  updateStep,
  informationFormValues,
  onSubmitShipment,
  generateQrCodeBatchLot,
  ENABLED_SHIPMENT_MAP_QR,
  setWarningNotMatchQrLabels,
  setWarningNotMatchDialog,
  setLoadingForm,
  receiptFormOptions,
  handleSubmitReceiptForm,
  shipmentForm,
  updateShipmentIdentity,
}: UseShipmentFormNavigationProps) => {
  const onNext = async () => {

    switch (formStep) {
      case FormShipmentStepEnum.QrReviewStep: {
        const matchedQrLabels = checkAllMatchedQrLabel(informationFormValues);
        const isMatched = matchedQrLabels.every((it) => it.value);

        if (!isMatched && ENABLED_SHIPMENT_MAP_QR) {
          const notMatchedLabels = matchedQrLabels.map((it) => it.field);
          setWarningNotMatchQrLabels(notMatchedLabels);
          setWarningNotMatchDialog(true);
          return;
        } else {
          setWarningNotMatchQrLabels([]);
        }

        await onSubmitShipment();
        break;
      }

      case FormShipmentStepEnum.UploadDocumentStep: {
        setLoadingForm(true);

        if (!ENABLED_SHIPMENT_MAP_QR) {
          await generateQrCodeBatchLot();
        }

        setLoadingForm(false);
        break;
      }

      case FormShipmentStepEnum.ReceiptStep: {
        const { trigger, getValues } = receiptFormOptions;
        const isValid = await trigger();

        if (!isValid) return;
        handleSubmitReceiptForm(getValues());
        break;
      }

      case FormShipmentStepEnum.ShipmentIdentity: {
        const isValid = await shipmentForm.trigger();
        if (!isValid) return;
        updateShipmentIdentity(shipmentForm.getValues().name ?? '');
        break;
      }

      default:
        break;
    }

    updateStep(formStep + 1);
  };

  return { onNext };
};
