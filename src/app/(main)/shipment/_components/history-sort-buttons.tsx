'use client';

import { But<PERSON>, Box } from '@mui/material';
import { useTranslations } from 'next-intl';
import { FC } from 'react';
import { SortOrder } from 'types/shipment-history';
import { theme } from 'styles/theme';
import { VerticalAlignBottomOutlined, VerticalAlignTopOutlined } from '@mui/icons-material';

interface HistorySortButtonsProps {
  sortOrder: SortOrder;
  onSortChange: (order: SortOrder) => void;
}

export const HistorySortButtons: FC<HistorySortButtonsProps> = ({ sortOrder, onSortChange }) => {
  const shipmentT = useTranslations('shipment');


  const newestFirstLabel = shipmentT('history-newest-first');
  const oldestFirstLabel = shipmentT('history-oldest-first');

  const buttonStyle = {
    borderRadius: '4px',
    padding: '8px 16px',
    fontWeight: 600,
    textTransform: 'none' as const,
    minWidth: '120px',
    height: '40px',
  };

  const activeButtonStyle = {
    ...buttonStyle,
    color: theme.palette.customColors.primary,
    backgroundColor: theme.palette.customColors.blueTint,
    border: `1px solid ${theme.palette.customColors.primary}`,
    '&:hover': {
      backgroundColor: theme.palette.customColors.blue50,
    },
  };

  const getButtonConfig = () => {
    if (sortOrder === 'oldest') {
      return {
        label: oldestFirstLabel,
        icon: <VerticalAlignTopOutlined fontSize="small" />,
        nextSort: 'newest' as SortOrder,
      };
    } else {
      return {
        label: newestFirstLabel,
        icon: <VerticalAlignBottomOutlined fontSize="small" />,
        nextSort: 'oldest' as SortOrder,
      };
    }
  };

  const buttonConfig = getButtonConfig();

  return (
    <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
      <Button sx={activeButtonStyle} onClick={() => onSortChange(buttonConfig.nextSort)} startIcon={buttonConfig.icon}>
        {buttonConfig.label}
      </Button>
    </Box>
  );
};
