'use client';

import { Box, Typography } from '@mui/material';
import ImageIcon from '@mui/icons-material/Image';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import { useTranslations } from 'next-intl';
import { FC } from 'react';
import { theme } from 'styles/theme';
import { ShipmentHistoryChange } from 'types/shipment-history';

interface AdditionalDocumentChangeProps {
  changes: ShipmentHistoryChange[];
}

export const AdditionalDocumentChange: FC<AdditionalDocumentChangeProps> = ({ changes }) => {
  const t = useTranslations('shipment');

  const removedChanges = changes.filter((c) => c.changeType === 'removed');
  const addedChanges = changes.filter((c) => c.changeType === 'added');

  const getDocumentIcon = (filename: string) => {
    const iconStyle = {
      fontSize: 16,
      opacity: 1,
    };

    const safeFilename = filename || '';
    if (safeFilename.toLowerCase().includes('.pdf')) {
      return <PictureAsPdfIcon sx={{ ...iconStyle, color: '#f44336' }} />; // Red for PDF
    } else if (safeFilename.toLowerCase().includes('.png')) {
      return <ImageIcon sx={{ ...iconStyle, color: '#9C27B0' }} />; // Purple for PNG
    } else if (safeFilename.toLowerCase().includes('.jpg') || safeFilename.toLowerCase().includes('.jpeg')) {
      return <ImageIcon sx={{ ...iconStyle, color: '#2196F3' }} />; // Blue for JPG
    }
    return <ImageIcon sx={{ ...iconStyle, color: theme.palette.customColors.gray5 }} />;
  };

  const renderDocumentItem = (filename: string | null) => (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
      {getDocumentIcon(filename || '')}
      <Typography
        variant="body2"
        color="text.secondary"
        sx={{
          fontSize: '14px',
        }}
      >
        {filename || ''}
      </Typography>
    </Box>
  );

  return (
    <Box sx={{ background: theme.palette.customColors.neutral50, borderRadius: 1, px: 2, py: 1 }}>
      <Typography variant="subtitle2" fontWeight="bold" sx={{ mb: 1, fontSize: '14px' }}>
        {t('history-field-additional-documents')}
      </Typography>

      {removedChanges.length > 0 && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1, fontSize: '14px' }}>
            {t('history-value-removed')}
          </Typography>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: 1,
              background: theme.palette.customColors.neutral100,
              borderRadius: 1,
              px: 2,
              py: 1,
            }}
          >
            {removedChanges.map((change) => (
              <Box
                key={`${change.field}-${change.changeType}-${typeof change.oldValue === 'string' ? change.oldValue : ''}`}
              >
                {renderDocumentItem(typeof change.oldValue === 'string' ? change.oldValue : null)}
              </Box>
            ))}
          </Box>
        </Box>
      )}

      {addedChanges.length > 0 && (
        <Box>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1, fontSize: '14px' }}>
            {t('history-value-added')}
          </Typography>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: 1,
              background: theme.palette.customColors.neutral100,
              borderRadius: 1,
              px: 2,
              py: 1,
            }}
          >
            {addedChanges.map((change) => (
              <Box
                key={`${change.field}-${change.changeType}-${typeof change.newValue === 'string' ? change.newValue : ''}`}
              >
                {renderDocumentItem(typeof change.newValue === 'string' ? change.newValue : null)}
              </Box>
            ))}
          </Box>
        </Box>
      )}
    </Box>
  );
};
