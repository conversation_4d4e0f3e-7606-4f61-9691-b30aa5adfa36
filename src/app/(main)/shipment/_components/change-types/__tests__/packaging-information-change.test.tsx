import { render, screen } from '@testing-library/react';
import { PackagingInformationChange } from '../packaging-information-change';

// Mock the PackagingDurianBox component
jest.mock('../../packaging-durian-box', () => ({
  PackagingDurianBox: ({ brandName, variety, grade, typeOfBox, netWeight, totalBox, packingDate }: {
    brandName: string;
    variety: string;
    grade: string;
    typeOfBox: string;
    netWeight: string;
    totalBox: string;
    packingDate?: string;
  }) => (
    <div data-testid="packaging-durian-box">
      <div>{String(brandName || '')}</div>
      <div>{String(variety || '')}</div>
      <div>{String(grade || '')}</div>
      <div>{String(typeOfBox || '')}</div>
      <div>{String(netWeight || '')}</div>
      <div>{String(totalBox || '')}</div>
      {packingDate && <div>{String(packingDate)}</div>}
    </div>
  ),
}));

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: jest.fn(() => (key: string) => {
    const translations: Record<string, string> = {
      'history-field-packaging-information': 'Packaging information',
      'history-value-added': 'Added',
      'history-value-removed': 'Removed',
    };
    return translations[key] || key;
  }),
}));

// Mock formatDateWithLocale function
jest.mock('containers/event/_components', () => ({
  formatDateWithLocale: jest.fn(() => '15/01/2025'),
}));

describe('PackagingInformationChange', () => {
  const mockChanges = [
    {
      field: 'packagingInformation',
      fieldLabel: 'Packaging information',
      oldValue: {
        id: 'pkg-old',
        brandNameInfo: {
          id: 'brand-old',
          label: { en: 'Old Brand', th: 'แบรนด์เก่า' },
        },
        productTypeInfo: {
          id: 'type-old',
          label: { en: 'Old Type', th: 'ประเภทเก่า' },
        },
        varietyGradeJoinId: 1,
        weightKg: 8,
        quantity: '30',
        numberOfBoxes: 30,
        sealNumber: 'SEAL-OLD',
        varietyGrade: {
          gradeValue: 'B',
          gradeDisplayText: 'Grade B',
          varietyValue: 'old-variety',
          varietyDisplayText: 'Old Durian',
        },
        varieties: [],
        batchlot: 'BATCH-OLD',
        varietyName: 'Old Durian',
        packingDate: 1705276800000,
      },
      newValue: null,
      changeType: 'removed' as const,
    },
    {
      field: 'packagingInformation',
      fieldLabel: 'Packaging information',
      oldValue: null,
      newValue: {
        id: 'pkg-new',
        brandNameInfo: {
          id: 'brand-new',
          label: { en: 'King Durian Brand', th: 'แบรนด์ทุเรียนคิง' },
        },
        productTypeInfo: {
          id: 'type-new',
          label: { en: 'Fresh Durian', th: 'ทุเรียนสด' },
        },
        varietyGradeJoinId: 2,
        weightKg: 9,
        quantity: '40',
        numberOfBoxes: 40,
        sealNumber: 'SEAL-NEW',
        varietyGrade: {
          gradeValue: 'AB',
          gradeDisplayText: 'Grade AB',
          varietyValue: 'king-durian',
          varietyDisplayText: 'King Durian',
        },
        varieties: [],
        batchlot: 'BATCH-NEW',
        varietyName: 'King Durian',
        packingDate: 1705363200000,
      },
      changeType: 'added' as const,
    },
  ];

  it('renders null when no changes provided', () => {
    const { container } = render(<PackagingInformationChange />);
    expect(container.firstChild).toBeNull();
  });

  it('renders null when empty changes array provided', () => {
    const { container } = render(<PackagingInformationChange changes={[]} />);
    expect(container.firstChild).toBeNull();
  });

  it('renders changes-based format with added and removed sections', () => {
    render(<PackagingInformationChange changes={mockChanges} />);

    expect(screen.getAllByText('Packaging information')).toHaveLength(2);
    expect(screen.getByText('Removed')).toBeInTheDocument();
    expect(screen.getByText('Added')).toBeInTheDocument();
    expect(screen.getAllByTestId('packaging-durian-box')).toHaveLength(2);
  });

  it('handles only removed packaging changes', () => {
    const removedOnlyChanges = [
      {
        field: 'packagingInformation',
        oldValue: {
          id: 'pkg-removed',
          brandNameInfo: {
            id: 'brand-removed',
            label: { en: 'Removed Brand', th: 'แบรนด์ที่ถูกลบ' },
          },
          productTypeInfo: {
            id: 'type-removed',
            label: { en: 'Removed Type', th: 'ประเภทที่ถูกลบ' },
          },
          varietyGradeJoinId: 1,
          weightKg: 8,
          quantity: '30',
          numberOfBoxes: 30,
          sealNumber: 'SEAL-REMOVED',
          varietyGrade: {
            gradeValue: 'B',
            gradeDisplayText: 'Grade B',
            varietyValue: 'removed-variety',
            varietyDisplayText: 'Removed Variety',
          },
          varieties: [],
          batchlot: 'BATCH-REMOVED',
          varietyName: 'Removed Variety',
          packingDate: 1705276800000,
        },
        newValue: null,
        changeType: 'removed' as const,
      },
    ];

    render(<PackagingInformationChange changes={removedOnlyChanges} />);

    expect(screen.getByText('Removed')).toBeInTheDocument();
    expect(screen.getByTestId('packaging-durian-box')).toBeInTheDocument();
    expect(screen.queryByText('Added')).not.toBeInTheDocument();
  });

  it('handles only added packaging changes', () => {
    const addedOnlyChanges = [
      {
        field: 'packagingInformation',
        oldValue: null,
        newValue: {
          id: 'pkg-added',
          brandNameInfo: {
            id: 'brand-added',
            label: { en: 'Added Brand', th: 'แบรนด์ที่เพิ่ม' },
          },
          productTypeInfo: {
            id: 'type-added',
            label: { en: 'Added Type', th: 'ประเภทที่เพิ่ม' },
          },
          varietyGradeJoinId: 1,
          weightKg: 9,
          quantity: '40',
          numberOfBoxes: 40,
          sealNumber: 'SEAL-ADDED',
          varietyGrade: {
            gradeValue: 'AB',
            gradeDisplayText: 'Grade AB',
            varietyValue: 'added-variety',
            varietyDisplayText: 'Added Variety',
          },
          varieties: [],
          batchlot: 'BATCH-ADDED',
          varietyName: 'Added Variety',
          packingDate: 1705363200000,
        },
        changeType: 'added' as const,
      },
    ];

    render(<PackagingInformationChange changes={addedOnlyChanges} />);

    expect(screen.getByText('Added')).toBeInTheDocument();
    expect(screen.getByTestId('packaging-durian-box')).toBeInTheDocument();
    expect(screen.queryByText('Removed')).not.toBeInTheDocument();
  });

  it('applies proper styling for removed packaging items', () => {
    const removedChanges = [
      {
        field: 'packagingInformation',
        oldValue: {
          id: 'pkg-styled',
          brandNameInfo: {
            id: 'brand-styled',
            label: { en: 'Styled Brand', th: 'แบรนด์ที่มีสไตล์' },
          },
          productTypeInfo: {
            id: 'type-styled',
            label: { en: 'Styled Type', th: 'ประเภทที่มีสไตล์' },
          },
          varietyGradeJoinId: 1,
          weightKg: 8,
          quantity: '30',
          numberOfBoxes: 30,
          sealNumber: 'SEAL-STYLED',
          varietyGrade: {
            gradeValue: 'B',
            gradeDisplayText: 'Grade B',
            varietyValue: 'styled-variety',
            varietyDisplayText: 'Styled Variety',
          },
          varieties: [],
          batchlot: 'BATCH-STYLED',
          varietyName: 'Styled Variety',
          packingDate: 1705276800000,
        },
        newValue: null,
        changeType: 'removed' as const,
      },
    ];

    render(<PackagingInformationChange changes={removedChanges} />);

    // Check that the packaging box is rendered for removed items
    expect(screen.getByTestId('packaging-durian-box')).toBeInTheDocument();
  });

  it('handles null packaging values gracefully', () => {
    const nullValueChanges = [
      {
        field: 'packagingInformation',
        oldValue: null,
        newValue: null,
        changeType: 'updated' as const,
      },
    ];

    render(<PackagingInformationChange changes={nullValueChanges} />);

    // Should render container but no packaging boxes when both values are null
    expect(screen.queryByTestId('packaging-durian-box')).not.toBeInTheDocument();
  });

  it('handles missing variety information gracefully', () => {
    const incompleteChanges = [
      {
        field: 'packagingInformation',
        oldValue: null,
        newValue: {
          id: 'pkg-incomplete',
          brandNameInfo: {
            id: 'brand-incomplete',
            label: { en: 'Incomplete Brand', th: 'แบรนด์ไม่สมบูรณ์' },
          },
          productTypeInfo: {
            id: 'type-incomplete',
            label: { en: 'Incomplete Type', th: 'ประเภทไม่สมบูรณ์' },
          },
          varietyGradeJoinId: 1,
          weightKg: 8,
          quantity: '30',
          numberOfBoxes: 30,
          sealNumber: 'SEAL-INCOMPLETE',
          varietyGrade: {
            gradeValue: '',
            gradeDisplayText: '',
            varietyValue: '',
            varietyDisplayText: '',
          },
          varieties: [],
          batchlot: 'BATCH-INCOMPLETE',
          varietyName: '', // Empty variety name
          packingDate: null, // No packing date
        },
        changeType: 'added' as const,
      },
    ];

    render(<PackagingInformationChange changes={incompleteChanges} />);

    expect(screen.getByText('Added')).toBeInTheDocument();
    expect(screen.getByTestId('packaging-durian-box')).toBeInTheDocument();
  });
});
