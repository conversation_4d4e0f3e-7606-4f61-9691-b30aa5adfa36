import { render, screen } from '@testing-library/react';
import { NormalFieldChange } from '../normal-field-change';
import { ShipmentHistoryChange } from 'types/shipment-history';

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: jest.fn((namespace: string) => (key: string) => {
    const shipmentTranslations: Record<string, string> = {
      'shipment-name': 'Shipment name',
      'history-value-none': 'None',
    };

    const ocrTranslations: Record<string, string> = {
      'receipt-number-input': 'Receipt number',
      'destination-country': 'Destination country',
      'transport-mode': 'Transportation mode',
      'border-checkpoint-name': 'Border checkpoint name',
      'total-weight': 'Total weight',
      'number-of-boxes': 'Number of boxes',
      'export-date': 'Export date',
      'exporting-company-name': 'Exporting company name',
      'container-number': 'Container number',
      'truck-number': 'Truck number',
      'trailer-number': 'Trailer number',
      'orchard-no': 'Orchard number',
    };

    if (namespace === 'shipment') {
      return shipmentTranslations[key] || key;
    } else if (namespace === 'ocr') {
      return ocrTranslations[key] || key;
    }
    return key;
  }),
}));

describe('NormalFieldChange', () => {
  const mockShipmentNameChanges: ShipmentHistoryChange[] = [
    {
      field: 'shipmentName',
      oldValue: '5000 kg durian to Shanghai China',
      newValue: '4800 kg durian to China',
      changeType: 'updated',
    },
  ];

  const mockDestinationCountryChanges: ShipmentHistoryChange[] = [
    {
      field: 'destinationCountry',
      oldValue: 'China',
      newValue: 'China, Vietnam, Laos',
      changeType: 'updated',
    },
  ];


  it('renders shipment name changes correctly', () => {
    render(<NormalFieldChange fieldName="shipmentName" changes={mockShipmentNameChanges} />);
    
    expect(screen.getByText('Shipment name')).toBeInTheDocument();
    expect(screen.getByText('5000 kg durian to Shanghai China')).toBeInTheDocument();
    expect(screen.getByText('→')).toBeInTheDocument();
    expect(screen.getByText('4800 kg durian to China')).toBeInTheDocument();
  });

  it('renders destination country changes correctly', () => {
    render(<NormalFieldChange fieldName="destinationCountry" changes={mockDestinationCountryChanges} />);
    
    expect(screen.getByText('Destination country')).toBeInTheDocument();
    expect(screen.getByText('China')).toBeInTheDocument();
    expect(screen.getByText('→')).toBeInTheDocument();
    expect(screen.getByText('China, Vietnam, Laos')).toBeInTheDocument();
  });

  it('handles empty values with "None" fallback', () => {
    const changesWithEmptyValues: ShipmentHistoryChange[] = [
      {
        field: 'shipmentName',
        oldValue: '',
        newValue: 'New shipment name',
        changeType: 'updated',
      },
    ];

    render(<NormalFieldChange fieldName="shipmentName" changes={changesWithEmptyValues} />);
    
    expect(screen.getByText('None')).toBeInTheDocument();
    expect(screen.getByText('New shipment name')).toBeInTheDocument();
  });

  it('renders default field changes for unknown field types', () => {
    const unknownFieldChanges: ShipmentHistoryChange[] = [
      {
        field: 'unknownField',
        oldValue: 'old value',
        newValue: 'new value',
        changeType: 'updated',
      },
    ];

    render(<NormalFieldChange fieldName="unknownField" changes={unknownFieldChanges} />);

    expect(screen.getByText('unknownField')).toBeInTheDocument();
    expect(screen.getByText('old value')).toBeInTheDocument();
    expect(screen.getByText('→')).toBeInTheDocument();
    expect(screen.getByText('new value')).toBeInTheDocument();
  });

  it('returns null when no matching change type is found', () => {
    const { container } = render(<NormalFieldChange fieldName="shipmentName" changes={[]} />);
    expect(container.firstChild).toBeNull();
  });

  it('returns null when changes array is undefined', () => {
    const { container } = render(<NormalFieldChange fieldName="shipmentName" changes={undefined as unknown as ShipmentHistoryChange[]} />);
    expect(container.firstChild).toBeNull();
  });

  it('handles null values correctly', () => {
    const changesWithNullValues: ShipmentHistoryChange[] = [
      {
        field: 'shipmentName',
        oldValue: null,
        newValue: 'New shipment name',
        changeType: 'updated',
      },
    ];

    render(<NormalFieldChange fieldName="shipmentName" changes={changesWithNullValues} />);

    expect(screen.getByText('None')).toBeInTheDocument();
    expect(screen.getByText('New shipment name')).toBeInTheDocument();
  });

  it('handles object values by rendering None', () => {
    const mockPackaging = {
      id: 'pkg-001',
      brandNameInfo: { id: 'brand-001', label: { en: 'Test Brand', th: 'แบรนด์ทดสอบ' } },
      productTypeInfo: { id: 'type-001', label: { en: 'Test Type', th: 'ประเภททดสอบ' } },
      varietyGradeJoinId: 1,
      weightKg: 8,
      quantity: '30',
      numberOfBoxes: 30,
      sealNumber: 'SEAL-001',
      varietyGrade: {
        gradeValue: 'A',
        gradeDisplayText: 'Grade A',
        varietyValue: 'test-variety',
        varietyDisplayText: 'Test Variety',
      },
      varieties: [],
      batchlot: 'BATCH-001',
      varietyName: 'Test Variety',
      packingDate: 1705276800000,
    };

    const changesWithObjectValues: ShipmentHistoryChange[] = [
      {
        field: 'packagingInformation',
        oldValue: mockPackaging,
        newValue: 'String value',
        changeType: 'updated',
      },
    ];

    render(<NormalFieldChange fieldName="packagingInformation" changes={changesWithObjectValues} />);

    expect(screen.getAllByText('None')).toHaveLength(1);
    expect(screen.getByText('String value')).toBeInTheDocument();
  });

  it('renders multiple changes correctly', () => {
    const multipleChanges: ShipmentHistoryChange[] = [
      {
        field: 'shipmentName',
        oldValue: 'Old name 1',
        newValue: 'New name 1',
        changeType: 'updated',
      },
      {
        field: 'shipmentName',
        oldValue: 'Old name 2',
        newValue: 'New name 2',
        changeType: 'updated',
      },
    ];

    render(<NormalFieldChange fieldName="shipmentName" changes={multipleChanges} />);

    expect(screen.getByText('Shipment name')).toBeInTheDocument();
    expect(screen.getByText('Old name 1')).toBeInTheDocument();
    expect(screen.getByText('New name 1')).toBeInTheDocument();
    expect(screen.getByText('Old name 2')).toBeInTheDocument();
    expect(screen.getByText('New name 2')).toBeInTheDocument();
    expect(screen.getAllByText('→')).toHaveLength(2);
  });

  it('renders all supported field types with correct labels', () => {
    const fieldTestCases = [
      { fieldName: 'receiptNumber', expectedLabel: 'Receipt number' },
      { fieldName: 'transportationMode', expectedLabel: 'Transportation mode' },
      { fieldName: 'borderCheckpointName', expectedLabel: 'Border checkpoint name' },
      { fieldName: 'totalWeightKg', expectedLabel: 'Total weight' },
      { fieldName: 'numberOfBoxes', expectedLabel: 'Number of boxes' },
      { fieldName: 'exportDate', expectedLabel: 'Export date' },
      { fieldName: 'nameOfExportingCompany', expectedLabel: 'Exporting company name' },
      { fieldName: 'containerNumber', expectedLabel: 'Container number' },
      { fieldName: 'truckNumber', expectedLabel: 'Truck number' },
      { fieldName: 'trailerNumber', expectedLabel: 'Trailer number' },
      { fieldName: 'orchardNo', expectedLabel: 'Orchard number' },
    ];

    fieldTestCases.forEach(({ fieldName, expectedLabel }) => {
      const changes: ShipmentHistoryChange[] = [
        {
          field: fieldName,
          oldValue: 'old value',
          newValue: 'new value',
          changeType: 'updated',
        },
      ];

      const { unmount } = render(<NormalFieldChange fieldName={fieldName} changes={changes} />);

      expect(screen.getByText(expectedLabel)).toBeInTheDocument();
      expect(screen.getByText('old value')).toBeInTheDocument();
      expect(screen.getByText('new value')).toBeInTheDocument();

      unmount();
    });
  });

  it('applies correct styling to the container', () => {
    render(<NormalFieldChange fieldName="shipmentName" changes={mockShipmentNameChanges} />);

    const container = screen.getByText('Shipment name').closest('.MuiBox-root');
    expect(container).toHaveStyle({
      'border-radius': '4px',
      'padding-left': '16px',
      'padding-right': '16px',
      'padding-top': '8px',
      'padding-bottom': '8px',
    });
  });

  it('applies correct styling to field label', () => {
    render(<NormalFieldChange fieldName="shipmentName" changes={mockShipmentNameChanges} />);

    const fieldLabel = screen.getByText('Shipment name');
    expect(fieldLabel).toHaveClass('MuiTypography-subtitle2');
  });

  it('applies correct styling to None values', () => {
    const changesWithEmptyValues: ShipmentHistoryChange[] = [
      {
        field: 'shipmentName',
        oldValue: null,
        newValue: 'New value',
        changeType: 'updated',
      },
    ];

    render(<NormalFieldChange fieldName="shipmentName" changes={changesWithEmptyValues} />);

    const noneElement = screen.getByText('None');
    expect(noneElement).toHaveClass('MuiTypography-body2');
    expect(noneElement).toHaveStyle({
      'font-size': '14px',
      'padding-left': '8px',
      'padding-right': '8px',
      'border-radius': '4px',
      'background': 'white',
    });
    // Check that border exists (without checking specific color)
    expect(noneElement).toHaveStyle('border-width: 1px');
  });
});
