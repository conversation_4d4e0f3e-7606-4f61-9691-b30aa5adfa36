'use client';

import { Box, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';
import { FC } from 'react';
import { theme } from 'styles/theme';
import { ShipmentHistoryChange, ShipmentHistoryPackaging } from 'types/shipment-history';

interface NormalFieldChangeProps {
  fieldName: string;
  changes: ShipmentHistoryChange[];
}

export const NormalFieldChange: FC<NormalFieldChangeProps> = ({ fieldName, changes }) => {
  const t = useTranslations('shipment');
  const ocrT = useTranslations('ocr');

  if (!changes || changes.length === 0) {
    return null;
  }

  const renderNoneValue = () => {
    return (
      <Typography
        variant="body2"
        color="text.secondary"
        sx={{
          fontSize: '14px',
          px: 1,
          borderRadius: 1,
          border: `1px solid ${theme.palette.customColors.neutralBorder}`,
          background: 'white',
        }}
      >
        {t('history-value-none')}
      </Typography>
    );
  };

  const renderValue = (value: string | ShipmentHistoryPackaging | null) => {
    if (!value) return renderNoneValue();

    if (typeof value === 'object') {
      return renderNoneValue();
    }

    return (
      <Typography variant="body2" color="text.secondary" sx={{ fontSize: '14px' }}>
        {value}
      </Typography>
    );
  };

  const getFieldLabel = (field: string) => {
    const fieldLabels: { [key: string]: string } = {
      shipmentName: t('shipment-name'),
      receiptNumber: ocrT('receipt-number-input'),
      destinationCountry: ocrT('destination-country'),
      transportationMode: ocrT('transport-mode'),
      borderCheckpointName: ocrT('border-checkpoint-name'),
      totalWeightKg: ocrT('total-weight'),
      numberOfBoxes: ocrT('number-of-boxes'),
      exportDate: ocrT('export-date'),
      nameOfExportingCompany: ocrT('exporting-company-name'),
      containerNumber: ocrT('container-number'),
      truckNumber: ocrT('truck-number'),
      trailerNumber: ocrT('trailer-number'),
      orchardNo: ocrT('orchard-no'),
    };
    return fieldLabels[field] || field;
  };

  return (
    <Box sx={{ background: theme.palette.customColors.neutral50, borderRadius: 1, px: 2, py: 1 }}>
      <Typography variant="subtitle2" fontWeight="bold" sx={{ mb: 1, fontSize: '14px' }}>
        {getFieldLabel(fieldName)}
      </Typography>
      {changes.map((change) => (
        <Box
          key={`${change.field}-${change.changeType}-${change.oldValue}-${change.newValue}`}
          sx={{ fontSize: '14px', display: 'flex', alignItems: 'center', gap: 1 }}
        >
          {renderValue(change.oldValue)} → {renderValue(change.newValue)}
        </Box>
      ))}
    </Box>
  );
};
