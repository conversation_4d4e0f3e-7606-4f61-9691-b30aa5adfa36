'use client';

import { Box, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';
import { FC } from 'react';
import { PackagingDurianBox } from '../packaging-durian-box';
import { theme } from 'styles/theme';
import { ShipmentHistoryChange, ShipmentHistoryPackaging } from 'types/shipment-history';
import { getCookieLocale } from 'utils/cookie-client';
import { formatNumberWithCommas } from 'utils';
import { formatDateWithLocale } from 'containers/event/_components';

type PackageDetail = ShipmentHistoryPackaging;

interface PackagingInformationChangeProps {
  changes?: ShipmentHistoryChange[];
}

export const PackagingInformationChange: FC<PackagingInformationChangeProps> = ({ changes }) => {
  const t = useTranslations('shipment');

  const parsePackagingValue = (value: string | PackageDetail | null): PackageDetail | null => {
    if (!value) return null;

    if (typeof value === 'object') {
      return value;
    }

    return null;
  };

  const renderPackagingItem = (packageDetail: PackageDetail | null) => {
    if (!packageDetail) return null;

    const locale = getCookieLocale() ?? 'th';

    const brandName =
      packageDetail.brandNameInfo.label[locale] || packageDetail.brandNameInfo.label.en || 'Unknown Brand';
    const variety = packageDetail.varietyName || packageDetail.varietyGrade?.varietyDisplayText || '';
    const grade = packageDetail.varietyGrade?.gradeDisplayText || '';
    const typeOfBox = packageDetail.productTypeInfo.label[locale] || packageDetail.productTypeInfo.label.en || '';
    const netWeight = formatNumberWithCommas(packageDetail.weightKg.toString());
    const totalBox = formatNumberWithCommas(packageDetail.numberOfBoxes.toString());
    const packingDate = packageDetail.packingDate ? formatDateWithLocale(packageDetail.packingDate) : undefined;

    return (
      <Box
        sx={{
          position: 'relative',
          mb: 1,
        }}
      >
        <PackagingDurianBox
          brandName={brandName}
          variety={variety}
          grade={grade}
          typeOfBox={typeOfBox}
          netWeight={netWeight}
          totalBox={totalBox}
          packingDate={packingDate}
        />
      </Box>
    );
  };

  const renderChangeSection = (
    sectionChanges: ShipmentHistoryChange[],
    changeType: 'removed' | 'added',
    title: string
  ) => {
    if (sectionChanges.length === 0) return null;

    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 1,
          background: theme.palette.customColors.neutral50,
          borderRadius: 1,
          px: 2,
          pt: 1,
        }}
      >
        <Typography fontWeight="bold" sx={{ fontSize: '14px' }}>
          {t('history-field-packaging-information')}
        </Typography>
        <Box>
          <Typography variant="body2" color="text.secondary" sx={{ fontSize: '14px' }}>
            {title}
          </Typography>
          {sectionChanges.map((change, index) => (
            <Box key={`${changeType}-${change.field}-${index}`}>
              {renderPackagingItem(parsePackagingValue(changeType === 'removed' ? change.oldValue : change.newValue))}
            </Box>
          ))}
        </Box>
      </Box>
    );
  };

  if (changes && changes.length > 0) {
    const removedChanges = changes.filter((c) => c.changeType === 'removed');
    const addedChanges = changes.filter((c) => c.changeType === 'added');

    return (
      <Box sx={{ display: 'flex', gap: 1, flexDirection: 'column' }}>
        {renderChangeSection(removedChanges, 'removed', t('history-value-removed'))}
        {renderChangeSection(addedChanges, 'added', t('history-value-added'))}
      </Box>
    );
  }

  return null;
};
