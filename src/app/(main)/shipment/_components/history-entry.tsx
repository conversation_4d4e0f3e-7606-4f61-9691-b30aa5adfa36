'use client';

import { Box, Typography, Avatar } from '@mui/material';
import { FC } from 'react';
import { ShipmentHistory, ShipmentHistoryChange } from 'types/shipment-history';
import { theme } from 'styles/theme';

import {
  NormalFieldChange,
  ShipmentPhotoChange,
  AdditionalDocumentChange,
  PackagingInformationChange,
} from './change-types';
import { formatDateTimeWithLocale } from 'containers/event/_components';
import { useTranslations } from 'next-intl';

interface HistoryEntryProps {
  entry: ShipmentHistory['entries'][0];
}

export const HistoryEntry: FC<HistoryEntryProps> = ({ entry }) => {
  const shipmentTranslation = useTranslations('shipment');

  const getUserInitials = (name: string) => {
    if (!name) return '';
    const nameParts = name.split(' ');
    if (nameParts.length >= 2) {
      return `${nameParts[0][0]}${nameParts[1][0]}`.toUpperCase();
    }
    return name.substring(0, 2).toUpperCase();
  };

  const getShipmentNameFromChanges = () => {
    if (!entry.changes) return '';

    const shipmentNameChange = entry.changes.find(
      (change) => change.field === 'shipmentName' && (change.changeType === 'created' || change.changeType === 'sealed')
    );

    return (shipmentNameChange?.newValue as string) || '';
  };

  const groupChangesByField = (changes: ShipmentHistoryChange[]) => {
    const grouped: { [key: string]: ShipmentHistoryChange[] } = {};
    changes.forEach((change) => {
      if (!grouped[change.field]) {
        grouped[change.field] = [];
      }
      grouped[change.field].push(change);
    });
    return grouped;
  };

  const renderChangeBlock = (fieldName: string, changes: ShipmentHistoryChange[]) => {
    switch (fieldName) {
      case 'shipmentPhotos':
        return <ShipmentPhotoChange changes={changes} />;
      case 'additionalDocuments':
        return <AdditionalDocumentChange changes={changes} />;
      case 'packagingInformation':
        return <PackagingInformationChange changes={changes} />;
      default:
        return <NormalFieldChange fieldName={fieldName} changes={changes} />;
    }
  };

  const renderActionWording = (action: 'created' | 'updated' | 'sealed') => {
    switch (action) {
      case 'created':
        return shipmentTranslation('history-action-created');
      case 'updated':
        return shipmentTranslation('history-action-updated');
      case 'sealed':
        return shipmentTranslation('history-action-sealed');
      default:
        return action;
    }
  };

  return (
    <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
      <Avatar
        src={entry.user?.avatar}
        sx={{
          width: 40,
          height: 40,
          backgroundColor: theme.palette.primary.main,
          fontSize: '14px',
          fontWeight: 'bold',
        }}
      >
        {getUserInitials(entry.user?.name || '')}
      </Avatar>

      <Box sx={{ flex: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
          <Typography variant="body2" color="text.primary" fontWeight={'bold'}>
            {entry.user?.name || ''}
          </Typography>
          <Typography variant="body2" color="text.primary">
            {renderActionWording(entry.action)}
          </Typography>
          {entry.action !== 'updated' && (
            <Typography variant="body2" fontWeight="bold">
              {getShipmentNameFromChanges() || ''}
            </Typography>
          )}
        </Box>

        <Typography variant="caption" color="text.secondary" sx={{ mb: 2, display: 'block' }}>
          {formatDateTimeWithLocale(entry.timestamp)}
        </Typography>
        {entry.action === 'updated' && (
          <Box sx={{ display: 'flex', gap: 1, flexDirection: 'column' }}>
            {entry.changes &&
              (() => {
                const groupedChanges = groupChangesByField(entry.changes);
                return Object.entries(groupedChanges).map(([fieldName, changes], index) => (
                  <div key={`${fieldName}-${index}`}>{renderChangeBlock(fieldName, changes)}</div>
                ));
              })()}
          </Box>
        )}
      </Box>
    </Box>
  );
};
