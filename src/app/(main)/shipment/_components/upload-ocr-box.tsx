'use client';

import BorderColorOutlinedIcon from '@mui/icons-material/BorderColorOutlined';
import CancelIcon from '@mui/icons-material/Cancel';
import FileUploadOutlinedIcon from '@mui/icons-material/FileUploadOutlined';
import { Box, Button, Fade, Stack, styled, Typography } from '@mui/material';
import uploadIcon from 'assets/icons/upload.svg';
import { CircularWithValueLabel } from 'components';
import { ACCEPT_UPLOAD_FILES } from 'constant/common';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useState } from 'react';
import { useCreateShipmentStore } from 'store/useCreateShipmentStore';
import { validateFile } from 'utils';
import { sendEvent } from 'utils/gtag';
import { capturePosthog } from 'utils/posthog';
import toastMessages from 'utils/toastMessages';
import { defaultReceiptFormValues } from '../_container/upload-receipt-content';

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: '100%',
  overflow: 'hidden',
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  whiteSpace: 'nowrap',
  width: '100%',
  cursor: 'pointer',
  opacity: 0,
});

export const UploadFileBox = () => {
  const shipmentT = useTranslations('shipment');
  const ocrT = useTranslations('ocr');
  const formT = useTranslations('form');

  const { updateOcrFile, isUploadOcrError, setIsUploadOcrError, updateOrcForm, manualInputOrc, setManualInput } =
    useCreateShipmentStore();
  const [isUploading, setIsUploading] = useState(false);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsUploadOcrError(false);
    const file = event.target.files?.[0];

    if (!file) return;

    const { isValid } = validateFile(file);

    if (!isValid) {
      toastMessages.error(formT('invalid-file'));
      event.target.value = '';
      return;
    }

    try {
      setIsUploading(true);
      await updateOcrFile(file);
      sendEvent('call_ePhyto_success');
    } catch {
      sendEvent('call_ePhyto_fail');
      capturePosthog('get_ephyto_failed', { imageUrl: file.name });
      setIsUploadOcrError(true);
    } finally {
      event.target.value = '';
      setIsUploading(false);
    }
  };

  return (
    <Box
      id="upload-ocr-box"
      component="div"
      sx={{
        width: '100%',
        alignItems: 'center',
        justifyContent: 'center',
        display: 'flex',
        flex: 1,
        flexDirection: 'column',
      }}
    >
      {!isUploadOcrError && !manualInputOrc && (
        <Fade in={true} style={{ width: '100%', height: '100%' }}>
          <Box
            component="div"
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: '4px',
              alignItems: 'center',
              width: '100%',
              height: '100%',
              justifyContent: 'center',
            }}
          >
            {isUploading && <CircularWithValueLabel title={shipmentT('upload-oca-progress')} />}
            {!isUploading && (
              <>
                <Image src={uploadIcon} width={80} height={80} alt="upload-icon" />
                <Typography variant="caption" fontWeight={600} textAlign="center">
                  {shipmentT('upload-description')}
                </Typography>
                <Typography variant="caption" color="text.secondary" textAlign="center">
                  {shipmentT('upload-placeholder')}
                </Typography>

                <Stack spacing={2} direction="row" mt={1}>
                  <Button
                    component="label"
                    role={undefined}
                    variant="outlined"
                    tabIndex={-1}
                    startIcon={<FileUploadOutlinedIcon />}
                    sx={{ width: '240px', position: 'relative' }}
                  >
                    {shipmentT('upload-btn-title')}
                    <VisuallyHiddenInput
                      accept={ACCEPT_UPLOAD_FILES.join(',')}
                      type="file"
                      onChange={handleFileUpload}
                    />
                  </Button>
                  <Button
                    startIcon={<BorderColorOutlinedIcon />}
                    variant="outlined"
                    sx={{ width: '240px' }}
                    onClick={() => {
                      updateOrcForm({ ...defaultReceiptFormValues });
                      setManualInput(true);
                    }}
                  >
                    {ocrT('manual-input')}
                  </Button>
                </Stack>
              </>
            )}
          </Box>
        </Fade>
      )}

      {!isUploading && isUploadOcrError && !manualInputOrc && (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            width: '100%',
            alignItems: 'center',
            justifyContent: 'flex-start',
            gap: '8px',
          }}
        >
          <CancelIcon sx={{ fontSize: '64px' }} color="error" />
          <Typography component="p" mt="8px">
            {ocrT.rich('ocr-upload-error', {
              linkDrawer: (chunk) => {
                return (
                  <Typography
                    onClick={() => {
                      updateOrcForm({ ...defaultReceiptFormValues });
                      setManualInput(true);
                    }}
                    sx={{ cursor: 'pointer' }}
                    component="span"
                    color="primary"
                  >
                    {chunk}
                  </Typography>
                );
              },
            })}
          </Typography>
          <Button
            component="label"
            variant="outlined"
            startIcon={<FileUploadOutlinedIcon />}
            sx={{ width: '280px', position: 'relative' }}
          >
            {ocrT('upload-other-document')}
            <VisuallyHiddenInput accept={ACCEPT_UPLOAD_FILES.join(',')} type="file" onChange={handleFileUpload} />
          </Button>
          <Button
            startIcon={<BorderColorOutlinedIcon />}
            variant="outlined"
            sx={{ width: '280px' }}
            onClick={() => {
              updateOrcForm({ ...defaultReceiptFormValues });
              setManualInput(true);
            }}
          >
            {ocrT('manual-input')}
          </Button>
        </Box>
      )}
    </Box>
  );
};
