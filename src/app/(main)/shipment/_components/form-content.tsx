import { Box, Fade } from '@mui/material';
import { FC, PropsWithChildren } from 'react';

export const FormContent: FC<PropsWithChildren & { active: boolean; includeDom?: boolean; minHeight?: number }> = ({
  children,
  active,
  minHeight = 0,
}) => {
  return (
    <Fade
      in={active}
      style={{
        zIndex: active ? 2 : 1,
        display: active ? 'flex' : 'none',
        flexDirection: 'column',
        minHeight: minHeight,
        height: 'fit-content',
      }}
      id="FormContent"
    >
      <Box
        component="div"
        sx={{
          width: '100%',
          height: 'fit-content',
          display: 'flex',
          alignItems: 'center',
          flexDirection: 'column',
        }}
      >
        {children}
      </Box>
    </Fade>
  );
};
