import * as React from 'react';
import Button from '@mui/material/Button';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { useTranslations } from 'next-intl';
import { FC, JSX } from 'react';
import { Typography } from '@mui/material';
import ReplayIcon from '@mui/icons-material/Replay';
import { SelectIcon } from 'assets/react-icons/select-icon';
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined';

export const QrCodeMenuItem: FC<{ icon: JSX.Element; label: string; isError?: boolean; onClick?: () => void }> = ({
  icon,
  label,
  isError,
  onClick,
}) => {
  return (
    <MenuItem sx={{ display: 'flex', flexDirection: 'row', gap: 1, alignItems: 'center' }} onClick={onClick}>
      {icon}
      <Typography variant="caption" color={isError ? 'error.main' : 'text.primary'}>
        {label}
      </Typography>
    </MenuItem>
  );
};

export const QrCodeMenuItems: FC<{
  disable?: boolean;
  removeQrCode?: () => void;
  autoGen?: () => Promise<void>;
  reselectQrCode?: () => void;
}> = ({ disable, removeQrCode, autoGen, reselectQrCode }) => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const shipmentT = useTranslations('shipment');

  const onRegenQrCode = () => {
    autoGen?.();
    handleClose();
  };

  const onReselectQrCode = () => {
    reselectQrCode?.();
    handleClose();
  };

  const onRemoveQrCode = () => {
    removeQrCode?.();
    handleClose();
  };

  return (
    <div>
      <Button
        onClick={handleClick}
        variant="outlined"
        size="small"
        color="primary"
        sx={{ width: '40px', padding: '4px', minWidth: 'unset !important' }}
        disabled={disable}
      >
        <MoreVertIcon fontSize="small" />
      </Button>
      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        slotProps={{
          list: {
            'aria-labelledby': 'basic-button',
          },
        }}
      >
        {autoGen && (
          <QrCodeMenuItem
            onClick={onRegenQrCode}
            icon={<ReplayIcon fontSize="small" />}
            label={shipmentT('qrcode-item-regen-label')}
          />
        )}
        <QrCodeMenuItem
          onClick={onReselectQrCode}
          icon={<SelectIcon fontSize="small" />}
          label={shipmentT('qrcode-item-reselect-label')}
        />
        <QrCodeMenuItem
          onClick={onRemoveQrCode}
          icon={<DeleteOutlineOutlinedIcon fontSize="small" color="error" />}
          isError
          label={shipmentT('qrcode-item-remove-label')}
        />
      </Menu>
    </div>
  );
};
