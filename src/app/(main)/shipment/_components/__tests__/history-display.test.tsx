import { render, screen } from '@testing-library/react';
import { useTranslations } from 'next-intl';
import { HistoryDisplay } from '../history-display';
import { ShipmentHistory } from 'types/shipment-history';

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: jest.fn(),
}));

// Mock child components
jest.mock('../history-entry', () => ({
  HistoryEntry: ({ entry }: { entry: ShipmentHistory['entries'][0] }) => (
    <div data-testid={`history-entry-${entry.id}`}>
      {entry.user.name} - {entry.action}
    </div>
  ),
}));

jest.mock('../history-error', () => ({
  HistoryError: ({ error, onRetry }: { error?: string; onRetry?: () => void }) => (
    <div data-testid="history-error">
      <span>{error || 'Error occurred'}</span>
      {onRetry && <button onClick={onRetry}>Retry</button>}
    </div>
  ),
}));

const mockUseTranslations = useTranslations as jest.MockedFunction<typeof useTranslations>;

describe('HistoryDisplay', () => {
  const mockShipmentT = jest.fn();
  const mockCommonT = jest.fn();

  const mockEntries: ShipmentHistory['entries'] = [
    {
      id: '1',
      timestamp: '2025-01-15T14:32:00Z',
      user: {
        id: 'user1',
        name: 'Ayahidi Srumputa',
      },
      action: 'updated',
    },
    {
      id: '2',
      timestamp: '2025-01-14T10:20:00Z',
      user: {
        id: 'user2',
        name: 'John Doe',
      },
      action: 'created',
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    mockShipmentT.mockImplementation((key: string) => {
      const translations: Record<string, string> = {
        'history-no-entries': 'No history entries found',
      };
      return translations[key] || key;
    });
    mockCommonT.mockImplementation((key: string) => {
      const translations: Record<string, string> = {
        'loading': 'Loading...',
        'data-not-found': 'No data found',
      };
      return translations[key] || key;
    });
    mockUseTranslations.mockImplementation((namespace?: string) => {
      const baseFn = namespace === 'shipment' ? mockShipmentT : mockCommonT;
      // Create a function with the required properties to match next-intl's translation function interface
      const translationFn = Object.assign(baseFn, {
        rich: jest.fn(),
        markup: jest.fn(),
        raw: jest.fn(),
        has: jest.fn(() => true),
      });
      return translationFn;
    });
  });



  it('renders error state', () => {
    const mockOnRetry = jest.fn();
    render(
      <HistoryDisplay
        entries={[]}
        sortOrder="newest"
        error="Failed to load"
        onRetry={mockOnRetry}
      />
    );

    expect(screen.getByTestId('history-error')).toBeInTheDocument();
    expect(screen.getByText('Failed to load')).toBeInTheDocument();
  });

  it('renders empty state when no entries', () => {
    render(
      <HistoryDisplay
        entries={[]}
        sortOrder="newest"
      />
    );

    expect(screen.getByText('No data found')).toBeInTheDocument();
    expect(screen.getByText('No history entries found')).toBeInTheDocument();
  });

  it('renders entries in newest first order', () => {
    render(
      <HistoryDisplay
        entries={mockEntries}
        sortOrder="newest"
      />
    );

    const entries = screen.getAllByTestId(/history-entry-/);
    expect(entries).toHaveLength(2);

    // First entry should be the newer one (id: 1)
    expect(entries[0]).toHaveAttribute('data-testid', 'history-entry-1');
    expect(entries[1]).toHaveAttribute('data-testid', 'history-entry-2');
  });

  it('renders entries in oldest first order', () => {
    render(
      <HistoryDisplay
        entries={mockEntries}
        sortOrder="oldest"
      />
    );

    const entries = screen.getAllByTestId(/history-entry-/);
    expect(entries).toHaveLength(2);

    // First entry should be the older one (id: 2)
    expect(entries[0]).toHaveAttribute('data-testid', 'history-entry-2');
    expect(entries[1]).toHaveAttribute('data-testid', 'history-entry-1');
  });

  it('renders all entries with proper content', () => {
    render(
      <HistoryDisplay
        entries={mockEntries}
        sortOrder="newest"
      />
    );

    expect(screen.getByText('Ayahidi Srumputa - updated')).toBeInTheDocument();
    expect(screen.getByText('John Doe - created')).toBeInTheDocument();
  });

  it('uses correct translation namespaces', () => {
    render(
      <HistoryDisplay
        entries={[]}
        sortOrder="newest"
      />
    );

    expect(mockUseTranslations).toHaveBeenCalledWith('shipment');
    expect(mockUseTranslations).toHaveBeenCalledWith('common');
  });

  it('handles single entry correctly', () => {
    const singleEntry = [mockEntries[0]];

    render(
      <HistoryDisplay
        entries={singleEntry}
        sortOrder="newest"
      />
    );

    const entries = screen.getAllByTestId(/history-entry-/);
    expect(entries).toHaveLength(1);
    expect(screen.getByText('Ayahidi Srumputa - updated')).toBeInTheDocument();
  });

  it('renders with proper container styling', () => {
    const { container } = render(
      <HistoryDisplay
        entries={mockEntries}
        sortOrder="newest"
      />
    );

    const historyContainer = container.firstChild as HTMLElement;
    expect(historyContainer).toHaveStyle({
      backgroundColor: '#FFFFFF',
      borderRadius: '8px',
      border: '1px solid #e5e5ea',
    });
  });
});
