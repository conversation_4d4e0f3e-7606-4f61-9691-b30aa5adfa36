import { createServerEventService } from 'app/(main)/_util/api-server';
import camelcaseKeys from 'camelcase-keys';
import { NextPage } from 'next';
import { redirect } from 'next/navigation';
import { basePathProd } from 'configs/app-config';
import ClientWrapper from 'app/(main)/event/_component/client-wrapper';

type IndexProps = {
  params: Promise<{
    id: string;
  }>;
};

const Index: NextPage<IndexProps> = async ({ params }) => {
  const { id } = await params;
  const api = await createServerEventService();

  try {
    const { data: response } = await api.get(`/v1/search/events/packing-house/${id}`);
    if (!response.data) {
      return redirect(`${basePathProd}/not-found`);
    }
    return <ClientWrapper id={id} eventDetail={camelcaseKeys(response.data, { deep: true })} isCreate={true} />;
  } catch (error) {
    if (error instanceof Error && error.message.includes('404')) {
      return redirect(`${basePathProd}/not-found`);
    }
    throw error;
  }
};

export default Index;
