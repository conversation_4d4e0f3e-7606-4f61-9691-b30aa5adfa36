import { createServerEventService } from 'app/(main)/_util/api-server';
import camelcaseKeys from 'camelcase-keys';
import { getCookieServer } from 'configs/cookie';
import { NextPage } from 'next';
import { redirect } from 'next/navigation';
import ClientWrapper from '../../_component/client-wrapper';
import { basePathProd } from 'configs/app-config';

type IndexProps = {
  params: Promise<{
    id: string;
  }>;
};

const Index: NextPage<IndexProps> = async ({ params }) => {
  const accessToken = await getCookieServer<string>('access_token_pkg_house');
  const { id } = await params;
  const headers: Record<string, string> = {};
  headers['content-type'] = 'application/json';

  if (accessToken) {
    headers['authorization'] = `Bearer ${accessToken}`;
  }

  const api = await createServerEventService();

  try {
    const { data: response } = await api.get(`/v1/search/events/packing-house/${id}`);
    if (!response.data) {
      return redirect(`${basePathProd}/not-found`);
    }
    return <ClientWrapper isReceiving id={id} eventDetail={camelcaseKeys(response.data, { deep: true })} />;
  } catch (error) {
    if (error instanceof Error && error.message.includes('404')) {
      return redirect(`${basePathProd}/not-found`);
    }
    throw error;
  }
};

export default Index;
