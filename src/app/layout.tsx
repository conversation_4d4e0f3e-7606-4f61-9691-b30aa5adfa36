import type { Metadata, Viewport } from 'next';
import { NextIntlClientProvider } from 'next-intl';
import { getLocale, getMessages } from 'next-intl/server';
import { Poppins } from 'next/font/google';

import { getCookieServer } from 'configs/cookie';
import { ProviderLayout } from 'layouts/main/provider-layout';
import Script from 'next/script';
import { getPublicEnv } from 'utils/getEnv';
import { PostHogProvider } from '../lib/posthog-providers';
import './globals.css';
import GtagSetup from 'gtag-setup';

const fontFamily = Poppins({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600'],
});

export const metadata: Metadata = {
  metadataBase: process.env.NEXT_PUBLIC_HOST ? new URL(process.env.NEXT_PUBLIC_HOST) : new URL('https://example.com'),
  title: 'ProPass: ระบบตรวจสอบย้อนกลับทุเรียนอัจฉริยะ',
  description:
    'ProPass เจ้าหน้าที่ภาครัฐสามารถใช้ ProPass เพื่อติดตาม ตรวจสอบ และจัดการข้อมูลทุเรียนได้ครบวงจรผ่านระบบที่ปลอดภัยและเชื่อถือได้',
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

interface LayoutProps {
  children: React.ReactNode;
}

const RootLayout = async ({ children }: Readonly<LayoutProps>) => {
  const locale = await getLocale();
  const messages = await getMessages();

  const configEnv = await getPublicEnv();

  const envScript = `
  window.__ENV__ = ${JSON.stringify(configEnv)};
`;

  const accessToken = await getCookieServer<string>('access_token_pkg_house');

  return (
    <html lang={locale}>
      {process.env.NEXT_PUBLIC_GTAG_ID && (
        <>
          {/* Load gtag.js script */}
          <Script
            strategy="afterInteractive"
            src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GTAG_ID}`}
          />
          {/* Initialize gtag */}
          <Script id="gtag-init" strategy="afterInteractive">
            {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '${process.env.NEXT_PUBLIC_GTAG_ID}', {
            user_id: null
          });
        `}
          </Script>
        </>
      )}
      <head>
        <meta name="google" content="notranslate" />
        <Script id="set-window-env" strategy="beforeInteractive" dangerouslySetInnerHTML={{ __html: envScript }} />
        <link href="https://fonts.cdnfonts.com/css/myriad-pro" rel="stylesheet"></link>
      </head>
      <body className={fontFamily.className} suppressHydrationWarning={true}>
        <GtagSetup />
        <NextIntlClientProvider locale={locale} messages={messages}>
          <PostHogProvider>
            <ProviderLayout isLogin={!!accessToken}>
              {children}
            </ProviderLayout>
          </PostHogProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
};

export default RootLayout;
