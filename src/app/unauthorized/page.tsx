'use client';

import { Box, Button, Container, Typography, Stack, useTheme } from '@mui/material';
import { useTranslations } from 'next-intl';
import LockIcon from '@mui/icons-material/Lock';
import DeleteIcon from '@mui/icons-material/Delete';
import ExitToAppIcon from '@mui/icons-material/ExitToApp';
import { useAuthStore } from 'store/useAuthStore';
import { AppConfig } from 'configs/app-config';
import { logger } from 'utils/logger';

export default function UnAuthorize() {
  const unauthorizeT = useTranslations('unauthorize');
  const theme = useTheme();

  const { logout } = useAuthStore();

  const handleClearData = () => {
    logout();
    alert(unauthorizeT('data-cleared'));
  };

  const handleRedirectToPortal = () => {
    if (typeof window !== 'undefined') {
      if (AppConfig.PORTAL_URL) {
        window.location.href = AppConfig.PORTAL_URL;
      } else {
        logger.info('PORTAL_URL is not defined');
      }
    }
  };

  return (
    <Container maxWidth="sm">
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          textAlign: 'center',
          py: 8,
        }}
      >
        <Box
          sx={{
            backgroundColor: theme.palette.error.light || '#fef3f2',
            borderRadius: '50%',
            p: 2,
            mb: 3,
          }}
        >
          <LockIcon
            fontSize="large"
            sx={{
              color: theme.palette.error.main,
              fontSize: 60,
            }}
          />
        </Box>

        <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
          {unauthorizeT('unauthorized')}
        </Typography>

        <Typography variant="body1" color="text.secondary" sx={{ mb: 4, maxWidth: 480 }}>
          {unauthorizeT('not-authorized-message')}
        </Typography>

        <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} justifyContent="center" sx={{ mt: 3 }}>
          <Button variant="outlined" color="error" startIcon={<DeleteIcon />} onClick={handleClearData}>
            {unauthorizeT('clear-data')}
          </Button>

          <Button variant="contained" color="primary" startIcon={<ExitToAppIcon />} onClick={handleRedirectToPortal}>
            {unauthorizeT('go-to-portal')}
          </Button>
        </Stack>
      </Box>
    </Container>
  );
}
