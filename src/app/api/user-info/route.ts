import { clearCookie } from 'configs/cookie';
import { NextRequest, NextResponse } from 'next/server';
import { RecordedByEnum } from 'types';
import { User } from 'types/user';
import { handler } from 'utils/handler';

export async function GET(req: NextRequest) {
  const rawResponse = (await handler(req, 'GET', '/v1/user/me'.split('/'))) as Response;

  if (!rawResponse.ok) {
    return NextResponse.json({ error: rawResponse.text, success: false }, { status: rawResponse.status });
  }

  const response = (await rawResponse.json()) as { data: User };

  const userInfo = response.data;

  if (userInfo.profile.role !== RecordedByEnum.PACKING_HOUSE) {
    return NextResponse.json({ error: 'Unauthorized', success: false }, { status: 401 });
  }

  await clearCookie('user_info');

  const res = NextResponse.json({ success: true, data: userInfo }, { status: 200 });

  return res;
}
