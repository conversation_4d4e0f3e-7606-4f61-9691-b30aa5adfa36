import { setCookieServer } from 'configs/cookie';
import { NextResponse } from 'next/server';

export async function POST(req: Request) {
  const body = await req.json();

  const token = body?.access_token;

  if (!token) {
    return NextResponse.json({ error: 'Missing token' }, { status: 400 });
  }

  await setCookieServer('access_token_pkg_house', token);

  return NextResponse.json({ success: true });
}
