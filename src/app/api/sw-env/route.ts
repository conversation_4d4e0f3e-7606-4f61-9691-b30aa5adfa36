// app/api/sw-env/route.ts
import { NextResponse } from 'next/server';

// force this handler to run in Node (not Edge),
export const runtime = 'nodejs';

export async function GET() {
  // these are your NEXT_PUBLIC_* vars:
  const {
    NEXT_PUBLIC_FIREBASE_API_KEY,
    NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
    NEXT_PUBLIC_FIREBASE_PROJECT_ID,
    NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
    NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
    NEXT_PUBLIC_FIREBASE_APP_ID,
    NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,
  } = process.env;

  const js = `
    // attach to the service‐worker global scope
    self.__RUNTIME_SW_ENV = {
      NEXT_PUBLIC_FIREBASE_API_KEY: ${JSON.stringify(NEXT_PUBLIC_FIREBASE_API_KEY)},
      NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: ${JSON.stringify(NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN)},
      NEXT_PUBLIC_FIREBASE_PROJECT_ID: ${JSON.stringify(NEXT_PUBLIC_FIREBASE_PROJECT_ID)},
      NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: ${JSON.stringify(NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET)},
      NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: ${JSON.stringify(NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID)},
      NEXT_PUBLIC_FIREBASE_APP_ID: ${JSON.stringify(NEXT_PUBLIC_FIREBASE_APP_ID)},
      NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID: ${JSON.stringify(NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID)}
    };
  `;

  return new NextResponse(js, {
    status: 200,
    headers: {
      'Content-Type': 'application/javascript; charset=utf-8',
      // cache per-deployment; adjust if you need more aggressive caching
      'Cache-Control': 'public, max-age=0, must-revalidate',
    },
  });
}
