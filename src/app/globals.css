html,
body {
  width: 100vw;
  min-height: 100vh;
  overflow-x: hidden;
  font-size: 20px;
  box-sizing: border-box !important;
  background-color: #f3f4f6;
  display: flex;
  flex-direction: column;
  overscroll-behavior-y: none;
}

body {
  font-family: var(--font-poppins), var(--font-open-sans), sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: relative;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

.otp-input:focus {
  border-color: #3b82f6;
}

.otp-input {
  border-color: #ccc;
}

.otp-input.error {
  border-color: #ef4444;
}
