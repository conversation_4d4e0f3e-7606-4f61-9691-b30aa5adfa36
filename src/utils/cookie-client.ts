import { basePathProd } from 'configs/app-config';
import { get } from 'lodash-es';
import { TranslateLabel } from 'types';

export const LOCALE_COOKIE_NAME = 'locale_pg_house';
export const LOCALE_COOKIE_NAME_NEXT = 'NEXT_LOCALE';

export const LOCALE_COOKIE_MAX_AGE = 365 * 24 * 60 * 60;

export function getCookieLocale(): keyof TranslateLabel {
  if (typeof window === 'undefined' || typeof document === 'undefined') {
    return 'th';
  }

  const match = document.cookie.match(new RegExp('(^| )' + LOCALE_COOKIE_NAME_NEXT + '=([^;]+)'));
  return (match ? decodeURIComponent(match[2]) : 'th') as keyof TranslateLabel;
}
export function setCookieLocale(newLocale: string) {
  document.cookie = [
    `${LOCALE_COOKIE_NAME_NEXT}=${encodeURIComponent(newLocale)}`,
    `path=${basePathProd}`,
    `max-age=${LOCALE_COOKIE_MAX_AGE}`,
  ].join('; ');
}
export type RecordLabel = { id?: string; label: TranslateLabel };

export const getLabelByLocale = (record?: RecordLabel) => {
  if (!record) return '';

  const locale = getCookieLocale();

  return get(record, `label[${locale}]`, '');
};


export const getTranslateLabelByLocale = (label?: TranslateLabel) => {
  if (!label) return '';

  const locale = getCookieLocale();

  return get(label, locale, '');
};
