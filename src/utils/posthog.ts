/* eslint-disable @typescript-eslint/no-explicit-any */
import posthog from 'posthog-js';
import { logger } from './logger';
import { AxiosError } from 'axios';
import { get } from 'lodash-es';

export const capturePosthog = (eventName: string, properties?: Record<string, any>) => {
  try {
    posthog?.capture(`${eventName}`, {
      ...properties,
    });
  } catch {
    logger.info('Error capturing posthog event');
  }
};

export function captureApiError(error: unknown, context: Record<string, any> = {}) {
  if (error instanceof AxiosError) {
    const path = error.config?.url?.split('/fe/packaging-house/api/proxy/')[1] || 'unknown';

    const url = `api/${path}`;
    const messageError = get(error, 'response.data.details.msg');
    posthog?.capture('api_error', {
      description: `api ${url} failed with status ${error.response?.status} with message: ${messageError ?? error.message}`,
      code: error.response?.status,
      message: messageError ?? error.message,
      name: 'api_error',
      url,
    });
  } else {
    posthog?.capture('api_error', {
      message: String(error),
      ...context,
    });
  }
}

export function captureExceptionError(error: unknown, context: Record<string, any> = {}) {
  if (error instanceof AxiosError) {
    posthog?.capture('exception_error', {
      message: error.message,
      stack: error.stack,
      name: error.name,
    });
  } else {
    posthog?.capture('exception_error', {
      message: String(error),
      ...context,
    });
  }
}
