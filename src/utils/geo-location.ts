import { FIXED_LOCATION } from "constant";

export const getCurrentLocation = async (location?: { lat: number; lng: number }): Promise<{ lat: number; lng: number }> => {
  const defaultLocation = location ?? { lat: FIXED_LOCATION.SHIPMENT.latitude, lng: FIXED_LOCATION.SHIPMENT.longitude };

  if (!navigator.geolocation) {
    return defaultLocation;
  }

  return new Promise((resolve) => {
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        resolve({ lat: latitude, lng: longitude });
      },
      () => {
        resolve(defaultLocation); // fallback on error
      },
      {
        timeout: 5000,
      }
    );
  });
};
