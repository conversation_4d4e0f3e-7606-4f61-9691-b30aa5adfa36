import { safeJSONParse } from './common';

const defaultExpireTime = 2 * 24 * 60 * 60 * 1000;

export const setLocalStorage = (key: string, value: object, expire?: number) => {
  if (typeof window === 'undefined') return;

  const newExpiredTime = Date.now() + (expire ?? defaultExpireTime);

  localStorage.setItem(key, JSON.stringify({ ...value, expireTime: newExpiredTime }));
};

export const getLocalStorage = (key: string) => {
  if (typeof window === 'undefined') return;

  const storage = localStorage.getItem(key);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const data = safeJSONParse(storage) as any;

  if(!data) return null;

  const currentTime = Date.now();

  if (currentTime > data.expireTime) {
    localStorage.removeItem(key);
    return null;
  }

  return data;
};
