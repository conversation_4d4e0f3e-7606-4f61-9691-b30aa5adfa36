import { parseFormattedNumber } from '../convert';

describe('parseFormattedNumber', () => {
  it('should parse a simple number string correctly', () => {
    expect(parseFormattedNumber('123')).toBe(123);
    expect(parseFormattedNumber('123.45')).toBe(123.45);
    expect(parseFormattedNumber('-123.45')).toBe(-123.45);
  });

  it('should handle numbers with commas', () => {
    expect(parseFormattedNumber('1,000')).toBe(1000);
    expect(parseFormattedNumber('1,234,567.89')).toBe(1234567.89);
    expect(parseFormattedNumber('-1,234,567.89')).toBe(-1234567.89);
  });

  it('should handle whitespace', () => {
    expect(parseFormattedNumber(' 123 ')).toBe(123);
    expect(parseFormattedNumber('  1,234  ')).toBe(1234);
  });

  it('should return 0 for invalid number formats', () => {
    expect(parseFormattedNumber('abc')).toBe(0);
    expect(parseFormattedNumber('123abc')).toBe(123); // parseFloat takes the valid part
    expect(parseFormattedNumber('abc123')).toBe(0);
    expect(parseFormattedNumber('')).toBe(0);
  });

  it('should handle edge cases', () => {
    expect(parseFormattedNumber('0')).toBe(0);
    expect(parseFormattedNumber('-0')).toBe(0);
    expect(parseFormattedNumber('Infinity')).toBe(Infinity);
    expect(parseFormattedNumber('-Infinity')).toBe(-Infinity);
    expect(parseFormattedNumber('NaN')).toBe(0); // NaN is caught and returned as 0
  });
});
