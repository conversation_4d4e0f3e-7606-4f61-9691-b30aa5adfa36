/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  cleanDecimalInput,
  normalizeValue,
  validateInteger<PERSON>ength,
  hasLeadingZero,
  processDecimalValue,
  processIntegerValue,
  findVarietyAndGrade,
  ensureVarietyInMap,
  isValidValue
} from '../input-grade';
import { DurianVariety } from 'types';

describe('cleanDecimalInput', () => {
  it('should return the input string if it does not contain a dot', () => {
    expect(cleanDecimalInput('123')).toBe('123');
  });

  it('should remove all dots after the first one', () => {
    expect(cleanDecimalInput('123.45.67')).toBe('123.4567');
    expect(cleanDecimalInput('**********.8')).toBe('123.45678');
  });

  it('should handle input starting with a dot', () => {
    expect(cleanDecimalInput('.123')).toBe('.123');
  });

  it('should handle empty string', () => {
    expect(cleanDecimalInput('')).toBe('');
  });
});

describe('normalizeValue', () => {
  it('should prepend "0" to values starting with a dot', () => {
    expect(normalizeValue('.5')).toBe('0.5');
  });

  it('should replace single dot with "1."', () => {
    expect(normalizeValue('.')).toBe('1.');
  });

  it('should return unchanged value for normal inputs', () => {
    expect(normalizeValue('123')).toBe('123');
    expect(normalizeValue('123.45')).toBe('123.45');
  });
});

describe('validateIntegerLength', () => {
  it('should return true if the value length is less than or equal to the max length', () => {
    expect(validateIntegerLength('123', 7)).toBe(true);
    expect(validateIntegerLength('1234567', 7)).toBe(true);
  });

  it('should return false if the value length exceeds the max length', () => {
    expect(validateIntegerLength('12345678', 7)).toBe(false);
  });

  it('should handle empty string', () => {
    expect(validateIntegerLength('', 7)).toBe(true);
  });
});

describe('hasLeadingZero', () => {
  it('should return true for values starting with 0 followed by a digit', () => {
    expect(hasLeadingZero('01')).toBe(true);
    expect(hasLeadingZero('0123')).toBe(true);
  });

  it('should return false for decimal values like 0.x', () => {
    expect(hasLeadingZero('0.5')).toBe(false);
  });

  it('should return false for single digit 0', () => {
    expect(hasLeadingZero('0')).toBe(false);
  });

  it('should return false for values not starting with 0', () => {
    expect(hasLeadingZero('123')).toBe(false);
  });

  it('should return false for empty string', () => {
    expect(hasLeadingZero('')).toBe(false);
  });
});

describe('processDecimalValue', () => {
  it('should process valid decimal values correctly', () => {
    expect(processDecimalValue('123.4')).toBe('123.4');
    expect(processDecimalValue('1.5')).toBe('1.5');
  });

  it('should truncate decimal to 1 digit', () => {
    expect(processDecimalValue('123.45')).toBe('123.4');
  });

  it('should allow values ending with a dot', () => {
    expect(processDecimalValue('5.')).toBe('5.');
  });

  it('should return null for values less than 1', () => {
    expect(processDecimalValue('0.5')).toBeNull();
  });

  it('should return null for integer parts exceeding max length', () => {
    expect(processDecimalValue('12345678.9')).toBeNull();
  });
});

describe('processIntegerValue', () => {
  it('should process valid integer values correctly', () => {
    expect(processIntegerValue('123')).toBe('123');
    expect(processIntegerValue('1')).toBe('1');
  });

  it('should return null for values less than 1', () => {
    expect(processIntegerValue('0')).toBeNull();
  });

  it('should return null for integer parts exceeding max length', () => {
    expect(processIntegerValue('12345678')).toBeNull();
  });
});

describe('findVarietyAndGrade', () => {
  const mockVarieties = [
    {
      id: 'V1',
      name: 'Variety 1',
      grades: [
        { id: 'G1', name: 'Grade 1', weight: 1 },
        { id: 'G2', name: 'Grade 2', weight: 2 }
      ]
    },
    {
      id: 'V2',
      name: 'Variety 2',
      grades: [
        { id: 'G3', name: 'Grade 3', weight: 3 }
      ]
    }
  ] as DurianVariety[];

  it('should find the correct variety and grade', () => {
    expect(findVarietyAndGrade('V1G1', mockVarieties)).toEqual({
      varietyId: 'V1',
      grade: { id: 'G1', name: 'Grade 1', weight: 1 }
    });
  });

  it('should return empty object when variety not found', () => {
    expect(findVarietyAndGrade('V3G1', mockVarieties)).toEqual({});
  });

  it('should return empty object when grade not found', () => {
    expect(findVarietyAndGrade('V1G4', mockVarieties)).toEqual({});
  });
});

describe('ensureVarietyInMap', () => {
  it('should add variety to map if it does not exist', () => {
    const varietiesMap: Record<string, any> = {};
    const varietyBloomDays = { 'V1': 30 };

    ensureVarietyInMap('V1', varietiesMap, varietyBloomDays);

    expect(varietiesMap).toEqual({
      'V1': {
        id: 'V1',
        grades: [],
        flowerBloomingDay: 30
      }
    });
  });

  it('should not modify existing variety in map', () => {
    const varietiesMap: Record<string, any> = {
      'V1': {
        id: 'V1',
        grades: [{ id: 'G1', weight: 1 }],
        flowerBloomingDay: 30
      }
    };
    const varietyBloomDays = { 'V1': 40 };

    ensureVarietyInMap('V1', varietiesMap, varietyBloomDays);

    expect(varietiesMap['V1'].flowerBloomingDay).toBe(30);
    expect(varietiesMap['V1'].grades).toEqual([{ id: 'G1', weight: 1 }]);
  });
});

describe('isValidValue', () => {
  it('should return true for valid non-empty strings', () => {
    expect(isValidValue('123')).toBe(true);
  });

  it('should return false for empty strings', () => {
    expect(isValidValue('')).toBe(false);
  });

  it('should return false for strings with only whitespace', () => {
    expect(isValidValue('   ')).toBe(false);
  });

  it('should return false for undefined values', () => {
    expect(isValidValue(undefined)).toBe(false);
  });
});
