import { enqueueSnackbar } from 'notistack';

const toastMessages = {
  error: (message: string) => enqueueSnackbar(message, { variant: 'error' }),
  success: (message: string) => enqueueSnackbar(message, { variant: 'success' }),
  info: (message: string) => enqueueSnackbar(message, { variant: 'info' }),
  warning: (message: string) => enqueueSnackbar(message, { variant: 'warning' }),
  default: (message: string) => enqueueSnackbar(message, { variant: 'default' }),
};

export default toastMessages;
