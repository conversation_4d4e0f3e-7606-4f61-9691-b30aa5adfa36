import envConfigJson from '../../public/env-config.json';

export async function getPublicEnv() {
  const processedConfig: Record<string, string> = {};

  Object.entries(envConfigJson).forEach(([key, value]) => {
    // If it's a placeholder (wrapped in %%), keep it as is in the browser
    // The actual values should have been replaced during build time
    if (typeof value === 'string' && value.startsWith('%') && value.endsWith('%')) {
      const envVarName = value.substring(1, value.length - 1);
      processedConfig[key] = process.env[envVarName] ?? '';
    } else {
      processedConfig[key] = value;
    }
  });

  return processedConfig;
}
