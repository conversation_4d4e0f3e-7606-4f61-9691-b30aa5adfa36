import { transportationModeValues } from 'constant/shipment';
import { OcrSourceFrom, Receipt, SuggestOcrResponse } from 'types';
import { v4 } from 'uuid';

export const mapperOcrResponseToToOcrRecord = (input: SuggestOcrResponse, source: string, id?: string): Receipt => {
  return {
    receiptNumber: input.receiptNumber,
    destinationCountry: "THE PEOPLE'S REPUBLIC OF CHINA",
    transportationMode: transportationModeValues.includes(input.transportationMode) ? input.transportationMode : '',
    numberOfBoxes: input.numberOfBoxes?.toString() ?? '',
    exportDate: input.exportDate ? input.exportDate * 1000 : null,
    borderCheckpointName: input.borderCheckpointName ?? '',
    containerNumber: '',
    truckNumber: '',
    trailerNumber: '',
    orchardNo: '',
    totalWeightKg: (input.totalWeightKg ?? 0).toString(),
    id: id ?? v4(),
    sourceFrom: (source as OcrSourceFrom) ?? 'ocr',
    nameOfExportingCompany: input.nameOfExportingCompany ?? '',
  };
};
