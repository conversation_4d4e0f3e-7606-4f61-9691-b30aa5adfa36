import { ShipmentHistory } from 'types/shipment-history';

export const mockShipmentHistoryData: ShipmentHistory = {
  shipmentId: 'SM211225000001',
  entries: [
    {
      id: '1',
      timestamp: '2025-01-15T15:30:00Z',
      user: {
        id: 'user2',
        name: '<PERSON>',
        avatar: '',
      },
      action: 'sealed',
      changes: [
        {
          field: 'shipmentName',
          oldValue: null,
          newValue: 'Shipment to China',
          changeType: 'sealed',
        },
      ],
    },
    {
      id: '2',
      timestamp: '2025-01-15T13:45:00Z',
      user: {
        id: 'user1',
        name: '<PERSON><PERSON><PERSON><PERSON>',
      },
      action: 'updated',
      changes: [
        {
          field: 'shipmentName',
          oldValue: '5000 kg durian to Shanghai China',
          newValue: null,
          changeType: 'updated',
        },
        {
          field: 'shipmentName2',
          oldValue: '',
          newValue: '4800 kg durian to China',
          changeType: 'updated',
        },
        {
          field: 'shipmentPhotos',
          oldValue: 'shipment_photo170944.jpg',
          newValue: 'null',
          changeType: 'removed',
        },
        {
          field: 'shipmentPhotos',
          oldValue: 'shipment_photo12366.jpg',
          newValue: null,
          changeType: 'removed',
        },
        {
          field: 'shipmentPhotos',
          oldValue: null,
          newValue: 'shipment_photo123jjaryu56.jpg',
          changeType: 'added',
        },
        {
          field: 'destinationCountry',
          oldValue: 'China',
          newValue: 'China, Vietnam, Laos',
          changeType: 'updated',
        },
        {
          field: 'shipmentPlace',
          oldValue: '7000 kg durian to Shanghai China',
          newValue: '4800 kg durian to China',
          changeType: 'updated',
        },
        {
          field: 'additionalDocuments',
          oldValue: 'Commercial_Invoice.png',
          newValue: null,
          changeType: 'removed',
        },
        {
          field: 'additionalDocuments',
          oldValue: 'Export_Declaration_Form.pdf',
          newValue: null,
          changeType: 'removed',
        },
        {
          field: 'additionalDocuments',
          oldValue: null,
          newValue: 'shipment_photo124.jpg',
          changeType: 'added',
        },
        {
          field: 'additionalDocuments',
          oldValue: null,
          newValue: 'Export_Declaration_Form.pdf',
          changeType: 'added',
        },
      ],
      documents: [
        {
          id: 'doc1',
          name: 'shipment_photo170944.jpg',
          type: 'image/jpeg',
        },
        {
          id: 'doc2',
          name: 'shipment_photo12366.jpg',
          type: 'image/jpeg',
        },
        {
          id: 'doc3',
          name: 'shipment_photo123jjaryu56.jpg',
          type: 'image/jpeg',
        },
        {
          id: 'doc4',
          name: 'Commercial_Invoice.png',
          type: 'image/png',
        },
        {
          id: 'doc5',
          name: 'Export_Declaration_Form.pdf',
          type: 'application/pdf',
        },
        {
          id: 'doc6',
          name: 'shipment_photo124.jpg',
          type: 'image/jpeg',
        },
        {
          id: 'doc7',
          name: 'Export_Declaration_Form.pdf',
          type: 'application/pdf',
        },
      ],
    },
    {
      id: '3',
      timestamp: '2025-01-15T12:50:00Z',
      user: {
        id: 'user1',
        name: 'Ayahidi Srumputa',
      },
      action: 'updated',
      changes: [
        {
          field: 'shipmentName',
          oldValue: '5000 kg durian to Shanghai China',
          newValue: '4800 kg durian to China',
          changeType: 'updated',
        },
        {
          field: 'shipmentName2',
          oldValue: '7000 kg durian to Shanghai China',
          newValue: '4800 kg durian to China',
          changeType: 'updated',
        },
        {
          field: 'shipmentPhotos',
          oldValue: 'shipment_photo170944.jpg',
          newValue: null,
          changeType: 'removed',
        },
        {
          field: 'shipmentPhotos',
          oldValue: 'shipment_photo12366.jpg',
          newValue: null,
          changeType: 'removed',
        },
        {
          field: 'shipmentPhotos',
          oldValue: null,
          newValue: 'shipment_photo123jjaryu56.jpg',
          changeType: 'added',
        },
        {
          field: 'destinationCountry',
          oldValue: 'China',
          newValue: 'China, Vietnam, Laos',
          changeType: 'updated',
        },
        {
          field: 'additionalDocuments',
          oldValue: 'Commercial_Invoice.png',
          newValue: null,
          changeType: 'removed',
        },
        {
          field: 'additionalDocuments',
          oldValue: 'Export_Declaration_Form.pdf',
          newValue: null,
          changeType: 'removed',
        },
        {
          field: 'additionalDocuments',
          oldValue: null,
          newValue: 'shipment_photo124.jpg',
          changeType: 'added',
        },
        {
          field: 'additionalDocuments',
          oldValue: null,
          newValue: 'Export_Declaration_Form.pdf',
          changeType: 'added',
        },
      ],
      documents: [
        {
          id: 'doc1',
          name: 'shipment_photo170944.jpg',
          type: 'image/jpeg',
        },
        {
          id: 'doc2',
          name: 'shipment_photo12366.jpg',
          type: 'image/jpeg',
        },
        {
          id: 'doc3',
          name: 'shipment_photo123jjaryu56.jpg',
          type: 'image/jpeg',
        },
        {
          id: 'doc4',
          name: 'Commercial_Invoice.png',
          type: 'image/png',
        },
        {
          id: 'doc5',
          name: 'Export_Declaration_Form.pdf',
          type: 'application/pdf',
        },
        {
          id: 'doc6',
          name: 'shipment_photo124.jpg',
          type: 'image/jpeg',
        },
        {
          id: 'doc7',
          name: 'Export_Declaration_Form.pdf',
          type: 'application/pdf',
        },
      ],
    },

    {
      id: '4',
      user: {
        id: '1',
        name: 'John Smith',
      },
      action: 'updated',
      timestamp: '2025-01-15T10:30:00Z',
      changes: [
        {
          field: 'packagingInformation',
          oldValue: {
            id: 'pkg-001',
            brandNameInfo: {
              id: 'brand-001',
              label: { en: 'Premium Durian', th: 'ทุเรียนพรีเมียม' },
            },
            productTypeInfo: {
              id: 'type-001',
              label: { en: 'Fresh Durian', th: 'ทุเรียนสด' },
            },
            varietyGradeJoinId: 1,
            weightKg: 8,
            quantity: '30',
            numberOfBoxes: 30,
            sealNumber: 'SEAL-001',
            varietyGrade: {
              gradeValue: 'B',
              gradeDisplayText: 'Grade B',
              varietyValue: 'old-variety',
              varietyDisplayText: 'Old Durian Variety',
            },
            varieties: [],
            batchlot: 'BATCH-001',
            varietyName: 'Old Durian Variety',
            packingDate: 1705276800000,
          },
          newValue: null,
          changeType: 'removed',
        },
        {
          field: 'packagingInformation',
          oldValue: {
            id: 'pkg-002',
            brandNameInfo: {
              id: 'brand-002',
              label: { en: 'Standard Durian', th: 'ทุเรียนมาตรฐาน' },
            },
            productTypeInfo: {
              id: 'type-002',
              label: { en: 'Fresh Durian', th: 'ทุเรียนสด' },
            },
            varietyGradeJoinId: 2,
            weightKg: 8,
            quantity: '30',
            numberOfBoxes: 30,
            sealNumber: 'SEAL-002',
            varietyGrade: {
              gradeValue: 'B',
              gradeDisplayText: 'Grade B',
              varietyValue: 'old-variety-2',
              varietyDisplayText: 'Old Durian Variety 2',
            },
            varieties: [],
            batchlot: 'BATCH-002',
            varietyName: 'Old Durian Variety 2',
            packingDate: 1705276800000,
          },
          newValue: null,
          changeType: 'removed',
        },
        {
          field: 'packagingInformation',
          oldValue: null,
          newValue: {
            id: 'pkg-003',
            brandNameInfo: {
              id: 'brand-003',
              label: { en: 'King Durian Brand', th: 'แบรนด์ทุเรียนคิง' },
            },
            productTypeInfo: {
              id: 'type-003',
              label: { en: 'Premium Fresh Durian', th: 'ทุเรียนสดพรีเมียม' },
            },
            varietyGradeJoinId: 3,
            weightKg: 9,
            quantity: '40',
            numberOfBoxes: 40,
            sealNumber: 'SEAL-003',
            varietyGrade: {
              gradeValue: 'AB',
              gradeDisplayText: 'Grade AB',
              varietyValue: 'king-durian',
              varietyDisplayText: 'King Durian',
            },
            varieties: [
              {
                id: 'var-001',
                value: 'monthong',
                label: { en: 'Monthong', th: 'หมอนทอง' },
                flowerBloomingDay: 1705276800,
                grades: [
                  {
                    id: 'grade-ab',
                    value: 'AB',
                    label: { en: 'Grade AB', th: 'เกรด AB' },
                    weight: 9,
                  },
                ],
                name: 'Monthong',
              },
            ],
            batchlot: 'BATCH-003',
            varietyName: 'King Durian',
            packingDate: 1705363200000,
          },
          changeType: 'added',
        },
        {
          field: 'packagingInformation',
          oldValue: null,
          newValue: {
            id: 'pkg-004',
            brandNameInfo: {
              id: 'brand-004',
              label: { en: 'Royal Durian Brand', th: 'แบรนด์ทุเรียนรอยัล' },
            },
            productTypeInfo: {
              id: 'type-004',
              label: { en: 'Premium Fresh Durian', th: 'ทุเรียนสดพรีเมียม' },
            },
            varietyGradeJoinId: 4,
            weightKg: 9,
            quantity: '40',
            numberOfBoxes: 40,
            sealNumber: 'SEAL-004',
            varietyGrade: {
              gradeValue: 'AB',
              gradeDisplayText: 'Grade AB',
              varietyValue: 'king-durian-2',
              varietyDisplayText: 'King Durian Premium',
            },
            varieties: [
              {
                id: 'var-002',
                value: 'monthong-premium',
                label: { en: 'Monthong Premium', th: 'หมอนทองพรีเมียม' },
                flowerBloomingDay: 1705363200,
                grades: [
                  {
                    id: 'grade-ab-premium',
                    value: 'AB',
                    label: { en: 'Grade AB Premium', th: 'เกรด AB พรีเมียม' },
                    weight: 9,
                  },
                ],
                name: 'Monthong Premium',
              },
            ],
            batchlot: 'BATCH-004',
            varietyName: 'King Durian Premium',
            packingDate: 1705449600000,
          },
          changeType: 'added',
        },
      ],
      documents: [],
    },
    {
      id: '5',
      timestamp: '2025-01-15T09:30:00Z',
      user: {
        id: 'user1',
        name: 'Ayahidi Srumputa',
      },
      action: 'created',
      changes: [
        {
          field: 'shipmentName',
          oldValue: null,
          newValue: 'Shipment to China',
          changeType: 'created',
        },
      ],
    },
  ],
};
