import { createTheme } from '@mui/material/styles';

import { colors } from './colors';

declare module '@mui/material/Typography' {
  interface TypographyPropsVariantOverrides {
    body3: true;
    body4: true;
  }
}

declare module '@mui/material/styles' {
  interface Palette {
    customColors: typeof colors & {
      durianYellow: string;
      darkBlue: string;
      softGray: string;
      gray5: string;
      gray6: string;
      white: string;
      lightLavender: string;
      waiting: string;
      reject: string;
      receive: string;
      draft: string;
      draftBg: string;
      waitingBg: string;
      rejectBg: string;
      receiveBg: string;
      primaryMain: string;
      primaryHover: string;
      lightGray: string;
      blueHighlight: string;
      blue500: string;
      blueTint: string;
      blue50: string;
      lightRed: string;
      toastSuccess: string;
      toastSuccessBg: string;
      toastError: string;
      toastErrorBg: string;
      toastWarning: string;
      toastWarningBg: string;
      toastInfo: string;
      toastInfoBg: string;
    };
  }

  interface PaletteOptions {
    customColors?: typeof colors & {
      durianYellow?: string;
      darkBlue?: string;
      softGray?: string;
      gray5?: string;
      gray6?: string;
      white: string;
      lightLavender: string;
      waiting: string;
      reject: string;
      receive: string;
      draft: string;
      draftBg: string;
      waitingBg: string;
      rejectBg: string;
      receiveBg: string;
      primaryMain?: string;
      primaryHover?: string;
      lightGray?: string;
      blueHighlight?: string;
      blue500?: string;
      blueTint?: string;
      blue50?: string;
      lightRed?: string;
      toastSuccess?: string;
      toastSuccessBg?: string;
      toastError?: string;
      toastErrorBg?: string;
      toastWarning?: string;
      toastWarningBg?: string;
      toastInfo?: string;
      toastInfoBg?: string;
    };
  }
}

export const theme = createTheme({
  palette: {
    primary: {
      main: colors.primary,
      contrastText: '#FFFFFF',
    },
    customColors: {
      durianYellow: '#FFB300',
      darkBlue: '#2E3B55',
      softGray: colors.bg,
      gray5: '#e5e5ea',
      gray6: '#f2f2f7',
      lightLavender: '#f7f2fa',
      waitingBg: '#FFF8E1',
      receiveBg: '#E8F5E9',
      draftBg: '#E5E7EB',
      draft: '#9CA3AF',
      waiting: '#F57F17',
      receive: '#2E7D32',
      primaryMain: '#4285F4',
      lightGray: '#f5f5f5',
      blueHighlight: '#2266D9',
      blue500: '#2196f3',
      blueTint: '#F5F9FF',
      blue50: '#E3F2FD',
      lightRed: '#FECACA',
      toastSuccess: '#34C759',
      toastSuccessBg: '#E2F1E6',
      toastError: '#EF4444',
      toastErrorBg: '#F6E3E2',
      toastWarning: '#22C55E',
      toastWarningBg: '#22C55E',
      toastInfo: '#007AFF',
      toastInfoBg: '#DDE9F6',
      ...colors,
    },
  },
  typography: {
    fontFamily: "'Poppins', 'Roboto', sans-serif",
    h1: {
      fontSize: '2rem',
      fontWeight: 600,
    },
    h2: {
      fontSize: '1.8rem',
      fontWeight: 500,
    },
    h3: {
      fontSize: 20,
      fontWeight: 600,
    },
    body1: {
      fontSize: 18,
      fontWeight: 500,
    },
    body2: {
      fontSize: 14,
      fontWeight: 400,
    },
    button: {
      textTransform: 'none',
    },
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: (baseTheme) => ({
        '.virtual-list-scrollbar-thumb::-webkit-scrollbar': {
          width: '8px',
          height: '8px',
        },
        '.virtual-list-scrollbar-thumb::-webkit-scrollbar-thumb': {
          backgroundColor: '#4285f4',
          borderRadius: '8px',
          border: '2px solid transparent',
          backgroundClip: 'content-box',
        },
        '.virtual-list-scrollbar-thumb::-webkit-scrollbar-track': {
          backgroundColor: '#f1f1f1',
        },
        '.virtual-list-scrollbar-thumb': {
          scrollbarColor: '#4285f4 #f1f1f1',
          scrollbarWidth: 'thin',
          background: '#4285f4',
        },

        /* WebKit browsers */
        '::-webkit-scrollbar': {
          width: '4px',
          height: '4px',
        },
        '::-webkit-scrollbar-track': {
          backgroundColor: baseTheme.palette.customColors.white,
        },
        '::-webkit-scrollbar-thumb': {
          backgroundColor: baseTheme.palette.customColors.white,
          borderRadius: '4px',
          border: `2px solid ${baseTheme.palette.customColors.divider}`,
        },
        '::-webkit-scrollbar-thumb:hover': {
          backgroundColor: theme.palette.primary.dark, // darker on hover
        },

        /* Firefox */
        html: {
          scrollbarWidth: 'thin', // "auto" | "thin" | "none"
          scrollbarColor: `${baseTheme.palette.customColors.divider} ${baseTheme.palette.customColors.white}`,
        },
      }),
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          fontWeight: 600,
          lineHeight: 1.2,
          textTransform: 'none',
          padding: '12px',
          boxShadow: 'none',
        },
        containedPrimary: {
          '&.Mui-disabled': {
            backgroundColor: colors.primary50,
            color: colors.divider,
          },
        },
      },
    },
    MuiFormHelperText: {
      styleOverrides: {
        root: {
          fontSize: '0.75rem',
          fontWeight: 400,
          marginTop: '4px',
          marginLeft: 0,
          marginRight: 0,
        },
      },
    },
  },
});
