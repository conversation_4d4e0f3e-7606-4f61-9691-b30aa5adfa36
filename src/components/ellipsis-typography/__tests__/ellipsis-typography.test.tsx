import React from 'react';
import { render, screen } from '@testing-library/react';
import { EllipsisTypography } from '../ellipsis-typography';

describe('EllipsisTypography', () => {
  // Clean up mocks after each test
  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('renders the text content', () => {
    render(<EllipsisTypography text="Short text" />);
    expect(screen.getByText('Short text')).toBeInTheDocument();
  });

  it('applies correct styling for ellipsis', () => {
    render(<EllipsisTypography text="Any text" />);

    const typography = screen.getByText('Any text');
    expect(typography).toHaveStyle({
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      whiteSpace: 'nowrap',
      width: '100%',
    });
  });

  it('has Typography component with correct props', () => {
    render(<EllipsisTypography text="Test text" />);

    const typography = screen.getByText('Test text');
    expect(typography).toHaveClass('MuiTypography-root');
    expect(typography).toHaveClass('MuiTypography-body2');
  });

  it('wraps text in Tooltip component', () => {
    render(<EllipsisTypography text="Test text" />);

    // Check that the Typography component exists (which is wrapped by Tooltip)
    const textElement = screen.getByText('Test text');
    expect(textElement).toBeInTheDocument();

    // Verify it's a Typography component (which means it's inside the Tooltip)
    expect(textElement).toHaveClass('MuiTypography-root');
  });

  // Test the behavior by checking if the component renders without errors
  // with different text lengths (the actual overflow detection is hard to test in jsdom)
  it('handles long text without errors', () => {
    const longText =
      'This is a very long text that would normally cause overflow in a real browser environment';

    expect(() => {
      render(<EllipsisTypography text={longText} />);
    }).not.toThrow();

    expect(screen.getByText(longText)).toBeInTheDocument();
  });

  it('handles empty text', () => {
    render(<EllipsisTypography text="" />);

    // Should render without error even with empty text
    const typography = screen.getByRole('generic'); // Typography renders as span/div
    expect(typography).toBeInTheDocument();
  });
});
