'use client';

import NextImage, { ImageProps as NextImageProps } from 'next/image';
import { Skeleton, SkeletonOwnProps } from '@mui/material';
import { useState } from 'react';
import { basePathProd } from 'configs/app-config';

type ImgProps = {
  internalAsset?: boolean;
  skeletonVariant?: SkeletonOwnProps['variant'];
};

export const getInternalAssetUrl = (src: string) => {
  return `${basePathProd}${src}`;
};


export const Image = ({
  skeletonVariant = 'rectangular',
  internalAsset = false,
  src: srcProp,
  ...props
}: NextImageProps & ImgProps) => {
  const [loaded, setLoaded] = useState(false);

  const [src, setSrc] = useState(() => {
    if (internalAsset) {
      return typeof srcProp === 'string' ? getInternalAssetUrl(srcProp) : srcProp;
    }
    return srcProp;
  });

  const handleImageError = () => {
    setLoaded(true);
    setSrc(getInternalAssetUrl('/assets/images/default-profile.svg'));
  };

  return (
    <div style={{ position: 'relative', width: props.width, height: props.height }}>
      {!loaded && (
        <Skeleton
          variant={skeletonVariant}
          width={props.width}
          height={props.height}
          sx={{ position: 'absolute', top: 0, left: 0, zIndex: 1 }}
        />
      )}

      <NextImage
        {...props}
        alt={props.alt || ''}
        src={src}
        style={{
          opacity: loaded ? 1 : 0,
          transition: 'opacity 0.3s ease-in-out',
          objectFit: 'cover',
          ...props.style,
        }}
        onLoad={() => setLoaded(true)}
        onError={handleImageError}
      />
    </div>
  );
};
