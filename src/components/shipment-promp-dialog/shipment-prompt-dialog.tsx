// components/PromptDialog.tsx
'use client';

import CloseIcon from '@mui/icons-material/Close';
import { Box, Button, Dialog, DialogActions, DialogContent, IconButton, Stack, Typography } from '@mui/material';
import warningDialogIcon from 'assets/icons/warning-dialog-icon.svg';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import React, { ReactNode } from 'react';

type ShipmentPromptDialogProps = {
  open: boolean;
  title?: string;
  children?: ReactNode;
  onConfirm: (value?: string) => void;
  onClose: () => void;
  contents: string[];
};

export const ShipmentPromptDialog: React.FC<ShipmentPromptDialogProps> = ({
  contents,
  onConfirm,
  onClose,
  open,
}) => {
  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  const commonT = useTranslations('common');
  const shipmentT = useTranslations('shipment');

  return (
    <Dialog fullWidth open={open} onClose={onClose} disableScrollLock>
      <DialogContent sx={{ display: 'flex', gap: '12px' }}>
        <Box sx={{ display: 'flex', flexDirection: 'row', gap: '16px', alignItems: 'flex-start' }}>
          <Image src={warningDialogIcon} alt="Warning Icon" width={58} height={58} />
          <Stack flexDirection="column" gap="4px">
            <Typography variant="body1" component="p" fontSize="20px" fontWeight="bold">
              {shipmentT('warning-not-match-title')}
            </Typography>
            <Typography variant="caption" component="p" fontSize="16px" color="text.secondary">
              {shipmentT('warning-not-match-content')}:
            </Typography>
            {contents.map((content, index) => (
              <Typography key={index} variant="body2" component="li" fontSize="16px" color="text.secondary" fontWeight="bold">
                {content}
              </Typography>
            ))}
              <Typography variant="caption" component="p" fontSize="16px" color="text.secondary">
              {shipmentT('warning-not-match-confirm')}
            </Typography>
          </Stack>
        </Box>

        <IconButton onClick={onClose} size="small" sx={{ position: 'absolute', top: '16px', right: '16px' }}>
          <CloseIcon fontSize="small" />
        </IconButton>
      </DialogContent>

      <DialogActions sx={{ display: 'flex', gap: '12px', pb: '20px', pr: '20px' }}>
        <Button variant="outlined" onClick={onClose} sx={{ width: '120px' }}>
          {commonT('cancel-modal-btn')}
        </Button>
        <Button onClick={handleConfirm} color="error" variant="contained" sx={{ width: '120px' }}>
          {commonT('ignore-button')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
