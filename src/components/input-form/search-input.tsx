'use client';

import { FC, useEffect, useMemo, useState } from 'react';

import { CSSObject } from '@emotion/react';
import SearchIcon from '@mui/icons-material/Search';
import { InputAdornment, OutlinedInput, OutlinedInputProps } from '@mui/material';
import { debounce } from 'lodash-es';

type SearchInputProps = {
  placeholder?: string;
  customSx?: CSSObject;
  maxLength?: number;
  loading?: boolean;
} & OutlinedInputProps;

const SearchInput: FC<SearchInputProps> = ({ onChange, placeholder, ...props }) => {
  return (
    <OutlinedInput
      placeholder={placeholder}
      onChange={onChange}
      fullWidth
      sx={{ height: '40px', width: '300px' }}
      startAdornment={
        <InputAdornment position="start">
          <SearchIcon />
        </InputAdornment>
      }
      {...props}
    />
  );
};

const DEBOUNCE_TIME = 300;

type DebounceSearchInputProps = {
  onChange: (val: string) => void;
} & Omit<SearchInputProps, 'onChange'>;

export const DebounceSearchInput: FC<DebounceSearchInputProps> = ({ onChange, value, ...props }) => {
  const [localSearch, setLocalSearch] = useState<string>((value as string) ?? '');

  const debouncedSetSearchKeyword = useMemo(
    () =>
      debounce((val: string) => {
        onChange(val);
      }, DEBOUNCE_TIME),
    [onChange]
  );

  useEffect(() => {
    debouncedSetSearchKeyword(localSearch);
    return () => {
      debouncedSetSearchKeyword.cancel();
    };
  }, [localSearch, debouncedSetSearchKeyword]);

  useEffect(() => {
    if (value === '') {
      setLocalSearch(value);
    }
  }, [value]);

  return <SearchInput value={localSearch} onChange={(e) => setLocalSearch(e.target.value)} {...props} />;
};

export default SearchInput;
