'use client';

import { CircularProgress, FormControl, InputAdornment, TextField, TextFieldProps } from '@mui/material';
import { omit } from 'lodash-es';
import { FC, JSX } from 'react';

interface InputProps extends Omit<TextFieldProps, 'type'> {
  maxLength?: number;
  loading?: boolean;
  endAdornment?: JSX.Element;
}

export const TextInput: FC<InputProps> = ({ maxLength, loading = false, endAdornment, sx, slotProps, ...props }) => {
  return (
    <FormControl fullWidth sx={{ position: 'relative' }}>
      <TextField
        {...props}
        type={'text'}
        disabled={props.disabled || loading}
        fullWidth
        sx={{
          '& .MuiInputBase-root': {
            padding: 0,
          },
          input: {
            paddingX: '14px',
          },
          ...sx,
        }}
        slotProps={{
          htmlInput: {
            maxLength,
            ...(slotProps?.htmlInput ?? {}),
          },
          input: {
            startAdornment: props.placeholder ? (
              <InputAdornment
                position="start"
                sx={{
                  position: 'absolute',
                  left: '10px',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  pointerEvents: 'none',
                }}
              />
            ) : null,
            endAdornment: (
              <>
                {loading ? (
                  <InputAdornment position="end">
                    <CircularProgress size={20} />
                  </InputAdornment>
                ) : (
                  endAdornment
                )}
              </>
            ),
            ...(slotProps?.input ?? {}),
          },
          ...omit(slotProps ?? {}, 'input', 'htmlInput'),
        }}
      />
    </FormControl>
  );
};
