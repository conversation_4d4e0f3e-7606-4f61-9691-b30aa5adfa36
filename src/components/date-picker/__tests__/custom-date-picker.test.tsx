import React from 'react';
import { render } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import { CustomDatePicker } from '../custom-date-picker';

// Mock the cookie utility
jest.mock('utils/cookie-client', () => ({
  getCookieLocale: jest.fn(() => 'th'),
}));

// Mock the constant
jest.mock('constant/common', () => ({
  DD_MMMM_YYYY_WITH_DASH: 'DD-MMMM-YYYY',
}));

const theme = createTheme();

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider theme={theme}>
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      {children}
    </LocalizationProvider>
  </ThemeProvider>
);

describe('CustomDatePicker', () => {
  const mockOnChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render with no value', () => {
    const { container } = render(
      <TestWrapper>
        <CustomDatePicker
          value={null}
          onChange={mockOnChange}
          label="Test Date Picker"
        />
      </TestWrapper>
    );
    expect(container).toMatchSnapshot();
  });

  it('should render with a value', () => {
    const testDate = dayjs('2023-12-25');
    const { container } = render(
      <TestWrapper>
        <CustomDatePicker
          value={testDate}
          onChange={mockOnChange}
          label="Test Date Picker"
        />
      </TestWrapper>
    );
    expect(container).toMatchSnapshot();
  });

  it('should render with error state', () => {
    const { container } = render(
      <TestWrapper>
        <CustomDatePicker
          value={null}
          onChange={mockOnChange}
          label="Test Date Picker"
          error={true}
        />
      </TestWrapper>
    );
    expect(container).toMatchSnapshot();
  });

  it('should render with custom min and max dates', () => {
    const minDate = dayjs('2020-01-01');
    const maxDate = dayjs('2025-12-31');
    const { container } = render(
      <TestWrapper>
        <CustomDatePicker
          value={null}
          onChange={mockOnChange}
          label="Test Date Picker"
          minDate={minDate}
          maxDate={maxDate}
        />
      </TestWrapper>
    );
    expect(container).toMatchSnapshot();
  });

  it('should render with custom textField props', () => {
    const { container } = render(
      <TestWrapper>
        <CustomDatePicker
          value={null}
          onChange={mockOnChange}
          label="Test Date Picker"
          textFieldProps={{
            helperText: 'Please select a date',
            placeholder: 'DD-MM-YYYY'
          }}
        />
      </TestWrapper>
    );
    expect(container).toMatchSnapshot();
  });

  it('should render disabled state', () => {
    const { container } = render(
      <TestWrapper>
        <CustomDatePicker
          value={null}
          onChange={mockOnChange}
          label="Test Date Picker"
          disabled={true}
        />
      </TestWrapper>
    );
    expect(container).toMatchSnapshot();
  });
});
