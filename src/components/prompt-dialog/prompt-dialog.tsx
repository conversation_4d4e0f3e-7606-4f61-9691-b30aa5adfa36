// components/PromptDialog.tsx
'use client';

import CloseIcon from '@mui/icons-material/Close';
import HelpIcon from '@mui/icons-material/Help';
import { Box, Button, Dialog, DialogActions, DialogContent, IconButton, Slide, Typography } from '@mui/material';
import { TransitionProps } from '@mui/material/transitions';
import { useTranslations } from 'next-intl';
import React, { forwardRef, ReactNode } from 'react';

type PromptDialogProps = {
  open: boolean;
  title?: string;
  children?: ReactNode;
  withInput?: boolean;
  defaultValue?: string;
  inputLabel?: string;
  onConfirm: (value?: string) => void;
  onClose: () => void;
  content?: string;
  cancelText?: string;
  confirmText?: string;
};

const Transition = forwardRef(function Transition(
  props: TransitionProps & { children: React.ReactElement },
  ref: React.Ref<unknown>
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

export const PromptDialog: React.FC<PromptDialogProps> = ({
  onConfirm,
  onClose,
  open,
  title,
  content,
  cancelText,
  confirmText,
}) => {
  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  const commonT = useTranslations('common');

  return (
    <Dialog fullWidth open={open} TransitionComponent={Transition} onClose={onClose} disableScrollLock>
      <DialogContent sx={{ display: 'flex', gap: '12px' }}>
        <HelpIcon color="primary" sx={{ width: '58px', height: '58px' }} />

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
          <Typography variant="body1" component="p" fontSize="18px" fontWeight={600}>
            {title ?? commonT('confirmation')}
          </Typography>
          <Typography variant="body1" component="p" fontSize="16px" fontWeight={400}>
            {content ?? commonT('confirmation-placeholder')}
          </Typography>
        </Box>

        <IconButton onClick={onClose} size="small" sx={{ position: 'absolute', top: '16px', right: '16px' }}>
          <CloseIcon fontSize="small" />
        </IconButton>
      </DialogContent>

      <DialogActions sx={{ display: 'flex', gap: '12px', pb: '20px', pr: '20px' }}>
        <Button variant="outlined" onClick={onClose} sx={{ width: '120px' }}>
          {cancelText ?? commonT('cancel-modal-btn')}
        </Button>
        <Button onClick={handleConfirm} variant="contained" sx={{ width: '120px' }}>
          {confirmText ?? commonT('confirm-modal-btn')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
