import { Box, Typography } from "@mui/material";
import { useTranslations } from "next-intl";
import Image from 'next/image';
import emptyStateUrl from 'assets/icons/empty-state.svg';

export const CustomNoRowsOverlay = () => {
  const commonT = useTranslations('common');

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <Image src={emptyStateUrl} alt="empty-state" width={140} height={140} />
      <Typography variant="body1" color="text.secondary">
        {commonT('data-not-found')}
      </Typography>
    </Box>
  );
};
