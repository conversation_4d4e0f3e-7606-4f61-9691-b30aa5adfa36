import {
  Box,
  MenuItem,
  Pagination,
  PaginationItem,
  Select,
  SelectChangeEvent,
  Typography,
  useTheme,
} from '@mui/material';
import {
  gridPageCountSelector,
  gridPageSelector,
  gridPageSizeSelector,
  useGridApiContext,
  useGridSelector,
} from '@mui/x-data-grid';
import { PAGE_SIZE_OPTIONS } from 'constant/common';
import { useTranslations } from 'next-intl';
import { FC } from 'react';
import { DoubleLeftArrowIcon, LeftArrowIcon } from 'assets/react-icons';

export const FirstPageIcon = () => <DoubleLeftArrowIcon sx={{ width: '12px', height: '12px' }} />;
export const LastPageIcon = () => (
  <DoubleLeftArrowIcon
    sx={{
      width: '12px',
      height: '12px',
      transform: 'rotateY(-180deg)',
    }}
  />
);
export const PreviousPageIcon = () => <LeftArrowIcon sx={{ width: '12px', height: '12px' }} />;
export const NextPageIcon = () => (
  <LeftArrowIcon
    sx={{
      width: '12px',
      height: '12px',
      transform: 'rotateY(-180deg)',
    }}
  />
);

export const CustomPagination: FC<{
  showSelectedRow?: boolean;
  rowSizeOptions?: number[];
}> = ({ showSelectedRow, rowSizeOptions }) => {
  const apiRef = useGridApiContext();
  const page = useGridSelector(apiRef, gridPageSelector);
  const pageCount = useGridSelector(apiRef, gridPageCountSelector);
  const pageSize = useGridSelector(apiRef, gridPageSizeSelector);
  const commonT = useTranslations('common');
  const theme = useTheme();

  const handlePageSizeChange = (event: SelectChangeEvent<number>) => {
    apiRef.current.setPageSize(Number(event.target.value));
  };

  const selectedRowLength = apiRef.current.getSelectedRows().size;

  return (
    <Box display="flex" alignItems="center" justifyContent="space-between" p={2} width="100%" gap={2}>
      <Box display="flex" alignItems="center">
        <Select<number>
          value={pageSize}
          renderValue={(value) => `${value} ${commonT('records-per-page')}`}
          onChange={handlePageSizeChange}
          size="small"
          sx={{
            height: 32,
            '.MuiSelect-select.MuiOutlinedInput-input': {
              fontSize: '14px',
              lineHeight: '140%',
              textTransform: 'lowercase',
            },
          }}
        >
          {(rowSizeOptions || PAGE_SIZE_OPTIONS).map((size) => (
            <MenuItem key={size} value={size}>
              {size}
            </MenuItem>
          ))}
        </Select>
      </Box>

      {showSelectedRow && (
        <Typography
          sx={{
            fontWeight: 500,
            color: theme.palette.customColors.neutral500,
            fontSize: '14px',
            textTransform: 'lowercase',
          }}
        >
          {selectedRowLength} {commonT('record-selected')}
        </Typography>
      )}

      <Pagination
        variant="outlined"
        shape="rounded"
        page={page + 1}
        count={pageCount}
        showFirstButton
        showLastButton
        renderItem={(props2) => (
          <PaginationItem
            sx={{
              width: '36px',
              height: '36px',
              padding: '12px',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              borderRadius: '4px',
              fontSize: '14px',
              lineHeight: '140%',
              border: `1px solid ${theme.palette.customColors.gray5}`,
              '&.Mui-selected': {
                borderColor: theme.palette.customColors.black,
                color: theme.palette.customColors.black,
                backgroundColor: theme.palette.customColors.white,
                '&:hover': {
                  backgroundColor: theme.palette.customColors.white,
                  borderColor: theme.palette.customColors.black,
                },
              },
            }}
            slots={{
              first: FirstPageIcon,
              last: LastPageIcon,
              previous: PreviousPageIcon,
              next: NextPageIcon,
            }}
            {...props2}
          />
        )}
        onChange={(event: React.ChangeEvent<unknown>, value: number) => apiRef.current.setPage(value - 1)}
      />
    </Box>
  );
};
