/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import {
  Box,
  CircularProgress,
  InputAdornment,
  Pagination,
  PaginationItem,
  SxProps,
  Theme,
  useTheme,
} from '@mui/material';
import { useTranslations } from 'next-intl';
import { ComponentProps, FC } from 'react';
import { useFormContext } from 'react-hook-form';
import SearchIcon from '@mui/icons-material/Search';
import { DebounceSearchInput } from 'components/input-form';
import { SelectAvailableQrSchema } from './schema';
import { FormRadioGroup } from 'components/hook-form';
import { FirstPageIcon, LastPageIcon, NextPageIcon, PreviousPageIcon } from 'components/event-table/custom-pagination';

type Props = {
  loading: boolean;
  search: string;
  onSearchChange: (search: string) => void;
  page: number;
  onPageChange: (page: number) => void;
  totalPage: number;
  options: ComponentProps<typeof FormRadioGroup>['options'];
};

export const AvailableQrList: FC<Props> = (props) => {
  const { loading, search, onSearchChange, page, onPageChange, totalPage, options } = props;

  const theme = useTheme();
  const qrT = useTranslations('qr');

  const {
    control,
    formState: { errors },
  } = useFormContext<SelectAvailableQrSchema>();

  return (
    <>
      <DebounceSearchInput
        placeholder={qrT('search-placeholder')}
        value={search}
        onChange={onSearchChange}
        startAdornment={
          <InputAdornment position="start">
            <SearchIcon />
          </InputAdornment>
        }
        sx={{ height: '48px', width: '100%' }}
      />
      {loading ? (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            width: '100%',
            height: '480px',
          }}
        >
          <CircularProgress />
        </Box>
      ) : (
        <>
          <Box
            sx={{
              flex: 1,
              overflowX: 'hidden',
              overflowY: 'auto',
              mt: 3,
              mb: 2,
            }}
          >
            <FormRadioGroup name="id" control={control} errors={errors} options={options} sx={radioSx(theme)} />
          </Box>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
            }}
          >
            <Pagination
              variant="outlined"
              shape="rounded"
              count={totalPage}
              page={page}
              onChange={(_, p: number) => {
                onPageChange(p);
              }}
              showFirstButton
              showLastButton
              renderItem={(p) => (
                <PaginationItem
                  sx={paginationSx(theme)}
                  slots={{
                    first: FirstPageIcon,
                    last: LastPageIcon,
                    previous: PreviousPageIcon,
                    next: NextPageIcon,
                  }}
                  {...p}
                />
              )}
            />
          </Box>
        </>
      )}
    </>
  );
};

const radioSx = (theme: Theme): SxProps => ({
  '& .MuiFormControlLabel-root': {
    background: 'white',
    mb: 3,
    display: 'flex',
    gap: 1.5,
    width: '100%',
    borderRadius: '8px',
    padding: '16px 16px 16px 9px',
    border: `1px solid transparent`,
    boxShadow: '0 1px 2px -1px rgba(0, 0, 0, 0.10), 0 1px 3px 0 rgba(0, 0, 0, 0.10)',
  },
  '& .MuiFormControlLabel-root:has(.Mui-checked)': {
    padding: '16px 16px 16px 2px',
    borderTop: `1px solid ${theme.palette.primary.main}`,
    borderRight: `1px solid ${theme.palette.primary.main}`,
    borderBottom: `1px solid ${theme.palette.primary.main}`,
    borderLeft: `8px solid ${theme.palette.primary.main}`,
  },
  '& .Mui-disabled': {
    cursor: 'not-allowed',
    background: theme.palette.customColors.neutral100,
  },
});

const paginationSx = (theme: Theme): SxProps => ({
  width: '36px',
  height: '36px',
  padding: '12px',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  borderRadius: '4px',
  fontSize: '14px',
  lineHeight: '140%',
  border: `1px solid ${theme.palette.customColors.gray5}`,
  '&.Mui-selected': {
    borderColor: theme.palette.customColors.black,
    color: theme.palette.customColors.black,
    backgroundColor: theme.palette.customColors.white,
    '&:hover': {
      backgroundColor: theme.palette.customColors.white,
      borderColor: theme.palette.customColors.black,
    },
  },
});
