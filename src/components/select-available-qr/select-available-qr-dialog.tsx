/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import {
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogProps,
  Divider,
  IconButton,
  SxProps,
  Typography,
  useTheme,
} from '@mui/material';
import { useTranslations } from 'next-intl';
import { FC, useMemo, useState } from 'react';
import { FormProvider, useForm, useFormContext } from 'react-hook-form';
import { selectAvailableQrSchema, SelectAvailableQrSchema } from './schema';
import { EventStatusEnum, QrDetail } from 'types';
import { getAvailableQrLabelsQueryKey, useAvailableQrLabelsQuery } from 'hooks/queries/useAvailableQrLabelsQuery';
import { formatDate, removeEmptyFields } from 'utils';
import { formatOrchardNo } from 'utils/format';
import { QrExtraContents } from 'containers/qr-code-management/view-qr/qr-extra-contents';
import CloseIcon from '@mui/icons-material/Close';
import { getQrDetail } from 'services/qr.service';
import { useQueryClient } from '@tanstack/react-query';
import toastMessages from 'utils/toastMessages';
import { AvailableQrList } from './available-qr-list';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { ViewQrDetail } from './view-qr-detail';

const PAGE_SIZE = 3;

type SelectAvailableQrDialogState = {
  open: boolean;
  disabledIds?: string[];
  onSelect?: (data: { qrUrl: string; data: QrDetail }) => void;
};

const initialSelectAvailableQrDialogState: SelectAvailableQrDialogState = {
  open: false,
};

export const useSelectAvailableQrDialogProps = () => {
  const [dialogState, setDialogState] = useState<SelectAvailableQrDialogState>(initialSelectAvailableQrDialogState);

  const onOpen = (state: Omit<SelectAvailableQrDialogState, 'open'>) => {
    setDialogState({ open: true, ...state });
  };

  const onClose = () => {
    setDialogState(initialSelectAvailableQrDialogState);
  };

  return {
    dialogState,
    onOpen,
    onClose,
  };
};

export const SelectAvailableQrDialog: FC<Omit<ReturnType<typeof useSelectAvailableQrDialogProps>, 'onOpen'>> = (
  props
) => {
  const form = useForm<SelectAvailableQrSchema>({
    resolver: zodResolver(selectAvailableQrSchema()),
    mode: 'onChange',
  });

  return (
    <FormProvider {...form}>
      <SelectAvailableQrDialogInner {...props} />
    </FormProvider>
  );
};

const SelectAvailableQrDialogInner: FC<Omit<ReturnType<typeof useSelectAvailableQrDialogProps>, 'onOpen'>> = (
  props
) => {
  const { dialogState, onClose } = props;

  const theme = useTheme();
  const qrT = useTranslations('qr');
  const commonT = useTranslations('common');

  const [title, setTitle] = useState<string>(qrT('available-qr-labels'));
  const [search, setSearch] = useState('');
  const [page, setPage] = useState(1);
  const [totalPage, setTotalPage] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [viewedId, setViewedId] = useState<string>('');

  const queryClient = useQueryClient();

  const queryParams = useMemo(
    () =>
      removeEmptyFields({
        page: page,
        search: search,
      }),
    [page, search]
  );

  const { data, isFetching } = useAvailableQrLabelsQuery(queryParams);

  const { handleSubmit, reset, setValue } = useFormContext<SelectAvailableQrSchema>();

  const resetForm = () => {
    reset(undefined);
  };

  const isLoading = isSubmitting || isFetching;

  const options = useMemo(() => {
    if (!data || !data.data.length) return [];

    setTotalPage(Math.ceil(data.total / PAGE_SIZE));

    const view = (qrDetail: QrDetail) => {
      setViewedId(qrDetail.id);
      setTitle(qrDetail.qrcodeId);

      // to submit form on click select-this-label button
      setValue('id', qrDetail.id);
    };

    const renderLabel = (qrDetail: QrDetail) => {
      return (
        <Box sx={{ flex: 1, display: 'flex', alignItems: 'center', gap: 1.5, color: theme.palette.text.primary }}>
          <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Box>
              <Typography fontSize={16} fontWeight={600}>
                {commonT('prefix-plot')} {formatOrchardNo(qrDetail.orchardRegisterNumber)} - {qrDetail.qrcodeId}
              </Typography>
              <Typography fontSize={12} fontWeight={400} sx={{ color: theme.palette.customColors.neutral400 }}>
                {formatDate(qrDetail.orchardRegisterNumber, 'DD/MM/YYYY HH:mm')}
              </Typography>
            </Box>
            <Divider sx={{ borderColor: theme.palette.customColors.neutralBorder }} />
            <QrExtraContents qrDetail={qrDetail} />
          </Box>
          <Button
            onClick={() => view(qrDetail)}
            variant="outlined"
            size="small"
            disabled={isLoading}
            sx={{
              px: 2,
              height: '32px',
              fontSize: '12px',
              background: theme.palette.customColors.primary50,
            }}
          >
            {qrT('view-details')}
          </Button>
        </Box>
      );
    };

    return data.data.map((availableQr) => {
      const isOptionDisabled = (dialogState.disabledIds ?? []).includes(availableQr.qrcodeId);
      return {
        value: availableQr.id,
        label: renderLabel(availableQr as unknown as QrDetail), // FIXME:
        disabled: isOptionDisabled,
      };
    });
  }, [commonT, data, isLoading, qrT, setValue, theme, dialogState.disabledIds]);

  const onSearchChange = (value: string) => {
    if (value !== search) {
      setSearch(value);
      setPage(1);
    }
  };

  const handleBackToList = () => {
    setViewedId('');
    setTitle(qrT('available-qr-labels'));
  };

  const handleBackdropClose: DialogProps['onClose'] = (_, reason) => {
    if (reason !== 'backdropClick') {
      handleClose();
    }
  };

  const handleClose = () => {
    resetForm();
    setSearch('');
    setPage(1);
    setTotalPage(1);
    setViewedId('');
    onClose();
  };

  const onSubmit = async ({ id }: SelectAvailableQrSchema) => {
    try {
      setIsSubmitting(true);

      const res = await getQrDetail(id);

      const isNotAvailableAtThisTime = res.status !== EventStatusEnum.AVAILABLE && !!res.packageAssigned;

      if (isNotAvailableAtThisTime) {
        // invalidate current page
        queryClient.invalidateQueries({
          queryKey: getAvailableQrLabelsQueryKey({ page, search }),
          exact: true,
        });
        throw new Error('The selected QR code is no more available at this time');
      }

      dialogState.onSelect?.({
        data: res,
        qrUrl: document.location.host,
      });
      setIsSubmitting(false);
      handleClose();
    } catch {
      // console.log('error', error);
      setIsSubmitting(false);
      toastMessages.error(commonT('data-outdated-error'));
    }
  };

  const renderDialogContent = () => {
    if (!viewedId) {
      return (
        <AvailableQrList
          loading={isFetching}
          search={search}
          onSearchChange={onSearchChange}
          page={page}
          onPageChange={setPage}
          totalPage={totalPage}
          options={options}
        />
      );
    }
    return <ViewQrDetail viewedId={viewedId} />;
  };

  const renderDialogActions = () => {
    if (!viewedId) {
      return (
        <>
          <Button onClick={handleClose} variant="outlined" size="large" disabled={isLoading} sx={actionSx}>
            {qrT('cancel')}
          </Button>
          <Button
            onClick={handleSubmit(onSubmit)}
            variant="contained"
            color="primary"
            size="large"
            disabled={isLoading}
            startIcon={isSubmitting ? <CircularProgress size={20} color="inherit" /> : null}
            sx={actionSx}
          >
            {qrT('confirm')}
          </Button>
        </>
      );
    }
    return (
      <>
        <Button onClick={handleBackToList} variant="outlined" size="large" disabled={isLoading} sx={actionSx}>
          {qrT('back')}
        </Button>
        <Button
          onClick={handleSubmit(onSubmit)}
          variant="contained"
          color="primary"
          size="large"
          disabled={isLoading}
          startIcon={isSubmitting ? <CircularProgress size={20} color="inherit" /> : null}
          sx={actionSx}
        >
          {qrT('select-this-label')}
        </Button>
      </>
    );
  };

  return (
    <Dialog
      open={dialogState.open}
      onClose={handleBackdropClose}
      maxWidth="md"
      fullWidth
      slotProps={{
        paper: {
          sx: { borderRadius: 1 },
        },
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          pt: 2,
          px: 3,
        }}
      >
        <Typography
          component="div"
          fontWeight={600}
          fontSize={20}
          sx={{ display: 'flex', gap: 1, alignItems: 'center' }}
        >
          {viewedId ? (
            <IconButton edge="start" onClick={handleBackToList} aria-label="back">
              <ArrowBackIcon />
            </IconButton>
          ) : null}
          {title}
        </Typography>
        <IconButton edge="end" onClick={handleClose} aria-label="close" disabled={isLoading}>
          <CloseIcon />
        </IconButton>
      </Box>

      <DialogContent
        dividers
        sx={{
          p: 3,
          pb: 0,
          border: 0,
          display: 'flex',
          flexDirection: 'column',
          overflowX: 'hidden',
          overflowY: 'auto',
        }}
      >
        {renderDialogContent()}
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2 }}>{renderDialogActions()}</DialogActions>
    </Dialog>
  );
};

const actionSx: SxProps = {
  px: 3,
  fontSize: '0.875rem',
  fontWeight: 500,
  width: 'calc(50% - 4px)',
  height: '47px',
};
