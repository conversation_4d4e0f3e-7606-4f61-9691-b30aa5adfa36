/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { Box, CircularProgress, Divider } from '@mui/material';
import { FC, useMemo, useState } from 'react';
import { useQrDetailQuery } from 'hooks/queries/useQrDetailQuery';
import { QrDetail } from 'types';
import { formatDate } from 'utils';
import { useMasterDataStore } from 'store/useMaterDataStore';
import { useGenerateQrUrlMutate } from 'hooks/queries/useGenerateQrUrlMutate';
import QrCodeReview from 'components/qr-code-review/qr-code-review';
import { formatOrchardNo } from 'utils/format';
import { QrExtraContents } from 'containers/qr-code-management/view-qr/qr-extra-contents';

type Props = {
  viewedId: string;
};

export const ViewQrDetail: FC<Props> = (props) => {
  const { viewedId } = props;

  const [url, setUrl] = useState('');

  const { mutate } = useGenerateQrUrlMutate();
  const { data, isFetching } = useQrDetailQuery(viewedId, (qrDetail) => {
    if (!qrDetail) return;
    mutate(
      {
        quantity: 1,
        nameOfExportingCompany: [qrDetail.nameOfExportingCompany || ''],
      },
      {
        onSuccess: (res) => {
          setUrl(res[0].qrUrl);
        },
      }
    );
  });

  return isFetching || !data || !url ? (
    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '480px' }}>
      <CircularProgress />
    </Box>
  ) : (
    <ViewQrDetailInner {...props} qrDetail={data} url={url} />
  );
};

const ViewQrDetailInner: FC<{
  qrDetail: QrDetail;
  url: string;
}> = (props) => {
  const { qrDetail, url } = props;

  const { getProductTypeLabel } = useMasterDataStore();

  const qrExportData = useMemo(
    () => ({
      qrUrl: url,
      exportCompany: qrDetail.nameOfExportingCompany,
      orchardRegisterNumber: `AC ${formatOrchardNo(qrDetail.orchardRegisterNumber)}`,
      packingDate: formatDate(qrDetail.packingDate * 1000),
      boxType: getProductTypeLabel(qrDetail.productType),
      exportTo: qrDetail.exportTo ?? '',
      packingHouseRegisterNumber: qrDetail.packingHouseDoaNumber ?? '',
      batchNumber: qrDetail.batchlot ?? '',
      productOf: 'Thailand',
      brand: 'Fresh Durian',
      variety: '',
      grade: '',
      totalBoxes: '',
      netWeight: '',
    }),
    [getProductTypeLabel, qrDetail, url]
  );

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      <QrExtraContents qrDetail={qrDetail} />
      <Divider sx={{ borderColor: '#F2F4F7' }} />
      <Box>
        <QrCodeReview.Container data={qrExportData} capturedEventName="qr_management_qr_code_review">
          <QrCodeReview.Label />
        </QrCodeReview.Container>
      </Box>
    </Box>
  );
};
