.loadingContainer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #ffffff;
  z-index: 9999;
}

.loadingContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.logo {
  width: 120px;
  height: auto;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3498db;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes logoAnimation {
  0% {
    transform: scale(1);
    filter: drop-shadow(0 0 0 rgba(0, 0, 0, 0.1));
    opacity: 0.8;
  }
  30% {
    transform: scale(1.15);
    filter: drop-shadow(0 6px 8px rgba(0, 0, 0, 0.15));
    opacity: 1;
  }
  60% {
    transform: scale(1.05);
    filter: drop-shadow(0 3px 4px rgba(0, 0, 0, 0.1));
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    filter: drop-shadow(0 0 0 rgba(0, 0, 0, 0.1));
    opacity: 0.8;
  }
}

.animatedLogo {
  animation: logoAnimation 2s infinite ease-in-out;
  transition: all 0.2s;
}

.logoContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 10px 15px;
  border-radius: 30px;
  flex-direction: column;
}

.loadingDots {
  display: flex;
  align-items: center;
  height: 24px;
}

.dot {
  width: 6px;
  height: 6px;
  margin: 0 3px;
  background-color: #3498db;
  border-radius: 50%;
  display: inline-block;
  opacity: 0;
  transition: all 0.2s;
}

.dot:nth-child(1) {
  animation: dotFade 1.5s infinite 0s;
}

.dot:nth-child(2) {
  animation: dotFade 1.5s infinite 0.5s;
}

.dot:nth-child(3) {
  animation: dotFade 1.5s infinite 1s;
}

@keyframes dotFade {
  0%,
  100% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}
