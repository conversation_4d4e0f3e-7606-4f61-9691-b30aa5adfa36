import { Box, Stack, styled, Typography, useTheme } from '@mui/material';
import { FORMAT_HOUR_MINUTE_DATE } from 'constant/common';
import { formatDateWithLocale } from 'containers/event/_components';
import Link from 'next/link';
import { FC, memo } from 'react';
import { clientRoutes } from 'routes/client-routes';
import useNotificationsStore from 'store/useNotificationStore';
import { NotificationType } from 'types/notification';

const StyledTypo = styled(Typography)({
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  whiteSpace: 'nowrap',
  maxWidth: '480px',
});

const ITEM_HEIGHT = 100;

const AlertItem: FC<{ alert: NotificationType; itemKey: string; showHeader?: boolean }> = memo(({ alert }) => {
  const theme = useTheme();
  const { updateSeen } = useNotificationsStore();

  // Pre-compute values to avoid recalculations in render
  const isUnread = alert.unread;
  const targetUrl = `${clientRoutes.eventIncoming}/${alert.extra?.productId ?? ''}`;

  // Handle click once instead of multiple times
  const handleClick = () => {
    updateSeen(alert.id);
  };

  return (
    <Box
      component="div"
      sx={{
        height: `${ITEM_HEIGHT}px`,
        padding: '12px',
        borderBottom: `1px solid ${theme.palette.customColors.divider}`,
        cursor: 'pointer',
        '&:hover': {
          backgroundColor: theme.palette.customColors.blueTint,
        },
      }}
      onClick={handleClick}
    >
      <Box
        sx={{
          display: 'flex',
          height: '100%',
          gap: '12px',
          px: '12px',
        }}
      >
        <Box
          component={Link}
          sx={{ flex: 1, display: 'flex', gap: '12px', textDecoration: 'none' }}
          onClick={handleClick}
          target="_blank"
          href={targetUrl}
        >
          <Stack gap={0.5}>
            <StyledTypo fontSize={20} fontWeight={600} color="textPrimary">
              {alert.title}
            </StyledTypo>
            <StyledTypo fontSize={16} color="textSecondary" fontWeight={500}>
              {alert.description}
            </StyledTypo>
            <StyledTypo fontSize={14} color="textSecondary" fontWeight={500}>
              {formatDateWithLocale(alert.createdTime, FORMAT_HOUR_MINUTE_DATE)}
            </StyledTypo>
          </Stack>
        </Box>
        <Box
          sx={{
            width: '20',
            display: 'flex',
          }}
        >
          {isUnread && (
            <Box
              sx={{
                width: 10,
                height: 10,
                backgroundColor: theme.palette.error.main,
                borderRadius: '50%',
                cursor: 'pointer',
                mt: '8px',
              }}
              onClick={() => updateSeen(alert.id)}
            />
          )}
        </Box>
      </Box>
    </Box>
  );
});

AlertItem.displayName = 'AlertItem';

export const ALERT_ITEM_HEIGHT = ITEM_HEIGHT;

export { AlertItem };
