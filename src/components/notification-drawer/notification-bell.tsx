'use client';

import { keyframes } from '@mui/system';

import {
  Badge,
  Box,
  Button,
  Card,
  CardHeader,
  Fade,
  Grow,
  IconButton,
  Skeleton,
  Tooltip,
  Typography,
  useTheme,
} from '@mui/material';
import { useTranslations } from 'next-intl';
import List from 'rc-virtual-list';
import useNotificationsStore from 'store/useNotificationStore';
import { v4 } from 'uuid';

import NotificationsNoneIcon from '@mui/icons-material/NotificationsNone';
import { Drawer } from 'components/drawer';
import { AlertItem, ALERT_ITEM_HEIGHT } from './alert-item';
import { useCallback, useEffect, useState } from 'react';
import { NotificationType } from 'types/notification';
import PriorityHighOutlinedIcon from '@mui/icons-material/PriorityHighOutlined';
import { useNotificationPermission } from 'hooks/useNotificationPermission';
import DoneAllIcon from '@mui/icons-material/DoneAll';

const SCROLL_ID = v4();

const heightOfHeader = 70;
const heightOfReader = 60;

const Loading = ({ cardLength = 8, noShadow = false }) => {
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      {Array.from({ length: cardLength }).map((_, index) => (
        <Card key={index} sx={{ boxShadow: noShadow ? 'none' : undefined }}>
          <CardHeader
            avatar={<Skeleton animation="wave" variant="circular" width={40} height={40} />}
            title={<Skeleton animation="wave" height={20} width="80%" style={{ marginBottom: 6 }} />}
            subheader={<Skeleton animation="wave" height={20} width="40%" />}
          />
        </Card>
      ))}
    </Box>
  );
};

const bounce = keyframes`
  0%, 100% { transform: scale(1); }
  50%      { transform: scale(1.2); }
`;

type CustomNotificationType = NotificationType & { showHeader?: boolean };

export const NotificationBell: React.FC = () => {
  const { getMore, hasMore, data, firstTimeLoading, openNoti, closeNoti, isOpenNoti, newNotiCount, markAsAllRead } =
    useNotificationsStore();

  const { permission } = useNotificationPermission();

  const [bellBounce, setBellBounce] = useState(false);
  const theme = useTheme();
  const t = useTranslations('common');
  const profileT = useTranslations('profile');
  const notificationsT = useTranslations('notifications');

  const [containerHeight, setContainerHeight] = useState(() => window.innerHeight - heightOfHeader - heightOfReader);
  const [loadingMore, setLoadingMore] = useState(false);

  useEffect(() => {
    if (newNotiCount > 0) {
      setBellBounce(true);
      const timer = setTimeout(() => setBellBounce(false), 300);
      return () => clearTimeout(timer);
    }
  }, [newNotiCount]);

  useEffect(() => {
    const onResize = () => setContainerHeight(window.innerHeight - heightOfHeader - heightOfReader);
    window.addEventListener('resize', onResize);
    return () => window.removeEventListener('resize', onResize);
  }, []);

  const itemHeight = ALERT_ITEM_HEIGHT;

  // Optimize onScroll with useCallback
  const onScroll = useCallback(
    (e: React.UIEvent<HTMLElement, UIEvent>) => {
      if (loadingMore || !hasMore) return;

      const { scrollHeight, scrollTop, clientHeight } = e.currentTarget;
      // Reduce the threshold to load earlier
      const threshold = scrollHeight * 0.8;

      if (scrollTop + clientHeight >= threshold) {
        setLoadingMore(true);
        getMore().finally(() => {
          setLoadingMore(false);
        });
      }
    },
    [getMore, hasMore, loadingMore]
  );

  return (
    <>
      <IconButton
        color="inherit"
        onClick={openNoti}
        sx={bellBounce ? { animation: `${bounce} 300ms ease-in-out` } : undefined}
      >
        <Badge badgeContent={newNotiCount > 99 ? '99+' : newNotiCount} color="error">
          <NotificationsNoneIcon sx={{ color: theme.palette.customColors.black }} />
        </Badge>
      </IconButton>
      {permission === 'denied' && (
        <Fade in={true}>
          <Tooltip title={notificationsT('description')}>
            <PriorityHighOutlinedIcon color="warning" />
          </Tooltip>
        </Fade>
      )}

      <Drawer anchor="right" open={isOpenNoti} onClose={closeNoti} drawerTitle={t('notification')} hasActionBtn={false}>
        <Box
          sx={{
            height: `${heightOfReader}px`,
            width: '100%',
            display: 'flex',
            justifyContent: 'flex-end',
            gap: '8px',
            alignItems: 'center',
            borderBottom: `1px solid ${theme.palette.customColors.divider}`,
            p: '20px 0',
            fontSize: '18px',
          }}
          component="div"
        >
          <Button variant="text" sx={{ display: 'flex', gap: '12px', height: '60px' }} onClick={markAsAllRead}>
            <DoneAllIcon color="primary" fontSize="inherit" />
            <Typography fontSize="inherit" color="primary">
              {profileT('mark-all-read')}
            </Typography>
          </Button>
        </Box>
        <Box
          id={SCROLL_ID}
          sx={{
            height: `calc(100vh - ${heightOfHeader + heightOfReader}px)`,
            overflow: 'hidden',
          }}
        >
          {firstTimeLoading ? (
            <Loading />
          ) : (
            <>
              {data.length === 0 ? (
                <Box sx={{ p: 2, textAlign: 'center' }}>
                  <Typography color="textSecondary">{t('no_notifications')}</Typography>
                </Box>
              ) : (
                <List
                  data={data}
                  height={containerHeight}
                  itemHeight={itemHeight}
                  itemKey="id"
                  onScroll={onScroll}
                  prefixCls="virtual-list"
                  virtual={true}
                >
                  {(alert: CustomNotificationType, index) => (
                    <Grow in={isOpenNoti} timeout={100 + index * 10} key={alert.id}>
                      <Box>
                        <AlertItem key={alert.id} alert={alert} itemKey={alert.id} showHeader={alert.showHeader} />
                      </Box>
                    </Grow>
                  )}
                </List>
              )}

              {loadingMore && (
                <Box sx={{ p: 2 }}>
                  <Loading cardLength={1} noShadow />
                </Box>
              )}
            </>
          )}
        </Box>
      </Drawer>
    </>
  );
};
