import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { EventBox } from '../event-box';

// Mock the ImageReviewModal component
jest.mock('components/image-review-modal', () => ({
  ImageReviewModal: jest.fn(({ imageUrl, isZoomImage }) => (
    <div data-testid="image-review-modal">
      Image Modal - URL: {imageUrl || 'null'} - Zoom: {isZoomImage ? 'true' : 'false'}
    </div>
  )),
}));

// Mock theme
jest.mock('styles/theme', () => ({
  theme: {
    palette: {
      grey: { 300: '#e0e0e0' },
      primary: { main: '#1976d2' },
      customColors: { blueTint: '#f5f9ff' },
    },
  },
}));

describe('EventBox', () => {
  const defaultProps = {
    title: 'Test Title',
    description: 'Test Description',
  };

  it('renders title and description correctly', () => {
    render(<EventBox {...defaultProps} />);

    expect(screen.getByText('Test Title')).toBeInTheDocument();
    expect(screen.getByText('Test Description')).toBeInTheDocument();
  });

  it('renders custom description when provided', () => {
    const customDescription = <span>Custom Description Element</span>;

    render(<EventBox {...defaultProps} customDescription={customDescription} />);

    expect(screen.getByText('Custom Description Element')).toBeInTheDocument();
  });

  it('calls onClick when clicked and onClick is provided', () => {
    const handleClick = jest.fn();

    render(<EventBox {...defaultProps} onClick={handleClick} />);

    const clickableArea = screen.getByText('Test Title').closest('div');
    fireEvent.click(clickableArea!);

    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('does not call onClick when no onClick is provided', () => {
    render(<EventBox {...defaultProps} />);

    const titleElement = screen.getByText('Test Title');
    expect(titleElement).toBeInTheDocument();
    // Should not throw error when clicked without onClick
  });

  it('renders ImageReviewModal component', () => {
    render(<EventBox {...defaultProps} />);

    expect(screen.getByTestId('image-review-modal')).toBeInTheDocument();
  });

  it('passes imageUrl to ImageReviewModal when provided', () => {
    const imageUrl = 'https://example.com/image.jpg';

    render(<EventBox {...defaultProps} imgUrl={imageUrl} />);

    expect(screen.getByText(/URL: https:\/\/example\.com\/image\.jpg/)).toBeInTheDocument();
  });

  it('passes isZoomImage as true by default', () => {
    render(<EventBox {...defaultProps} />);

    expect(screen.getByText(/Zoom: true/)).toBeInTheDocument();
  });

  it('passes isZoomImage as false when inDrawer is true', () => {
    render(<EventBox {...defaultProps} inDrawer={true} />);

    expect(screen.getByText(/Zoom: false/)).toBeInTheDocument();
  });

  it('applies border bottom when hasBorderBottom is true', () => {
    render(<EventBox {...defaultProps} hasBorderBottom={true} />);

    const container = screen.getByText('Test Title').closest('div')?.parentElement;
    expect(container).toHaveStyle({
      borderBottom: '1px solid #e0e0e0',
    });
  });

  it('applies custom padding when provided', () => {
    const customPadding = '24px 32px';

    render(<EventBox {...defaultProps} padding={customPadding} />);

    const container = screen.getByText('Test Title').closest('div')?.parentElement;
    expect(container).toHaveStyle({
      padding: customPadding,
    });
  });

  it('applies default padding when not provided', () => {
    render(<EventBox {...defaultProps} />);

    const container = screen.getByText('Test Title').closest('div')?.parentElement;
    expect(container).toHaveStyle({
      padding: '16px 20px',
    });
  });

  it('applies cursor pointer when onClick is provided', () => {
    const handleClick = jest.fn();

    render(<EventBox {...defaultProps} onClick={handleClick} />);

    const container = screen.getByText('Test Title').closest('div')?.parentElement;
    expect(container).toHaveStyle({
      cursor: 'pointer',
    });
  });

  it('handles null imgUrl correctly', () => {
    render(<EventBox {...defaultProps} imgUrl={null} />);

    expect(screen.getByText(/URL: null/)).toBeInTheDocument();
  });

  it('handles undefined imgUrl correctly', () => {
    render(<EventBox {...defaultProps} />);

    expect(screen.getByText(/URL: null/)).toBeInTheDocument();
  });

  it('renders without crashing with minimal props', () => {
    expect(() => {
      render(<EventBox title="" description="" />);
    }).not.toThrow();
  });
});
