'use client';

import React, { useState, useLayoutEffect, MouseEvent } from 'react';
import { Box, Menu, MenuItem, ListItemIcon, ListItemText, Button, Typography, useTheme } from '@mui/material';
import { useTranslations } from 'next-intl';
import { setCookieLocale } from 'utils/cookie-client';
import { getCookieLocaleService, updateCookieLocaleService } from 'services/internal.service';

const flags: Record<string, string> = {
  en: '🇬🇧',
  th: '🇹🇭',
};

export const LanguagePicker: React.FC = () => {
  const t = useTranslations('language');
  const LANGUAGES = [
    { value: 'en', label: `${t('english')} (EN)` },
    { value: 'th', label: `${t('thai')} (TH)` },
  ];

  const [locale, setLocale] = useState<string>('');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const theme = useTheme();

  useLayoutEffect(() => {
    getCookieLocaleService().then((response) => {
      if (response.locale) {
        setCookieLocale(response.locale);
        setLocale(response.locale);
      }
    });
  }, []);

  const handleOpen = (event: MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleSelect = async (newLocale: string) => {
    await updateCookieLocaleService(newLocale);
    setCookieLocale(newLocale);
    window.location.reload();
  };

  return (
    <Box
      sx={{
        width: '200px',
        visibility: locale ? 'visible' : 'hidden',
        border: `1px solid ${theme.palette.customColors.neutralBorder}`,
        borderRadius: '8px',
      }}
    >
      <Button variant="text" onClick={handleOpen} sx={{ height: '42px', width: '100%' }}>
        <Typography variant="body1">{flags[locale]}</Typography>
        <Typography variant="body1" fontWeight="bold" ml="8px">
          {LANGUAGES.find((it) => it.value === locale)?.label || ''}
        </Typography>
      </Button>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        disableScrollLock
      >
        {LANGUAGES.map(({ value, label }) => (
          <MenuItem
            key={value}
            selected={value === locale}
            onClick={() => {
              handleSelect(value);
              handleClose();
            }}
          >
            <ListItemIcon>
              <Box component="span" sx={{ fontSize: '16px' }}>
                {flags[value]}
              </Box>
            </ListItemIcon>
            <ListItemText>{label}</ListItemText>
          </MenuItem>
        ))}
      </Menu>
    </Box>
  );
};

export default LanguagePicker;
