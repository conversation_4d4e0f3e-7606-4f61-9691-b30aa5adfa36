import { toPng, toJpeg } from 'html-to-image';
import { PRINT_FONT, QR_BOX_HEIGHT, QR_RATIO } from './constant';
import dayjs, { Dayjs } from 'dayjs';

const circleCache = new Map<string, string>();

export function generateCircleWithNumber(number: string | number, size = 100, dpi = 2): string {
  // Create cache key
  const cacheKey = `${number}-${size}-${dpi}`;

  // Return cached result if available
  if (circleCache.has(cacheKey)) {
    return circleCache.get(cacheKey)!;
  }

  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d', {
    alpha: true,
    desynchronized: true,
  });

  if (!ctx) return '';

  // Scale canvas size by DPI
  const scaledSize = size * dpi;
  canvas.width = scaledSize;
  canvas.height = scaledSize;

  // Scale context for high DPI
  ctx.scale(dpi, dpi);

  // Enable optimizations
  ctx.imageSmoothingEnabled = true;
  ctx.imageSmoothingQuality = 'high';

  // Draw white circle with optimized path
  ctx.fillStyle = '#ffffff';
  ctx.beginPath();
  ctx.arc(size / 2, size / 2, size / 2, 0, Math.PI * 2);
  ctx.fill();

  // Draw text with optimized settings
  ctx.fillStyle = 'black';
  ctx.font = `bold ${size * 0.6}px Arial, sans-serif`;
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillText(number.toString(), size / 2, size / 2);

  const dataUrl = canvas.toDataURL('image/png', 1.0);

  // Cache the result
  circleCache.set(cacheKey, dataUrl);

  // Clean up canvas
  canvas.width = 0;
  canvas.height = 0;

  return dataUrl;
}

export const downloadQrFullLabel = async (fullContentRef: React.RefObject<HTMLDivElement>, batchNumber: string) => {
  if (!fullContentRef.current) return;

  const dpi = 900;
  const widthCm = 8;
  const inchPerCm = 2.54;

  const targetWidth = Math.round((widthCm / inchPerCm) * dpi);

  const currentWidth = QR_BOX_HEIGHT * QR_RATIO;
  const currentHeight = QR_BOX_HEIGHT;

  const pixelRatio = targetWidth / currentWidth;

  const rounded = Math.ceil(pixelRatio * 10) / 10;

  const dataUrl = await toJpeg(fullContentRef.current, {
    cacheBust: true,
    skipFonts: false,
    skipAutoScale: true,
    quality: 1.0,
    width: currentWidth,
    height: currentHeight,
    pixelRatio: rounded,
    style: {
      transformOrigin: 'center center',
      overflow: 'hidden',
      boxSizing: 'border-box',
      padding: '12px',
      textRendering: 'optimizeLegibility',
      fontFamily: PRINT_FONT,
      fontWeight: '700',
      lineHeight: '30px',
    },
    backgroundColor: '#ffffff',
  });

  const link = document.createElement('a');
  link.download = `${batchNumber}.jpeg`;
  link.href = dataUrl;
  link.click();
};

export const downloadQrCodeOnly = async (contentRef: React.RefObject<HTMLDivElement>, batchNumber: string) => {
  const width = contentRef.current.clientWidth;

  const dataUrl = await toPng(contentRef.current, {
    cacheBust: true,
    skipFonts: false,
    skipAutoScale: true,
    width: width,
    height: width,
    pixelRatio: 5,
    quality: 1.0,
    style: {
      transformOrigin: 'center center',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      boxSizing: 'border-box',
    },
    backgroundColor: '#ffffff',
  });

  const link = document.createElement('a');
  link.download = `${batchNumber}.png`;
  link.href = dataUrl;
  link.click();
};

export const convertStringToTimeUnix = (date?: null | string | number | Dayjs): number => {

  if (!date) return 0;

  if (typeof date === 'object' && dayjs.isDayjs(date)) {
    return date.unix();
  }

  if (typeof date === 'object' && dayjs(date).isValid()) {
    return  dayjs(date).unix();
  }

  if (typeof date === 'string') {
    return dayjs(date).unix();
  }

  return date;
};
