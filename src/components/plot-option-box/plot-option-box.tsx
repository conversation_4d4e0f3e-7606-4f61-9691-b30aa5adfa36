import { Box, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';
import { FC, ReactNode } from 'react';
import { theme } from 'styles/theme';
import { formatGap, formatNumberWithCommas, formatPlotId } from 'utils';

interface PlotOptionBoxProps {
  plotNumber: string;
  plotGap: string;
  plotArea: number;
  plotAreaUnit: string;
  plotId: string;
  onClick?: () => void;
  renderActionButtons?: () => ReactNode;
  isError?: boolean;
}

const DEFAULT_VALUE = '--';

export const PlotOptionBox: FC<PlotOptionBoxProps> = ({
  plotNumber,
  plotGap,
  plotArea,
  plotId,
  plotAreaUnit,
  onClick,
  renderActionButtons,
  isError = false,
}) => {
  const commonTranslation = useTranslations('common');

  return (
    <Box
      component="div"
      onClick={onClick}
      sx={{
        width: '100%',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderRadius: '12px',
        flexDirection: 'row',
        gap: 4,
        padding: '20px',
        bgcolor: theme.palette.customColors.primary50,
        border: `1px solid ${isError ? theme.palette.customColors.toastError : 'transparent'}`,
      }}
    >
      <Box sx={{ display: 'flex', flex: 1, flexDirection: 'column', gap: '8px', textAlign: 'start' }}>
        <Typography component="p" variant="body2" fontWeight="bold">
          {plotNumber}
        </Typography>
        <Typography component="p" variant="caption" color="text.secondary">
          {commonTranslation('id')} - {formatPlotId(plotId) || DEFAULT_VALUE}
        </Typography>
        <Typography component="p" variant="caption" color="text.secondary">
          {commonTranslation('gap')} -{' '}
          {plotGap ? `${commonTranslation('prefix-plot')} ${formatGap(plotGap)}` : DEFAULT_VALUE}
        </Typography>
        <Typography component="p" variant="caption" color="text.secondary">
          {commonTranslation('area')} - {formatNumberWithCommas(plotArea) || DEFAULT_VALUE}{' '}
          {plotAreaUnit || DEFAULT_VALUE}
        </Typography>
      </Box>
      {renderActionButtons?.()}
    </Box>
  );
};
