import React, { useState } from 'react';
import { Box, Breadcrumbs as MuiBreadcrumbs, Typography } from '@mui/material';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import { useTranslations } from 'next-intl';
import { Dialog } from 'components';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface BreadcrumbItem {
  label: string;
  href: string;
  showConfirmation?: boolean;
  onClick?: () => void;
}

interface BreadcrumbsProps {
  items: BreadcrumbItem[];
  maxItems?: number;
  className?: string;
}

export const Breadcrumbs: React.FC<BreadcrumbsProps> = ({ items, maxItems = 8, className }) => {
  const commonTranslations = useTranslations('common');
  const [dialogOpen, setDialogOpen] = useState(false);
  const router = useRouter();
  const [pendingNavigation, setPendingNavigation] = useState<string | null>(null);
  const [pendingAction, setPendingAction] = useState<(() => void) | null>(null);

  const handleLinkClick = (event: React.MouseEvent<HTMLAnchorElement, MouseEvent>, item: BreadcrumbItem) => {
    if (item.showConfirmation) {
      event.preventDefault();
      setPendingNavigation(item.href);
      setPendingAction(item.onClick || null);
      setDialogOpen(true);
      return;
    } else if (item.onClick) {
      event.preventDefault();
      item.onClick();
      return;
    }
    return router.push(item.href)
  };

  const handleConfirmNavigation = () => {
    setDialogOpen(false);
    if (pendingAction) {
      pendingAction();
    } else if (pendingNavigation) {
      router.push(pendingNavigation);
    }
    setPendingNavigation(null);
    setPendingAction(null);
  };

  const handleCancelNavigation = () => {
    setDialogOpen(false);
    setPendingNavigation(null);
    setPendingAction(null);
  };

  return (
    <>
      <MuiBreadcrumbs
        separator={<NavigateNextIcon fontSize="small" />}
        aria-label="breadcrumb"
        maxItems={maxItems}
        className={className}
      >
        {items.map((item, index) => {
          const isLast = index === items.length - 1;

          return isLast ? (
            <Typography key={item.label} color="text.primary">
              {item.label}
            </Typography>
          ) : (
            <Box
              component={Link}
              key={item.label}
              color="primary"
              href={item.href}
              onClick={(e) => handleLinkClick(e, item)}
            >
              {item.label}
            </Box>
          );
        })}
      </MuiBreadcrumbs>
      <Dialog
        isOpen={dialogOpen}
        title={commonTranslations('reminder-title')}
        content={commonTranslations('reminder-content')}
        okButtonText={commonTranslations('ok-modal-btn')}
        cancelButtonText={commonTranslations('cancel-modal-btn')}
        onConfirm={handleConfirmNavigation}
        onCancel={handleCancelNavigation}
      />
    </>
  );
};
