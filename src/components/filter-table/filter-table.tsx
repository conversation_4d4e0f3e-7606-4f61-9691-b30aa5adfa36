'use client';
import { Close } from '@mui/icons-material';
import SearchIcon from '@mui/icons-material/Search';
import { Box, Button, Divider, IconButton, InputAdornment, Popover, Typography, useTheme } from '@mui/material';
import { CustomDatePicker } from 'components/date-picker/custom-date-picker';
import dayjs, { Dayjs } from 'dayjs';
import { useTranslations } from 'next-intl';
import { FC, useState } from 'react';
import { DebounceSearchInput } from 'components/input-form';
import { MultiSelectFilterButtonSelection } from './multi-select-filter-button-selection';
import { RecordedByEnum } from 'types';
import filterSrc from 'assets/icons/filter.svg';
import reloadSrc from 'assets/icons/reload.svg';
import Image from 'next/image';
import farmerSrc from 'assets/icons/user.svg';
import cutterSrc from 'assets/icons/knife.svg';
import packingSrc from 'assets/icons/box.svg';

export interface FilterState {
  startDate: Dayjs | null;
  endDate: Dayjs | null;
  recordedBy: string[];
  status: string[];
}

interface FilterTableProps {
  searchPlaceHolder?: string;
  searchKeyword?: string;
  setSearchKeyword: (search: string) => void;
  filterId: string;
  handleApplyFilters: (filter: FilterState) => void;
  filters?: FilterState;
  hasFilterUserRole?: boolean;
  filterStatusOptions?: { label: string; value: string }[];
  refetch: () => void;
  filterDateTitle: string;
}

export const FilterTable: FC<FilterTableProps> = ({
  searchPlaceHolder,
  searchKeyword,
  setSearchKeyword,
  filterId,
  handleApplyFilters,
  filters,
  hasFilterUserRole,
  filterStatusOptions,
  refetch,
  filterDateTitle,
}) => {
  const receiveTranslation = useTranslations('receive');

  const theme = useTheme();

  const initialFilter = {
    startDate: null,
    endDate: null,
    recordedBy: [],
    status: [],
  };

  const commonT = useTranslations('common');
  const [filterAnchorEl, setFilterAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [tempFilters, setTempFilters] = useState<FilterState>(filters || initialFilter);
  const [openPicker, setOpenPicker] = useState<'start' | 'end' | null>(null);

  const handleFilterClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setFilterAnchorEl(event.currentTarget);

    if (filters) {
      setTempFilters({ ...filters });
    }
  };

  const handleFilterClose = () => {
    setFilterAnchorEl(null);
  };

  const clearFilter = () => {
    setTempFilters({ ...initialFilter });
  };

  const applyFilter = () => {
    handleApplyFilters(tempFilters);
    handleFilterClose();
  };

  const recordByOptions = [
    {
      label: receiveTranslation('farmer'),
      value: RecordedByEnum.FARMER,
      icon: farmerSrc,
    },
    {
      label: receiveTranslation('cutter'),
      value: RecordedByEnum.CUTTER,
      icon: cutterSrc,
    },
    {
      label: receiveTranslation('packing-house'),
      value: RecordedByEnum.PACKING_HOUSE,
      icon: packingSrc,
    },
  ];

  const isDisabledFilter =
    !tempFilters.startDate &&
    !tempFilters.endDate &&
    tempFilters.recordedBy.length === 0 &&
    tempFilters.status.length === 0;

  return (
    <>
      <Box sx={{ display: 'flex', mb: 2, justifyContent: 'space-between' }}>
        <DebounceSearchInput
          placeholder={searchPlaceHolder}
          value={searchKeyword}
          onChange={setSearchKeyword}
          startAdornment={
            <InputAdornment position="start">
              <SearchIcon />
            </InputAdornment>
          }
          sx={{ height: '40px', width: '300px' }}
        />
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<Image src={filterSrc} alt={''} width={20} height={20} unoptimized />}
            onClick={handleFilterClick}
            aria-describedby={filterId}
            sx={{ height: '40px', ml: 2 }}
          >
            {commonT('filter')}
          </Button>
          <Divider orientation="vertical" variant="fullWidth" flexItem />
          <Button variant="outlined" onClick={refetch} sx={{ height: '40px' }}>
            <Image src={reloadSrc} alt={''} width={20} height={20} unoptimized />
          </Button>
        </Box>
      </Box>
      <Popover
        id={filterId}
        open={Boolean(filterAnchorEl)}
        anchorEl={filterAnchorEl}
        onClose={handleFilterClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        slotProps={{
          paper: {
            sx: {
              width: '600px',
            },
          },
        }}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            borderBottom: `1px solid ${theme.palette.customColors.divider}`,
            p: 2,
          }}
        >
          <Typography variant="h6">{receiveTranslation('filter')}</Typography>
          <IconButton size="small" onClick={handleFilterClose}>
            <Close />
          </IconButton>
        </Box>

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: '12px', p: 2 }}>
          <Typography variant="subtitle2" sx={{ fontWeight: 'medium' }}>
            {filterDateTitle}
          </Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <CustomDatePicker
              sx={{ flex: 1 }}
              label={receiveTranslation('start-date')}
              value={tempFilters.startDate}
              open={openPicker === 'start'}
              onOpen={() => setOpenPicker('start')}
              onClose={() => setOpenPicker(null)}
              maxDate={tempFilters.endDate ? dayjs(tempFilters.endDate) : dayjs()}
              onChange={(date) => {
                if (!dayjs(date).startOf('day').isValid()) {
                  return;
                }
                setTempFilters({ ...tempFilters, startDate: dayjs(date).startOf('day') });
              }}
              slotProps={{
                textField: {
                  size: 'small',
                  onClick: () => setOpenPicker('start'),
                  readOnly: true,
                },
              }}
            />

            <CustomDatePicker
              sx={{ flex: 1 }}
              minDate={tempFilters?.startDate ?? undefined}
              label={receiveTranslation('end-date')}
              value={tempFilters.endDate}
              open={openPicker === 'end'}
              onOpen={() => setOpenPicker('end')}
              onClose={() => setOpenPicker(null)}
              maxDate={dayjs()}
              onChange={(date) => {
                if (!dayjs(date).endOf('day').isValid()) {
                  return;
                }
                setTempFilters({ ...tempFilters, endDate: dayjs(date).endOf('day') });
              }}
              slotProps={{
                textField: {
                  size: 'small',
                  onClick: () => setOpenPicker('end'),
                  readOnly: true,
                },
              }}
            />
          </Box>

          {hasFilterUserRole && (
            <>
              <Typography variant="subtitle2" sx={{ fontWeight: 'medium' }}>
                {receiveTranslation('recorded-by')}
              </Typography>
              <MultiSelectFilterButtonSelection
                options={recordByOptions}
                selectedValues={tempFilters.recordedBy}
                onSelectionChange={(values) => setTempFilters({ ...tempFilters, recordedBy: values })}
                totalOptionsLength={recordByOptions?.length ?? 0}
              />
            </>
          )}

          {filterStatusOptions && (
            <>
              <Typography variant="subtitle2" sx={{ fontWeight: 'medium' }}>
                {receiveTranslation('status')}
              </Typography>
              <MultiSelectFilterButtonSelection
                options={filterStatusOptions}
                selectedValues={tempFilters.status}
                onSelectionChange={(values) =>
                  setTempFilters((prevTempFilter) => ({ ...prevTempFilter, status: values }))
                }
                totalOptionsLength={filterStatusOptions.length}
              />
            </>
          )}
        </Box>

        <Box
          sx={{
            display: 'flex',
            gap: 2,
            borderTop: `1px solid ${theme.palette.customColors.divider}`,
            justifyContent: 'flex-end',
            p: 2,
          }}
        >
          <Button
            variant="outlined"
            onClick={clearFilter}
            sx={{ borderRadius: '8px', width: '150px' }}
            disabled={isDisabledFilter}
          >
            {receiveTranslation('clear-filter')}
          </Button>
          <Button variant="contained" onClick={applyFilter} sx={{ borderRadius: '8px', width: '150px' }}>
            {receiveTranslation('apply-filter')}
          </Button>
        </Box>
      </Popover>
    </>
  );
};
