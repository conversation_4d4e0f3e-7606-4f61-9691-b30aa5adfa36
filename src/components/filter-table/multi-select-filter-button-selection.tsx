import { Button, Grid } from '@mui/material';
import { FC } from 'react';
import { activeButtonSelectStyle, normalButtonSelectStyle } from './filter-table.styles';
import Image from 'next/image';

interface FilterOption {
  label: string;
  value: string;
  icon?: string;
}

interface MultiSelectFilterButtonSelectionProps {
  options: FilterOption[];
  selectedValues: string[];
  onSelectionChange: (values: string[]) => void;
  totalOptionsLength?: number;
}

export const MultiSelectFilterButtonSelection: FC<MultiSelectFilterButtonSelectionProps> = ({
  options,
  selectedValues,
  onSelectionChange,
  totalOptionsLength,
}) => {
  const itemsLength = totalOptionsLength ?? options.length;
  const size = itemsLength > 3 ? 6 : 4;

  const handleOptionClick = (value: string) => {
    const isSelected = selectedValues.includes(value);
    let newSelectedValues: string[];

    if (isSelected) {
      // Remove from selection
      newSelectedValues = selectedValues.filter(v => v !== value);
    } else {
      // Add to selection
      newSelectedValues = [...selectedValues, value];
    }

    onSelectionChange(newSelectedValues);
  };

  return (
    <Grid container spacing={2}>
      {options.map((option) => {
        const isSelected = selectedValues.includes(option.value);
        
        return (
          <Grid key={option.value} size={size}>
            <Button
              fullWidth
              sx={isSelected ? activeButtonSelectStyle : normalButtonSelectStyle}
              onClick={() => handleOptionClick(option.value)}
              startIcon={
                option.icon ? (
                  <Image src={option.icon} alt={option.label} width={16} height={16} unoptimized />
                ) : undefined
              }
            >
              {option.label}
            </Button>
          </Grid>
        );
      })}
    </Grid>
  );
};
