import { render, screen, fireEvent } from '@testing-library/react';
import { MultiSelectFilterButtonSelection } from '../multi-select-filter-button-selection';

const mockOptions = [
  { label: 'Farmer', value: 'farmer', icon: '/farmer-icon.svg' },
  { label: 'Cutter', value: 'cutter', icon: '/cutter-icon.svg' },
  { label: 'Packing House', value: 'packing_house', icon: '/packing-icon.svg' },
];

describe('MultiSelectFilterButtonSelection', () => {
  const mockOnSelectionChange = jest.fn();

  beforeEach(() => {
    mockOnSelectionChange.mockClear();
  });

  it('renders all options', () => {
    render(
      <MultiSelectFilterButtonSelection
        options={mockOptions}
        selectedValues={[]}
        onSelectionChange={mockOnSelectionChange}
      />
    );

    expect(screen.getByText('Farmer')).toBeInTheDocument();
    expect(screen.getByText('Cutter')).toBeInTheDocument();
    expect(screen.getByText('Packing House')).toBeInTheDocument();
  });

  it('shows selected state for selected values', () => {
    render(
      <MultiSelectFilterButtonSelection
        options={mockOptions}
        selectedValues={['farmer', 'cutter']}
        onSelectionChange={mockOnSelectionChange}
      />
    );

    const farmerButton = screen.getByText('Farmer').closest('button');
    const cutterButton = screen.getByText('Cutter').closest('button');
    const packingButton = screen.getByText('Packing House').closest('button');

    // Check that selected buttons have active styling
    expect(farmerButton).toHaveStyle('border-left: 8px solid rgb(59, 130, 246)');
    expect(cutterButton).toHaveStyle('border-left: 8px solid rgb(59, 130, 246)');
    expect(packingButton).not.toHaveStyle('border-left: 8px solid rgb(59, 130, 246)');
  });

  it('adds value to selection when unselected option is clicked', () => {
    render(
      <MultiSelectFilterButtonSelection
        options={mockOptions}
        selectedValues={['farmer']}
        onSelectionChange={mockOnSelectionChange}
      />
    );

    const cutterButton = screen.getByText('Cutter');
    fireEvent.click(cutterButton);

    expect(mockOnSelectionChange).toHaveBeenCalledWith(['farmer', 'cutter']);
  });

  it('removes value from selection when selected option is clicked', () => {
    render(
      <MultiSelectFilterButtonSelection
        options={mockOptions}
        selectedValues={['farmer', 'cutter']}
        onSelectionChange={mockOnSelectionChange}
      />
    );

    const farmerButton = screen.getByText('Farmer');
    fireEvent.click(farmerButton);

    expect(mockOnSelectionChange).toHaveBeenCalledWith(['cutter']);
  });

  it('handles empty selection', () => {
    render(
      <MultiSelectFilterButtonSelection
        options={mockOptions}
        selectedValues={[]}
        onSelectionChange={mockOnSelectionChange}
      />
    );

    const farmerButton = screen.getByText('Farmer');
    fireEvent.click(farmerButton);

    expect(mockOnSelectionChange).toHaveBeenCalledWith(['farmer']);
  });

  it('handles selecting all options', () => {
    render(
      <MultiSelectFilterButtonSelection
        options={mockOptions}
        selectedValues={['farmer', 'cutter']}
        onSelectionChange={mockOnSelectionChange}
      />
    );

    const packingButton = screen.getByText('Packing House');
    fireEvent.click(packingButton);

    expect(mockOnSelectionChange).toHaveBeenCalledWith(['farmer', 'cutter', 'packing_house']);
  });
});
