'use client';

import { FC, useState } from 'react';
import { Box, Typography } from '@mui/material';
import { Swiper, SwiperSlide } from 'swiper/react';

import 'swiper/css';
import 'swiper/css/pagination';
import { Image } from 'components';
import { getImageUrl } from 'utils';

interface CarouselProps {
  images: string[];
  name?: string;
  address?: string;
  description?: string;
  onBack?: () => void;
}

export const Carousel: FC<CarouselProps> = ({ images, name, description }) => {
  const [activeIndex, setActiveIndex] = useState(1);

  const imagesDomain = images.map((urlId) => getImageUrl(urlId));

  return (
    <Box
      sx={{
        position: 'relative',
        width: '100%',
        margin: 'auto',
        height: '300',
        maxWidth: '800',
        boxSizing: 'border-box',
        borderBottom: '1px solid #E5E5EA',
      }}
    >
      {/* Swiper Carousel */}
      <Swiper
        slidesPerView={1}
        spaceBetween={0}
        loop={true}
        effect="fade"
        pagination={{ clickable: true }}
        onSlideChange={(swiper) => setActiveIndex((swiper?.realIndex ?? 0) + 1)}
        style={{
          width: '100%',
          height: '300px',
        }}
      >
        {images.map((imgUrl) => (
          <SwiperSlide style={{ width: '100%', height: '400px' }} key={imgUrl}>
            <Image
              alt={imgUrl}
              style={{ objectFit: 'cover', width: '100%' }}
              sizes="100vw"
              src={imgUrl}
              width={800}
              height={'400'}
            />
          </SwiperSlide>
        ))}

        <Box
          sx={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            height: '60px',
            width: '100%',
            background:
              'linear-gradient(to bottom, rgba(200, 200, 200, 0.2), rgba(51, 51, 51, 0.2), rgba(0, 0, 0, 0.2))',
            color: '#fff',
            display: 'flex',
            alignItems: 'center',
            padding: '0 16px',
            zIndex: 5,
          }}
        >
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              width: '100%',
              height: '100%',
              alignItems: 'center',
            }}
          >
            <Box component="div" sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
              <Typography
                fontSize="14px"
                sx={{
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  display: '-webkit-box',
                  WebkitLineClamp: '1',
                  WebkitBoxOrient: 'vertical',
                  maxWidth: 'calc(100% - 60px)',
                }}
              >
                {name}
              </Typography>

              <Box
                sx={{
                  width: '100%',
                  display: 'flex',
                  justifyContent: description ? 'space-between' : 'flex-end',
                }}
              >
                {description && (
                  <Typography
                    fontSize="14px"
                    sx={{
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      display: '-webkit-box',
                      WebkitLineClamp: '1',
                      WebkitBoxOrient: 'vertical',
                      maxWidth: 'calc(100% - 60px)',
                    }}
                  >
                    {description}
                  </Typography>
                )}
                <Typography fontSize="14px">
                  {activeIndex} / {imagesDomain?.length}
                </Typography>
              </Box>
            </Box>
          </Box>
        </Box>
        {/* <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            height: '60px',
            width: '100%',
            background: 'linear-gradient(to top, rgba(200, 200, 200, 0.2), rgba(51, 51, 51, 0.2), rgba(0, 0, 0, 0.2))',
            color: '#fff',
            display: 'flex',
            alignItems: 'center',
            zIndex: 5,
          }}
        >
          <IconButton sx={{ color: '#fff' }} onClick={onBack}>
            <ArrowBackIcon />
          </IconButton>
        </Box> */}
      </Swiper>
    </Box>
  );
};
