import CloseIcon from '@mui/icons-material/Close';
import {
  Box,
  Button,
  Drawer as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  IconButton,
  Typography,
  DrawerProps as MuiDrawerProps,
  useTheme,
} from '@mui/material';
import { useTranslations } from 'next-intl';
import { FC, ReactNode } from 'react';

interface DrawerProps extends MuiDrawerProps {
  open: boolean;
  onConfirm?: () => void;
  onClose?: () => void;
  drawerTitle: string;
  hasActionBtn?: boolean;
  drawerWidth?: string;
  footerElement?: ReactNode;
  confirmButtonText?: string;
  isDisableConfirm?: boolean;
  isLoading?: boolean;
}

export const Drawer: FC<DrawerProps> = ({
  open,
  onClose,
  onConfirm,
  drawerTitle,
  hasActionBtn = true,
  drawerWidth = '550px',
  footerElement = <></>,
  children,
  confirmButtonText = '',
  isDisableConfirm,
  isLoading = false,
  ...props
}) => {
  const commonT = useTranslations('common');

  const theme = useTheme();

  return (
    <MuiDrawer
      anchor="right"
      open={open}
      onClose={onClose}
      disableScrollLock
      sx={{
        touchAction: 'none',
        overscrollBehaviorX: 'none',
        overflow: 'hidden',
      }}
      {...props}
    >
      <Box
        sx={{
          width: drawerWidth,
          height: '100dvh',
          display: 'flex',
          flexDirection: 'column',
          boxSizing: 'border-box',
          overflow: 'hidden',
        }}
      >
        <Box
          sx={{
            background: theme.palette.customColors.gradientAppBgColor,
            color: theme.palette.customColors.black,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            p: '12px 12px 0',
            boxSizing: 'border-box',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              borderBottom: `1px solid ${theme.palette.customColors.divider}`,
              width: '100%',
              justifyContent: 'space-between',
            }}
          >
            <Typography variant="body1" fontSize="22px" fontWeight={500}>
              {drawerTitle}
            </Typography>
            <IconButton onClick={onClose} color="inherit">
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        <Box sx={{ display: 'flex', flex: 1, overflowY: 'auto', flexDirection: 'column' }}>
          <Box
            component="div"
            sx={{ display: 'flex', flex: 1, flexDirection: 'column', overflowY: 'auto', maxHeight: '96dvh' }}
          >
            {children}
          </Box>

          {hasActionBtn && (
            <Box
              sx={{
                px: 2,
                py: 1.5,
                borderTop: 1,
                borderColor: 'divider',
                display: 'flex',
                justifyContent: 'space-between',
                gap: 1,
              }}
            >
              <Button fullWidth variant="outlined" onClick={onClose}>
                {commonT('cancel-modal-btn')}
              </Button>
              <Button fullWidth variant="contained" onClick={onConfirm} disabled={isDisableConfirm} loading={isLoading}>
                {confirmButtonText || commonT('confirm-modal-btn')}
              </Button>
            </Box>
          )}
          {footerElement}
        </Box>
      </Box>
    </MuiDrawer>
  );
};
