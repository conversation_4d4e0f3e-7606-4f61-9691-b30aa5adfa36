import React, { useState } from 'react';
import { Box, CardMedia, IconButton } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import VisibilityIcon from '@mui/icons-material/Visibility';
import { getInternalAssetUrl, Image, MobileDialog } from 'components';
import { useDeviceWidth } from 'hooks/useDeviceWidth';
import { useDeviceHeight } from 'hooks/useDeviceHeight';

interface ImageReviewModalProps {
  imageUrl: string | null;
  isLocalImage?: boolean;
  imageSize?: number;
  isZoomImage?: boolean;
}

export const ImageReviewModal: React.FC<ImageReviewModalProps> = ({ imageUrl, imageSize = 80, isZoomImage = true }) => {
  const [open, setOpen] = useState(false);
  const deviceWidth = useDeviceWidth();
  const deviceHeight = useDeviceHeight();
  const [onError, setOnError] = useState(false);

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <>
      <Box
        component="div"
        sx={{
          position: 'relative',
          width: `${imageSize + 2}px`,
          height: `${imageSize + 2}px`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: imageUrl && !onError ? 'pointer' : 'default',
          boxShadow: 0,
          '&:hover .overlay': {
            opacity: 1,
          },
        }}
        onClick={() => setOpen(true)}
      >
        <CardMedia
          sx={{
            height: imageSize,
            maxHeight: imageSize,
            width: imageSize,
            objectFit: 'cover',
            borderRadius: '10px',
            boxShadow: 1,
          }}
          component={'img'}
          alt={'not-found'}
          onError={(e) => {
            e.currentTarget.src = getInternalAssetUrl('/assets/images/no-image.png');
            setOnError(true);
          }}
          image={imageUrl || getInternalAssetUrl('/assets/images/no-image.png')}
        />
        {isZoomImage && imageUrl && !onError && (
          <Box
            className="overlay"
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              bgcolor: 'rgba(0, 0, 0, 0.4)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              opacity: 0,
              transition: isZoomImage ? 'opacity 0.3s ease-in-out' : 'unset',
              borderRadius: 1,
            }}
          >
            <VisibilityIcon sx={{ color: 'white', fontSize: 32 }} />
          </Box>
        )}
      </Box>

      {isZoomImage && imageUrl && !onError && (
        <MobileDialog
          sx={{
            '.MuiDialog-paper': {
              backgroundColor: 'transparent',
              boxShadow: 'none',
              overflow: 'hidden',
            },
            background: 'red',
          }}
          open={open}
          onClose={handleClose}
        >
          <Box
            sx={{
              position: 'fixed',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              bgcolor: 'rgba(0, 0, 0, 1)',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 1300,
            }}
          >
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                display: 'flex',
                justifyContent: 'end',
                alignItems: 'center',
                p: 2,
                zIndex: 10,
                bgcolor: 'black',
                height: '64px',
              }}
            >
              <IconButton onClick={handleClose} sx={{ color: 'white' }} aria-label="close">
                <CloseIcon />
              </IconButton>
            </Box>

            <Box
              sx={{
                width: '100%',
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                p: 2,
                position: 'relative',
                pt: '64px', // Add padding for header
              }}
            >
              <div
                style={{
                  position: 'relative',
                  width: '100%',
                  height: '80vh',
                  maxWidth: '100vw',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Image
                  src={imageUrl}
                  alt={imageUrl ?? ''}
                  width={Math.max(deviceWidth, 800)}
                  height={deviceHeight}
                  priority
                  unoptimized={imageUrl ? imageUrl?.startsWith('data:') || imageUrl?.startsWith('blob:') : undefined}
                  style={{
                    objectFit: 'contain',
                  }}
                  internalAsset={!imageUrl?.startsWith('https')}
                />
              </div>
            </Box>
          </Box>
        </MobileDialog>
      )}
    </>
  );
};

export default ImageReviewModal;
