import { Stack, StackProps, Typography } from '@mui/material';
import { FC, ReactNode } from 'react';
import { theme } from 'styles/theme';

interface DetailRowProps {
  title: string | ReactNode;
  content?: ReactNode;
  sx?: StackProps['sx'];
  noBorder?: boolean;
  onClick?: StackProps['onClick'];
  contentFullWidth?: boolean;
  noPadding?: boolean;
  isCapitalize?: boolean;
}

const DEFAULT_VALUE = '--';

export const DetailRow: FC<DetailRowProps> = ({
  title,
  sx,
  onClick,
  noBorder = false,
  content = '',
  contentFullWidth = false,
  noPadding,
  isCapitalize = false,
}) => {

  return (
    <Stack
      onClick={onClick}
      direction="column"
      sx={{
        justifyContent: 'space-between',
        alignItems: 'start',
        width: '100%',
        padding: noPadding ? 0 : '12px 0',
        borderBottom: noBorder ? 'none' : `1px solid ${theme.palette.customColors.gray5}`,
        ...sx,
      }}
    >
      <Typography minWidth="60px" align="center" color="textSecondary" textAlign="start">
        {title}
      </Typography>
      <Typography
        color="textPrimary"
        textAlign="start"
        fontWeight="medium"
        component="div"
        width={contentFullWidth ? '100%' : 'auto'}
        sx={{ textTransform: isCapitalize ? 'capitalize' : '' }}
      >
        {content || DEFAULT_VALUE}
      </Typography>
    </Stack>
  );
};
