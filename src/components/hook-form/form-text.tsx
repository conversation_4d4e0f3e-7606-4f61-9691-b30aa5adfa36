import { Box, FormControl, TextFieldProps, Typography } from '@mui/material';
import { TextInput } from 'components/input-form';
import { JSX, ReactElement } from 'react';
import { Control, Controller, FieldErrors, FieldValues, Path } from 'react-hook-form';

export interface FormTextInputProps<TFormValues extends FieldValues> {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  errors?: FieldErrors<TFormValues>;
  label?: string;
  required?: boolean;
  requiredMessage?: string;
  placeholder?: string;
  variant?: TextFieldProps['variant'];
  max?: number;
  min?: number;
  patternMessage?: string;
  pattern?: RegExp;
  loading?: boolean;
  minLength?: number;
  maxLength?: number;
  minLengthMessage?: string;
  maxLengthMessage?: string;
  validate?: Record<string, (value: unknown) => boolean | string>;
  endAdornment?: JSX.Element;
  fullWidth?: boolean;
  sx?: TextFieldProps['sx'];
  customBtn?: JSX.Element;
  multiline?: boolean;
  rows?: number;
  slotProps?: TextFieldProps['slotProps'];
  minRows?: number;
  maxRows?: number;
  defaultValue?: string;
}

export function FormTextInput<TFormValues extends FieldValues>({
  name,
  control,
  errors,
  label,
  required = false,
  requiredMessage,
  placeholder,
  variant = 'outlined',
  loading,
  pattern,
  patternMessage,
  minLength,
  maxLength = 100,
  minLengthMessage,
  maxLengthMessage,
  validate,
  endAdornment,
  fullWidth = true,
  sx,
  customBtn,
  multiline = false,
  rows = 1,
  slotProps = {},
  minRows,
  maxRows,
  defaultValue,
}: FormTextInputProps<TFormValues>): ReactElement {
  const rules = {
    ...(required && { required: requiredMessage || `${label} is required` }),
    ...(pattern && { pattern: { value: pattern, message: patternMessage || `Invalid format for ${label}` } }),
    ...(minLength && {
      minLength: { value: minLength, message: minLengthMessage || `${label} must be at least ${minLength} characters` },
    }),
    ...(maxLength && {
      maxLength: { value: maxLength, message: maxLengthMessage || `${label} must not exceed ${maxLength} characters` },
    }),
    ...(validate && { validate }),
  };

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field }) => {
        const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
          const raw = e.target.value;
          const trimmed = typeof raw === 'string' ? raw.trim() : raw;
          field.onChange(trimmed);
          field.onBlur();
        };

        return (
          <FormControl fullWidth={fullWidth} error={!!errors?.[name]}>
            {label && (
              <Typography sx={{ fontSize: '18px', mb: 1 }} variant="caption">
                {label}{' '}
                <Typography sx={{ visibility: required ? 'visible' : 'hidden' }} variant="caption" color="error">
                  *
                </Typography>
              </Typography>
            )}

            <Box sx={{ display: 'flex', gap: '12px' }}>
              <TextInput
                minRows={minRows}
                maxRows={maxRows}
                rows={rows}
                multiline={multiline}
                helperText={errors?.[name]?.message as string}
                endAdornment={endAdornment}
                error={!!errors?.[name]}
                required={required}
                loading={loading}
                defaultValue={defaultValue}
                name={name}
                placeholder={placeholder}
                variant={variant}
                value={field.value}
                onChange={(event) => {
                  if (maxLength && event.target.value.length > maxLength) {
                    return;
                  }
                  field.onChange(event.target.value);
                }}
                onBlur={handleBlur}
                slotProps={{
                  htmlInput: {
                    maxLength: maxLength,
                    minLength: minLength,
                  },
                  ...slotProps,
                }}
                sx={sx}
              />
              {customBtn}
            </Box>
          </FormControl>
        );
      }}
    />
  );
}
