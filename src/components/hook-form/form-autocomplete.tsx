/* eslint-disable react-hooks/exhaustive-deps */
import SearchIcon from '@mui/icons-material/Search';
import { Autocomplete, Box, FormControl, TextField, TextFieldProps, Typography, useTheme } from '@mui/material';
import { useTranslations } from 'next-intl';
import { JSX, ReactElement, useCallback, useEffect, useState } from 'react';
import { Control, Controller, FieldErrors, FieldValues, Path } from 'react-hook-form';
import { debounce } from 'lodash';

type OptionType = {
  label: string;
  value: string;
};

export interface FormAutocompleteProps<TFormValues extends FieldValues> {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  errors?: FieldErrors<TFormValues>;
  label?: string;
  required?: boolean;
  requiredMessage?: string;
  placeholder?: string;
  variant?: TextFieldProps['variant'];
  loading?: boolean;
  endAdornment?: JSX.Element;
  fullWidth?: boolean;
  sx?: TextFieldProps['sx'];
  options: OptionType[];
  disabled?: boolean;
  numberOfOpts?: number;
  onSearch?: (keyword: string) => Promise<OptionType[]>;
  debounceMs?: number;
}

export function FormAutocomplete<TFormValues extends FieldValues>({
  name,
  control,
  errors,
  label,
  required = false,
  requiredMessage,
  placeholder,
  variant = 'outlined',
  loading,
  fullWidth = true,
  options,
  disabled,
  numberOfOpts = 30,
  onSearch,
  debounceMs = 300,
}: FormAutocompleteProps<TFormValues>): ReactElement {
  const [internalLoading, setInternalLoading] = useState(false);
  const [searchOptions, setSearchOptions] = useState(options);
  const [inputValue, setInputValue] = useState('');

  const rules = {
    ...(required && { required: requiredMessage || `${label} is required` }),
  };

  const theme = useTheme();
  const commonT = useTranslations('common');

  const debouncedSearch = useCallback(
    debounce(async (keyword: string) => {
      if (onSearch && keyword.trim()) {
        setInternalLoading(true);
        try {
          const results = await onSearch(keyword);
          setSearchOptions(results);
        } catch {
          setSearchOptions([]);
        } finally {
          setInternalLoading(false);
        }
      } else {
        setSearchOptions(options);
      }
    }, debounceMs),
    [onSearch, options, debounceMs]
  );

  useEffect(() => {
    if (onSearch) {
      debouncedSearch(inputValue);
    } else {
      setSearchOptions(options);
    }
  }, [inputValue, debouncedSearch, onSearch, options]);

  const isLoading = loading || internalLoading;
  const currentOptions = onSearch ? searchOptions : options;

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field }) => {
        return (
          <FormControl
            fullWidth={fullWidth}
            error={!!errors?.[name]}
            sx={{
              color: disabled ? theme.palette.text.disabled : theme.palette.text.primary,
            }}
          >
            {label && (
              <Typography sx={{ fontSize: '18px', mb: 1 }} variant="caption">
                {label}{' '}
                <Typography sx={{ visibility: required ? 'visible' : 'hidden' }} variant="caption" color="error">
                  *
                </Typography>
              </Typography>
            )}

            <Box sx={{ display: 'flex', gap: '12px' }}>
              <Autocomplete
                id={`autocomplete-${name}`}
                options={currentOptions}
                disabled={disabled}
                fullWidth
                loadingText={commonT('loading')}
                loading={isLoading}
                noOptionsText={commonT('data-not-found')}
                inputValue={inputValue}
                onInputChange={(_, newInputValue) => {
                  setInputValue(newInputValue);
                }}
                filterOptions={
                  onSearch
                    ? (opts) => opts
                    : (opts, { inputValue: inputSearchValue }) => {

                        const lowerInput = inputSearchValue.toLowerCase();
                        // const startsWithMatches = opts.filter((option) =>
                        //   option.label.toLowerCase().startsWith(lowerInput)
                        // );

                        const searchContent = opts.filter((option) => option.label.toLowerCase().includes(lowerInput));
                        return [...searchContent].slice(0, numberOfOpts);
                      }
                }
                renderOption={(props, option, { inputValue: renderInputValue }) => {
                  const { key, ...optionProps } = props;
                  const text = option.label;
                  const searchText = onSearch ? inputValue.toLowerCase() : renderInputValue.toLowerCase();

                  if (!searchText) {
                    return (
                      <li key={key} {...optionProps}>
                        {text}
                      </li>
                    );
                  }

                  const textLower = text.toLowerCase();
                  const searchIndex = textLower.indexOf(searchText);

                  if (searchIndex === -1) {
                    return (
                      <li key={key} {...optionProps}>
                        {text}
                      </li>
                    );
                  }

                  const beforeMatch = text.substring(0, searchIndex);
                  const match = text.substring(searchIndex, searchIndex + searchText.length);
                  const afterMatch = text.substring(searchIndex + searchText.length);

                  return (
                    <li key={key} {...optionProps}>
                      <span>
                        {beforeMatch}
                        <Box component="span" sx={{ color: theme.palette.primary.main, fontWeight: 'bold' }}>
                          {match}
                        </Box>
                        {afterMatch}
                      </span>
                    </li>
                  );
                }}
                value={currentOptions.find((option) => option.value === field.value) || null}
                onChange={(_, newValue) => {
                  if (newValue && typeof newValue === 'object') {
                    field.onChange(newValue.value);
                  } else {
                    field.onChange('');
                  }
                }}
                getOptionLabel={(option) => {
                  if (typeof option === 'string') {
                    return option;
                  }
                  return option?.label || '';
                }}
                getOptionKey={(option) => {
                  if (typeof option === 'string') {
                    return option;
                  }
                  return option?.value || '';
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    variant={variant}
                    placeholder={placeholder}
                    label=""
                    sx={{
                      padding: '0px !important',
                      position: 'relative',
                      '& .MuiAutocomplete-endAdornment': {
                        '& button[aria-label="Open"]': {
                          visibility: 'hidden',
                        },
                      },
                      '& .MuiAutocomplete-popupIndicatorOpen': {
                        visibility: 'hidden',
                      },
                    }}
                    slotProps={{
                      input: {
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            <Box
                              sx={{
                                position: 'absolute',
                                right: '12px',
                                top: 0,
                                bottom: 0,
                                display: 'flex',
                                alignItems: 'center',
                                color: theme.palette.text.secondary,
                              }}
                            >
                              <SearchIcon color="inherit" />
                            </Box>
                            {params.InputProps.endAdornment}
                          </>
                        ),
                      },
                    }}
                  />
                )}
              />
            </Box>
            {errors?.[name] && (
              <Typography variant="caption" color="error">
                {errors[name]?.message as string}
              </Typography>
            )}
          </FormControl>
        );
      }}
    />
  );
}
