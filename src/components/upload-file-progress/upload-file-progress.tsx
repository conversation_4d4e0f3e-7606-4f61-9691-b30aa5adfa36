import React, { useMemo } from 'react';
import { Box, Typography, IconButton, LinearProgress } from '@mui/material';
import Image from 'next/image';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import pdfFileIcon from 'assets/icons/pdf-file.svg';
import pngFileIcon from 'assets/icons/png-file.svg';
import jpgFileIcon from 'assets/icons/jpg-file.svg';
import { bytesToMB, getImageUrl } from 'utils';
import { useTranslations } from 'next-intl';
import { capitalize } from 'lodash-es';
import VisibilityIcon from '@mui/icons-material/Visibility';
import { AcceptFileTypes } from 'types';

interface UploadProgressUIProps {
  uploading?: boolean;
  onPreview?: () => void;
  fileType: AcceptFileTypes;
  fileName?: string;
  fileSize?: number; // byte
  onDelete?: (id: string) => void;
  fileId: string;
  fileUrl: string;
}

const UploadProgressUI: React.FC<UploadProgressUIProps> = ({
  uploading,
  fileType,
  fileName,
  fileSize,
  onPreview,
  onDelete,
  fileId,
  fileUrl,
}) => {
  const commonT = useTranslations('common');

  const fileTypePrefix = fileType ? capitalize(fileType.split('/')[1]) : '';

  // Truncate file name if too long
  const truncatedFileName = useMemo(() => {
    if (!fileName) return '';

    const maxLength = 30;
    if (fileName.length <= maxLength) return fileName;

    // Get file extension
    const lastDotIndex = fileName.lastIndexOf('.');
    const extension = lastDotIndex !== -1 ? fileName.substring(lastDotIndex) : '';
    const nameWithoutExt = lastDotIndex !== -1 ? fileName.substring(0, lastDotIndex) : fileName;

    // Calculate available space for name (minus extension and ellipsis)
    const availableLength = maxLength - extension.length - 3; // 3 for "..."

    if (availableLength > 0) {
      return nameWithoutExt.substring(0, availableLength) + '...' + extension;
    } else {
      return fileName.substring(0, maxLength - 3) + '...';
    }
  }, [fileName]);

  const fileIconLogo = useMemo(() => {
    if (fileType === 'application/pdf') {
      return pdfFileIcon;
    } else if (fileType === 'image/png') {
      return pngFileIcon;
    }
    return jpgFileIcon;
  }, [fileType]);

  return (
    <Box
      sx={{
        width: '100%',
        p: 2,
        display: 'flex',
        border: (theme) => `1px solid ${theme.palette.customColors.neutralBorder}`,
        borderRadius: '4px',
        gap: '12px',
        alignItems: 'center',
        position: 'relative',
        opacity: uploading ? 0.9 : 1,
      }}
    >
      <Image unoptimized width={40} height={40} alt="file-icon" src={fileIconLogo} />

      <Box
        sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'flex-start',
          overflowX: 'hidden',
          textWrap: 'nowrap',
          textOverflow: 'ellipsis',
          gap: '4px',
          minHeight: '60px', // Reserve minimum height to prevent layout shift
        }}
      >
        <Typography
          title={fileName} // Show full name on hover
          sx={{
            width: '100%',
            overflowX: 'hidden',
            textWrap: 'nowrap',
            textOverflow: 'ellipsis',
          }}
        >
          {truncatedFileName}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {`${fileTypePrefix} - ${bytesToMB(fileSize ?? 0)}${commonT('mb')}`}
        </Typography>

        {/* Progress bar container - always present to prevent layout shift */}
        <Box sx={{ width: '100%', height: '4px', mt: 0.5 }}>
          {uploading && (
            <LinearProgress
              sx={{
                width: '100%',
                height: '100%',
                borderRadius: 2,
              }}
            />
          )}
        </Box>
      </Box>

      <Box
        sx={{
          display: 'flex',
          gap: '12px',
        }}
      >
        <IconButton
          color="primary"
          onClick={onPreview}
          {...(getImageUrl(fileUrl) && { href: getImageUrl(fileUrl), target: "_blank" })}
          disabled={uploading}
        >
          <VisibilityIcon />
        </IconButton>
        {onDelete && (
          <IconButton
            color="error"
            onClick={() => onDelete?.(fileId)}
            disabled={uploading}
          >
            <DeleteOutlineIcon />
          </IconButton>
        )}
      </Box>
    </Box>
  );
};

export default UploadProgressUI;
