import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { DurianVarietyBox, DurianVarietyBoxProps } from './durian-variety';
import { theme } from 'styles/theme';
import dayjs from 'dayjs';

// Mock dependencies
jest.mock('next-intl', () => ({
  useTranslations: () => (key: string) => {
    const translations: Record<string, string> = {
      'receive-blooming-on': 'Blooming on',
      'updated-on': 'Updated on',
      'by': 'by',
      'view-updated-data': 'View updated data',
      'view-original-data': 'View original data',
    };
    return translations[key] || key;
  },
}));

jest.mock('store/useMaterDataStore', () => ({
  useMasterDataStore: () => ({
    getGradeLabel: (id: string) => `Grade ${id}`,
    getVarietyLabel: (id: string) => `Variety ${id}`,
  }),
}));

jest.mock('utils', () => ({
  formatNumberWithCommas: (num: number) => num.toLocaleString(),
}));

jest.mock('containers/event/_components', () => ({
  formatDateWithLocale: () => '01-January-2024',
}));

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider theme={theme}>{children}</ThemeProvider>
);

const mockVariety = {
  id: 'variety-1',
  name: 'Test Variety',
  flowerBloomingDay: dayjs('2024-01-01').unix(),
  grades: [
    { id: 'grade-1', weight: 100 },
    { id: 'grade-2', weight: 200 },
  ],
};

const defaultProps: DurianVarietyBoxProps = {
  variety: mockVariety,
};

describe('DurianVarietyBox', () => {
  it('renders variety information correctly', () => {
    render(
      <TestWrapper>
        <DurianVarietyBox {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Test Variety')).toBeInTheDocument();
    expect(screen.getByText(/Blooming on 01-January-2024/)).toBeInTheDocument();
  });

  it('uses variety label when name is not provided', () => {
    const varietyWithoutName = { ...mockVariety, name: undefined };
    render(
      <TestWrapper>
        <DurianVarietyBox variety={varietyWithoutName} />
      </TestWrapper>
    );

    expect(screen.getByText('Variety variety-1')).toBeInTheDocument();
  });

  it('shows delete button when onDelete prop is provided', () => {
    const mockOnDelete = jest.fn();
    render(
      <TestWrapper>
        <DurianVarietyBox {...defaultProps} onDelete={mockOnDelete} />
      </TestWrapper>
    );

    const deleteButton = screen.getByRole('button');
    expect(deleteButton).toBeInTheDocument();
  });

  it('calls onDelete when delete button is clicked', () => {
    const mockOnDelete = jest.fn();
    render(
      <TestWrapper>
        <DurianVarietyBox {...defaultProps} onDelete={mockOnDelete} />
      </TestWrapper>
    );

    const deleteButton = screen.getByRole('button');
    fireEvent.click(deleteButton);

    expect(mockOnDelete).toHaveBeenCalledWith('variety-1');
  });

  it('does not show delete button when onDelete is not provided', () => {
    render(
      <TestWrapper>
        <DurianVarietyBox {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.queryByRole('button')).not.toBeInTheDocument();
  });

  it('displays grades when showGrades is true', () => {
    render(
      <TestWrapper>
        <DurianVarietyBox {...defaultProps} showGrades={true} />
      </TestWrapper>
    );

    expect(screen.getByText('Grade grade-1 - 100')).toBeInTheDocument();
    expect(screen.getByText('Grade grade-2 - 200')).toBeInTheDocument();
  });

  it('does not display grades when showGrades is false', () => {
    render(
      <TestWrapper>
        <DurianVarietyBox {...defaultProps} showGrades={false} />
      </TestWrapper>
    );

    expect(screen.queryByText('Grade grade-1 - 100')).not.toBeInTheDocument();
    expect(screen.queryByText('Grade grade-2 - 200')).not.toBeInTheDocument();
  });

  it('filters out grades with zero weight', () => {
    const varietyWithZeroWeight = {
      ...mockVariety,
      grades: [
        { id: 'grade-1', weight: 100 },
        { id: 'grade-2', weight: 0 },
        { id: 'grade-3', weight: 300 },
      ],
    };

    render(
      <TestWrapper>
        <DurianVarietyBox variety={varietyWithZeroWeight} showGrades={true} />
      </TestWrapper>
    );

    expect(screen.getByText('Grade grade-1 - 100')).toBeInTheDocument();
    expect(screen.queryByText('Grade grade-2 - 0')).not.toBeInTheDocument();
    expect(screen.getByText('Grade grade-3 - 300')).toBeInTheDocument();
  });

  it('shows update information when isUpdated is true', () => {
    render(
      <TestWrapper>
        <DurianVarietyBox
          {...defaultProps}
          isUpdated={true}
          updatedBy="John Doe"
        />
      </TestWrapper>
    );

    expect(screen.getByText(/Updated on 01-January-2024 by/)).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });

  it('shows toggle view options when isUpdated is true', () => {
    const mockOnToggleView = jest.fn();
    render(
      <TestWrapper>
        <DurianVarietyBox
          {...defaultProps}
          isUpdated={true}
          onToggleView={mockOnToggleView}
          isShowingOriginal={false}
        />
      </TestWrapper>
    );

    const toggleLink = screen.getByText('View original data');
    expect(toggleLink).toBeInTheDocument();

    fireEvent.click(toggleLink);
    expect(mockOnToggleView).toHaveBeenCalledWith('variety-1');
  });

  it('shows correct toggle text based on isShowingOriginal state', () => {
    render(
      <TestWrapper>
        <DurianVarietyBox
          {...defaultProps}
          isUpdated={true}
          isShowingOriginal={true}
        />
      </TestWrapper>
    );

    expect(screen.getByText('View updated data')).toBeInTheDocument();
  });

  it('does not show update information when isUpdated is false', () => {
    render(
      <TestWrapper>
        <DurianVarietyBox {...defaultProps} isUpdated={false} />
      </TestWrapper>
    );

    expect(screen.queryByText(/Updated on/)).not.toBeInTheDocument();
    expect(screen.queryByText(/View original data/)).not.toBeInTheDocument();
  });

  it('handles empty grades array', () => {
    const varietyWithoutGrades = { ...mockVariety, grades: [] };
    render(
      <TestWrapper>
        <DurianVarietyBox variety={varietyWithoutGrades} showGrades={true} />
      </TestWrapper>
    );

    expect(screen.getByText('Test Variety')).toBeInTheDocument();
    // Should not crash and should not show any grade chips
  });

  it('applies custom sx props', () => {
    const customSx = { backgroundColor: 'red' };
    const { container } = render(
      <TestWrapper>
        <DurianVarietyBox {...defaultProps} sx={customSx} />
      </TestWrapper>
    );

    const paper = container.querySelector('.MuiPaper-root');
    expect(paper).toHaveStyle('background-color: rgb(255, 0, 0)');
  });
});
