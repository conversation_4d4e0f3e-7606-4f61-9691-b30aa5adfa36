import { Box, Chip, IconButton, Paper, SxProps, Theme, Typography } from '@mui/material';
import DeleteOutlinedIcon from '@mui/icons-material/DeleteOutlined';
import React from 'react';
import { formatNumberWithCommas } from 'utils';
import { theme } from 'styles/theme';
import { useTranslations } from 'next-intl';
import dayjs from 'dayjs';
import { useMasterDataStore } from 'store/useMaterDataStore';
import { formatDateWithLocale } from 'containers/event/_components';
import { DD_MMMM_YYYY_WITH_DASH } from 'constant/common';

export interface DurianVarietyBoxProps {
  variety: {
    name?: string;
    id: string;
    flowerBloomingDay: number;
    grades: { id: string; weight: number }[];
  };
  onDelete?: (id: string) => void;
  showGrades?: boolean;
  sx?: SxProps<Theme>;
  isUpdated?: boolean;
  updateDate?: string;
  updatedBy?: string;
  isShowingOriginal?: boolean;
  onToggleView?: (varietyId: string) => void;
}

export const DurianVarietyBox: React.FC<DurianVarietyBoxProps> = ({
  variety,
  onDelete,
  showGrades = false,
  sx = {},
  isUpdated = false,
  updatedBy = '',
  isShowingOriginal = false,
  onToggleView,
}) => {
  const { getGradeLabel, getVarietyLabel } = useMasterDataStore();
  const { flowerBloomingDay } = variety || {};

  const receiveTranslation = useTranslations('receive');
  const handleDelete = () => {
    if (onDelete) {
      onDelete(variety.id);
    }
  };

  const handleToggleView = () => {
    if (onToggleView) {
      onToggleView(variety.id);
    }
  };
  const formattedBloomDay = flowerBloomingDay
    ? formatDateWithLocale(dayjs.unix(flowerBloomingDay), DD_MMMM_YYYY_WITH_DASH)
    : '';
  return (
    <Paper
      elevation={0}
      sx={{
        position: 'relative',
        border: '1px solid',
        borderRadius: '12px',
        borderColor: 'divider',
        flexDirection: 'column',
        alignItems: 'flex-start',
        p: '12px',
        ...sx,
      }}
    >
      {onDelete && (
        <IconButton
          size="small"
          onClick={handleDelete}
          sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            p: 0.5,
          }}
        >
          <DeleteOutlinedIcon color="error" fontSize="small" />
        </IconButton>
      )}

      <Box
        sx={{
          mb: showGrades ? '16px' : 0,
          pr: 4,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'flex-start',
        }}
      >
        <Typography variant="subtitle1" fontWeight="medium">
          {variety?.name ? variety.name : getVarietyLabel(variety.id)}
        </Typography>
        <Typography variant="caption" color="text.secondary" fontWeight={400}>
          {`${receiveTranslation('receive-blooming-on')} ${formattedBloomDay}`}
        </Typography>
      </Box>

      {/* Display grades if showGrades is true */}
      {showGrades && variety.grades?.length > 0 && (
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
          {variety.grades
            .filter((grade) => grade.weight > 0)
            .map((grade) => (
              <Chip
                key={grade.id}
                sx={{
                  backgroundColor: theme.palette.customColors.primary100,
                  borderColor: theme.palette.customColors.primary100,
                }}
                label={`${getGradeLabel(grade.id)} - ${formatNumberWithCommas(grade.weight)}`}
                variant="outlined"
                size="medium"
              />
            ))}
        </Box>
      )}
      {isUpdated && (
        <Box
          sx={{
            mt: 2,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'flex-start',
            fontSize: 10,
          }}
        >
          <Typography variant="caption" color="text.secondary" fontWeight={400}>
            {receiveTranslation('updated-on')} {formatDateWithLocale(dayjs())} {receiveTranslation('by')}{' '}
            <strong>{updatedBy}</strong>
          </Typography>
          <Box sx={{ mt: 0.5 }}>
            <Typography
              variant="caption"
              color="primary"
              sx={{ cursor: 'pointer', textDecoration: 'none' }}
              onClick={handleToggleView}
            >
              {isShowingOriginal ? receiveTranslation('view-updated-data') : receiveTranslation('view-original-data')}
            </Typography>
          </Box>
        </Box>
      )}
    </Paper>
  );
};
