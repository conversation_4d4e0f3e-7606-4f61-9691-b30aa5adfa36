import React from 'react';

import HelpIcon from '@mui/icons-material/Help';
import { useTranslations } from 'next-intl';
import {
  Box,
  Button,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  Dialog as MuiDialog,
} from '@mui/material';
import { theme } from 'styles/theme';
import { Close, Delete } from '@mui/icons-material';

interface DialogContentProps {
  isOpen: boolean;
  title: string;
  content: string;
  okButtonText?: string;
  cancelButtonText?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
  type?: 'info' | 'danger';
  hideCancelButton?: boolean;
}

export const Dialog: React.FC<DialogContentProps> = ({
  isOpen,
  title,
  content,
  okButtonText,
  cancelButtonText,
  onConfirm,
  onCancel,
  type = 'info',
  hideCancelButton = false,
}) => {
  const commonTranslations = useTranslations('common');

  const handleConfirmNavigation = () => {
    if (!onConfirm) return;
    onConfirm();
  };

  const handleCancelNavigation = () => {
    if (!onCancel) return;
    onCancel();
  };

  return (
    <MuiDialog
      open={isOpen}
      onClose={handleCancelNavigation}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
      disableScrollLock
    >
      <Box sx={{ display: 'flex', alignItems: 'start', padding: 4, gap: 2, pb: 0 }}>
        <Box>
          {type === 'danger' ? (
            <IconButton
              sx={{
                width: '62px',
                height: '62px',
                background: theme.palette.customColors.toastError,
                ':hover': {
                  background: theme.palette.customColors.toastError,
                },
              }}
            >
              <Delete sx={{ fontSize: '40px', color: 'white' }} />
            </IconButton>
          ) : (
            <HelpIcon sx={{ fontSize: '62px' }} color="primary" />
          )}
        </Box>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <DialogTitle id="alert-dialog-title" sx={{ padding: 0 }}>
              {title || commonTranslations('reminder-title')}
            </DialogTitle>
            <IconButton>
              <Close
                sx={{ color: theme.palette.customColors.black, fontSize: '28px' }}
                onClick={handleCancelNavigation}
              />
            </IconButton>
          </Box>
          <DialogContent sx={{ padding: 0 }}>
            <DialogContentText
              id="alert-dialog-description"
              sx={{ color: theme.palette.customColors.black, fontWeight: 400, wordBreak: 'break-word' }}
            >
              {content || commonTranslations('reminder-content')}
            </DialogContentText>
          </DialogContent>
        </Box>
      </Box>
      <DialogActions sx={{ padding: 4, pt: 2 }}>
        {!hideCancelButton && (
          <Button variant="outlined" onClick={handleCancelNavigation} autoFocus sx={{ width: '120px' }}>
            {cancelButtonText || commonTranslations('cancel-modal-btn')}
          </Button>
        )}
        <Button
          variant="contained"
          onClick={handleConfirmNavigation}
          sx={{
            width: '120px',
            backgroundColor: type === 'danger' ? theme.palette.customColors.toastError : theme.palette.primary.main,
          }}
        >
          {okButtonText || commonTranslations('ok-modal-btn')}
        </Button>
      </DialogActions>
    </MuiDialog>
  );
};
