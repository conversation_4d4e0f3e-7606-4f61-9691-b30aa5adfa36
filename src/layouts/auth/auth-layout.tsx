'use client';

import Box from '@mui/material/Box';
import { FC, PropsWithChildren } from 'react';
import logo from 'assets/icons/logo.svg';
import textLogo from 'assets/icons/text-logo.svg';

import { LanguagePicker } from 'components';
import Image from 'next/image';

import { basePathProd } from 'configs/app-config';
import { Typography } from '@mui/material';
import { useTranslations } from 'next-intl';

export const AuthLayout: FC<PropsWithChildren> = ({ children }) => {
  const commonT = useTranslations('common');
  const isDev = window.__ENV__?.NEXT_PUBLIC_ENV === 'development';
  const isStaging = window.__ENV__?.NEXT_PUBLIC_ENV === 'staging';

  const renderVersion = () => {
    if (typeof window === 'undefined') {
      return '';
    }

    const version = window.__ENV__?.NEXT_PUBLIC_WEB_VERSION ?? '';

    if (isDev) {
      return version;
    } else if (isStaging) {
      return `${version}`;
    }
    return `${version?.split('-prod')[0] || 'v1.0.0'}`;
  };

  return (
    <Box
      translate="no"
      sx={{
        width: '100%',
        height: '100vh',
        display: 'flex',
        justifyContent: 'space-between',
        boxSizing: 'border-box',
        alignItems: 'center',
        backgroundColor: '#fff',
        position: 'relative',
        padding: {
          lg: '2rem',
          md: '1rem',
          sm: '1rem',
          xs: '1rem',
        },
      }}
    >
      <Box
        component="div"
        sx={{
          flex: 1,
          height: '100%',
          backgroundImage: `
               url('${basePathProd}/assets/images/warehouse-bg.jpg')`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          width: '100%',
          borderRadius: '36px',
        }}
      />
      <Box
        component="div"
        sx={{
          flex: 1,
          height: '100%',
          display: 'flex',
          justifyContent: 'flex-end',
          alignItems: 'center',
          flexDirection: 'column',
          position: 'relative',
        }}
      >
        <Box
          component="div"
          sx={{
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            flexDirection: 'column',
            justifyContent: 'center',
            maxWidth: '500px',
          }}
        >
          <Box
            component="div"
            mb={1}
            sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', position: 'relative' }}
          >
            <Typography
              variant="caption"
              fontWeight={500}
              sx={{
                width: '100%',
                display: 'flex',
                justifyContent: 'flex-end',
                fontSize: '14px',
                position: 'fixed',
                top: '16px',
                right: '16px',
              }}
            >
              {renderVersion()}
            </Typography>
            <Image src={logo} width={400} height={180} alt="project_icon" />
            <Image src={textLogo} width={200} height={80} alt="title-text" />
            <Typography
              textAlign="center"
              fontWeight={600}
              my="8px"
              variant="caption"
              sx={{ fontSize: '28px' }}
              color="primary"
            >
              {commonT('app-description')}
            </Typography>
          </Box>

          {children}
        </Box>

        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            zIndex: 2,
          }}
        >
          <LanguagePicker />
        </Box>
      </Box>
    </Box>
  );
};
