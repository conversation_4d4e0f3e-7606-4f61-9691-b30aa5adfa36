'use client';

import { <PERSON>, Button } from '@mui/material';

import { useAllMasterDataQuery } from 'hooks/queries/useMasterData';
import { useEffect, useState } from 'react';
import { useMasterDataStore } from 'store/useMaterDataStore';
import { DrawerAppBar, PageTransition } from './components';
import { User } from 'types/user';
import { useUserStore } from 'store/useUserStore';
import { useGetVarietiesQuery } from 'hooks/queries/useGetVarietiesQuery';
import { DurianVariety } from 'types';
import { LoadingLayout } from './loading-layout';
import { useGlobalStore } from 'store/useGlobalStore';
import { fetchUserInfoService } from '../../services/internal.service';
import { clientRoutes } from 'routes/client-routes';
import { usePathname } from 'next/navigation';
import { useCreateShipmentStore } from 'store/useCreateShipmentStore';
import { theme } from 'styles/theme';
import { useIsLaptop } from 'hooks/useIsLaptop';
import { Header } from './components/header';
import { useTranslations } from 'next-intl';
import { useAuthStore } from 'store/useAuthStore';

interface LayoutProps {
  children: React.ReactNode;
  loading?: boolean;
  user?: User;
}

const httpAccessLockCode = 423;

export const Layout = ({ children }: Readonly<LayoutProps>) => {
  const [{ data: masterData }, { data: provinces }] = useAllMasterDataQuery();
  const { data: varietyData } = useGetVarietiesQuery();
  const { updateMasterData, setVarieties } = useMasterDataStore();
  const { setUserInfo } = useUserStore();
  const { loadingPage, setLoadingPage, setIsTablet, setFailedUserInfo, hasFailedUserInfo } = useGlobalStore();
  const { resetStore: resetShipmentStore } = useCreateShipmentStore();
  const [hasLoadUserInfo, setHasLoadUserInfo] = useState(false);
  const logoutFn = useAuthStore((state) => state.logout);
  const { setCurrentPathname, previousPathname, currentPathname } = useGlobalStore();

  const isLaptop = useIsLaptop();

  const commonTranslation = useTranslations('common');

  useEffect(() => {
    updateMasterData(masterData?.brandNames ?? [], masterData?.productTypes ?? [], provinces ?? []);
  }, [masterData, provinces, updateMasterData]);

  useEffect(() => {
    setIsTablet(!isLaptop);
  }, [isLaptop, setIsTablet]);

  useEffect(() => {
    if (varietyData) {
      setVarieties(varietyData as unknown as DurianVariety[]);
    }
  }, [setVarieties, varietyData]);

  useEffect(() => {
    fetchUserInfoService()
      .then((userResponse) => {
        if (userResponse) {
          setUserInfo(userResponse.data);
          setFailedUserInfo(false);
        }
      })
      .catch((error) => {
        if (error.response?.status === httpAccessLockCode) {
          setUserInfo(undefined);
          setFailedUserInfo(true);
        } else {
          throw new Error('Failed to fetch user info');
        }
      })
      .finally(() => {
        setHasLoadUserInfo(true);
      });
  }, [setFailedUserInfo, setLoadingPage, setUserInfo]);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (loadingPage && previousPathname !== currentPathname) {
      timer = setTimeout(() => {
        setLoadingPage(false);
      }, 100);
    }
    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [currentPathname, loadingPage, previousPathname, setLoadingPage]);

  const browserPathname = usePathname();

  useEffect(() => {
    if (!browserPathname) {
      return;
    }
    setCurrentPathname(browserPathname);
  }, [browserPathname, setCurrentPathname]);

  useEffect(() => {
    if (
      previousPathname?.includes(clientRoutes.createShipment) &&
      !currentPathname?.includes(clientRoutes.createShipment)
    ) {
      resetShipmentStore();
    }
  }, [currentPathname, previousPathname, resetShipmentStore]);

  const handleLogout = () => {
    try {
      setLoadingPage(true);
      logoutFn();
    } catch {
      setLoadingPage(false);
      throw new Error('Logout failed');
    } finally {
      setLoadingPage(false);
    }
  };

  return (
    <LoadingLayout loading={loadingPage}>
      <Box
        component="div"
        id="main-layout-wrapper"
        sx={{
          display: 'flex',
          flex: 1,
          width: '100%',
          flexDirection: 'column',
          background: theme.palette.customColors.lightGray,
        }}
      >
        <Header />
        {hasFailedUserInfo ? (
          <Box
            sx={{
              width: '100%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: 'inherit',
              padding: '16px',
              alignContent: 'center',
              height: '100%',
              flex: 1,
              flexDirection: 'column',
            }}
          >
            <div>{commonTranslation('permission-text')}</div>
            <Button color="error" onClick={handleLogout} sx={{ mt: '12px' }} variant="outlined">
              {commonTranslation('logout')}
            </Button>
          </Box>
        ) : (
          hasLoadUserInfo && (
            <DrawerAppBar>
              <PageTransition>{children}</PageTransition>
            </DrawerAppBar>
          )
        )}
      </Box>
    </LoadingLayout>
  );
};
