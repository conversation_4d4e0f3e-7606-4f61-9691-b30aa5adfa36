import { Box, useTheme } from '@mui/material';
import logo from 'assets/icons/side-logo-icon.svg';
import Image from 'next/image';
import React from 'react';

import textLogo from 'assets/icons/text-logo.svg';

export const LeftHeader = React.memo(() => {
  const theme = useTheme();

  const logoSize = 40;
  return (
    <Box component="div" sx={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mb: 1 }}>
        <Box
          sx={{
            display: 'flex',
            gap: 1,
            alignItems: 'center',
            height: 64,
            width: '100%',
            backgroundColor: 'transparent',
            transition: theme.transitions.create(['opacity', 'width'], {
              easing: theme.transitions.easing.easeInOut,
              duration: theme.transitions.duration.complex,
            }),
            justifyContent: 'center',
            position: 'relative',
          }}
        >
          <Image unoptimized src={logo} width={logoSize} height={logoSize} alt="logo" />
          <Image unoptimized src={textLogo} width={120} height={30} alt="title-text" />
        </Box>
      </Box>
    </Box>
  );
});

LeftHeader.displayName = 'AppLogo';
