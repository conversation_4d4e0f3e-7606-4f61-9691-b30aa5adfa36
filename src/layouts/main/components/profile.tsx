'use client';

import { ArrowDropDown, HeadphonesOutlined, PersonOutline } from '@mui/icons-material';
import Logout from '@mui/icons-material/Logout';
import { Box, Button, Divider, ListItemText, Typography } from '@mui/material';
import Avatar from '@mui/material/Avatar';
import ListItemIcon from '@mui/material/ListItemIcon';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import { GB, TH } from 'country-flag-icons/react/3x2';
import { get } from 'lodash-es';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { CSSProperties, FC, MouseEvent, ReactNode, useLayoutEffect, useMemo, useState } from 'react';
import { clientRoutes } from 'routes/client-routes';
import { updateCookieLocaleService } from 'services/internal.service';
import { useAuthStore } from 'store/useAuthStore';
import { useGlobalStore } from 'store/useGlobalStore';
import { theme } from 'styles/theme';
import { User } from 'types/user';
import { getImageUrl } from 'utils';
import { setCookieLocale } from 'utils/cookie-client';

const flagStyle: CSSProperties = { width: '20px', height: '20px' };

const flags: Record<string, ReactNode> = {
  th: <TH style={flagStyle} />,
  en: <GB style={flagStyle} />,
};

export const Profile: FC<{ user?: User }> = ({ user }) => {
  const router = useRouter();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const t = useTranslations('language');
  const commonT = useTranslations('common');

  const localeStore = useGlobalStore((state) => state.locale);

  const LANGUAGES = [
    { value: 'th', label: `${t('thai')} (TH)` },
    { value: 'en', label: `${t('english')} (EN)` },
  ];
  const [locale, setLocale] = useState<string>('');

  useLayoutEffect(() => {
    if (localeStore) {
      setLocale(localeStore);
    }
  }, [localeStore]);

  const { logout } = useAuthStore();
  const open = Boolean(anchorEl);
  const handleClick = (event: MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const avatarUrl = useMemo(() => {
    const filenameDisk = get(user, 'avatar.filenameDisk', '') as string;

    return getImageUrl(filenameDisk);
  }, [user]);

  const userName = `${user?.firstName ?? ''}`;

  const handleSelect = async (newLocale: string) => {
    await updateCookieLocaleService(newLocale);
    setCookieLocale(newLocale);
    window.location.reload();
  };

  return (
    <>
      <Button
        onClick={handleClick}
        size="small"
        aria-controls={open ? 'account-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            gap: 2,
          }}
        >
          {avatarUrl ? (
            <Avatar sx={{ width: 30, height: 30, objectFit: 'cover' }} src={avatarUrl} />
          ) : (
            <Avatar sx={{ width: 30, height: 30 }}>{user?.lastName?.split('')[0] ?? ''}</Avatar>
          )}
          <Box sx={{ gap: 1, display: 'flex', alignItems: 'center' }}>
            <Typography sx={{ color: theme.palette.customColors.black, fontSize: '16px' }}>{userName}</Typography>
            <ArrowDropDown sx={{ color: theme.palette.customColors.black }} />
          </Box>
        </Box>
      </Button>
      <Menu
        anchorEl={anchorEl}
        id="account-menu"
        open={open}
        onClose={handleClose}
        onClick={handleClose}
        slotProps={{
          paper: {
            elevation: 0,
            sx: {
              minWidth: '250px',
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
              mt: 1.5,
              padding: '16px 24px',

              '&::before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: 'background.paper',
                transform: 'translateY(-50%) rotate(45deg)',
                zIndex: 0,
              },
            },
          },
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <Typography sx={{ color: theme.palette.customColors.gray, fontSize: '12px' }}>{commonT('general')}</Typography>
        <Divider sx={{ marginTop: '2px', marginBottom: '8px' }} />
        <MenuItem
          onClick={() => {
            router.push(clientRoutes.profile);
          }}
          sx={{ padding: '8px 0' }}
        >
          <ListItemIcon>
            <PersonOutline fontSize="small" sx={{ color: theme.palette.customColors.blueHighlight }} />
          </ListItemIcon>
          <Typography sx={{ color: theme.palette.customColors.black, fontSize: '16px' }}>
            {commonT('my-profile')}
          </Typography>
        </MenuItem>
        <MenuItem
          onClick={() => {
            router.push(clientRoutes.contactUs);
          }}
          sx={{ padding: '8px 0' }}
        >
          <ListItemIcon>
            <HeadphonesOutlined fontSize="small" sx={{ color: theme.palette.customColors.blueHighlight }} />
          </ListItemIcon>
          <Typography sx={{ color: theme.palette.customColors.black, fontSize: '16px' }}>
            {commonT('contact-us')}
          </Typography>
        </MenuItem>
        <Typography sx={{ color: theme.palette.customColors.gray, fontSize: '12px', marginTop: '12px' }}>
          {commonT('language')}
        </Typography>
        <Divider sx={{ marginTop: '2px', marginBottom: '8px' }} />
        {LANGUAGES.map(({ value, label }) => (
          <MenuItem
            key={value}
            selected={value === locale}
            onClick={() => {
              handleSelect(value);
              handleClose();
            }}
            sx={{ padding: '8px 0', display: 'flex', alignItems: 'center' }}
          >
            <ListItemIcon>
              <Box component="span" sx={{ fontSize: '16px' }}>
                {flags[value]}
              </Box>
            </ListItemIcon>
            <ListItemText>{label}</ListItemText>
          </MenuItem>
        ))}
        <Divider sx={{ marginTop: '2px', marginBottom: '8px' }} />
        <Typography sx={{ color: theme.palette.customColors.gray, fontSize: '12px', marginTop: '16px' }}>
          {commonT('others')}
        </Typography>
        <MenuItem onClick={logout} sx={{ padding: '8px 0' }}>
          <ListItemIcon>
            <Logout fontSize="small" sx={{ color: theme.palette.customColors.reject }} />
          </ListItemIcon>
          <Typography sx={{ color: theme.palette.customColors.black, fontSize: '16px' }}>
            {commonT('logout')}
          </Typography>
        </MenuItem>
      </Menu>
    </>
  );
};
