import { Box } from '@mui/material';
import { FC } from 'react';

interface ActiveIndicatorProps {
  readonly isActive: boolean;
  readonly borderColor: string;
}

export const ActiveIndicator: FC<ActiveIndicatorProps> = ({ isActive, borderColor }) => {
  return (
    <Box
      sx={{
        borderLeftWidth: '4px',
        borderLeftStyle: 'solid',
        borderLeftColor: isActive ? borderColor : 'transparent',
        position: 'absolute',
        left: 0,
        top: 'calc(50% - 10px)',
        height: '20px',
        borderRadius: '4px',
        transition: 'border-color 0.2s ease-in-out',
      }}
    />
  );
};
