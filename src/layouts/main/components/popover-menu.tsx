import { Box, ListItemButton, ListItemIcon, ListItemText, Paper, Popover } from '@mui/material';
import { FC } from 'react';
import { MenuItem } from 'hooks/useMenuItems';
import Link from 'next/link';
import { ActiveIndicator } from './active-indicator';
import { motion } from 'framer-motion';

interface PopoverMenuProps {
  readonly item: MenuItem;
  readonly open: boolean;
  readonly anchorEl: HTMLElement | null;
  readonly onClose: () => void;
  readonly onChildClick: (childItem: MenuItem) => void;
  readonly isSelected: (path: string, exact: boolean) => boolean;
  readonly styles: {
    readonly textPrimarySelectedColor: string;
    readonly borderLeftColor: string;
    readonly backgroundChildrenColor: string;
    readonly backgroundActiveColor: string;
  };
}

export const PopoverMenu: FC<PopoverMenuProps> = ({
  item,
  open,
  anchorEl,
  onClose,
  onChildClick,
  isSelected,
  styles,
}) => {
  return (
    <Popover
      id={`popover-${item.itemKey}`}
      open={open}
      anchorEl={anchorEl}
      onClose={onClose}
      anchorOrigin={{
        vertical: 'top',
        horizontal: 'right',
      }}
      transformOrigin={{
        vertical: 'top',
        horizontal: 'left',
      }}
      sx={{
        '& .MuiPopover-paper': {
          borderRadius: 1,
          boxShadow: 4,
          mt: 0,
        },
      }}
    >
      <Paper
        sx={{
          width: 'auto',
          overflow: 'hidden',
        }}
      >
        {item.children?.map((childItem) => (
          <motion.div
            key={childItem.itemKey}
            whileHover={{ scale: 1.05 }}
            transition={{ type: 'spring', stiffness: 400, damping: 25 }}
          >
            <ListItemButton
              component={childItem.path ? Link : 'button'}
              href={childItem.path}
              onClick={() => onChildClick(childItem)}
              selected={isSelected(childItem?.path ?? '', childItem.exact ?? false)}
              sx={{
                p: '12px',
                backgroundColor: styles.backgroundChildrenColor,
                '&.Mui-selected': {
                  color: styles.textPrimarySelectedColor,
                  backgroundColor: styles.backgroundChildrenColor,
                  '&:hover': {
                    color: styles.textPrimarySelectedColor,
                    backgroundColor: styles.backgroundChildrenColor,
                  },
                },
                '&:hover:not(.Mui-selected)': {
                  color: styles.textPrimarySelectedColor,
                  backgroundColor: styles.backgroundChildrenColor,

                },
              }}
            >
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                width: '100%',
                px: '12px',
                position: 'relative',
                backgroundColor: isSelected(childItem?.path ?? '', childItem.exact ?? false)
                  ? styles.backgroundActiveColor
                  : 'transparent',
                borderRadius: isSelected(childItem?.path ?? '', childItem.exact ?? false) ? '8px' : '0',
                '&:hover': {
                  backgroundColor: isSelected(childItem?.path ?? '', childItem.exact ?? false)
                    ? styles.backgroundActiveColor
                    : 'transparent',
                  borderRadius: '0',
                },
              }}
            >
              <ActiveIndicator
                isActive={isSelected(childItem.path ?? '', childItem.exact ?? false)}
                borderColor={styles.borderLeftColor}
              />
              {childItem.icon && (
                <ListItemIcon sx={{ minWidth: 36, color: 'inherit' }}>
                  {childItem.icon}
                </ListItemIcon>
              )}
              <ListItemText
                primary={childItem.text}
                sx={{
                  '& .MuiTypography-root': {
                    fontSize: '14px',
                    fontWeight: isSelected(childItem?.path ?? '', childItem.exact ?? false)
                      ? 'medium'
                      : 'normal',
                  },
                }}
                translate="no"
              />
            </Box>
          </ListItemButton>
          </motion.div>
        ))}
      </Paper>
    </Popover>
  );
};
