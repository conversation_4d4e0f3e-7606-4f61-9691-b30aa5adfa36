'use client';

import { AppB<PERSON>, <PERSON>, Button, Toolbar, Typography, useTheme } from '@mui/material';
import logo from 'assets/icons/logo.svg';
import { Image, NotificationBell } from 'components';
import { useTranslations } from 'next-intl';
import { FC } from 'react';
import { useUserStore } from 'store/useUserStore';
import { headerHeight } from '../constant';
import { LeftHeader } from './left-header';
import { Profile } from './profile';
import { useGlobalStore } from 'store/useGlobalStore';
import { Logout } from '@mui/icons-material';
import { useAuthStore } from 'store/useAuthStore';

export const Header: FC<{ isFullWidth?: boolean }> = ({ isFullWidth }) => {
  const commonT = useTranslations('common');
  const logoSize = 40;
  const { logout } = useAuthStore();
  const user = useUserStore((state) => state.user);
  const { hasFailedUserInfo } = useGlobalStore();

  const theme = useTheme();

  return (
    <AppBar
      position="sticky"
      elevation={4}
      sx={{
        width: '100vw',
        zIndex: theme.zIndex.appBar,
        boxShadow: 1,
        backgroundColor: theme.palette.customColors.white,
        color: theme.palette.customColors.black,
        height: `${headerHeight}px`,
      }}
    >
      {isFullWidth ? (
        <Box
          sx={{
            display: 'flex',
            gap: 1,
            alignItems: 'center',
            height: 64,
            width: '100%',
            background: theme.palette.customColors.gradientAppBgColor,
            transition: theme.transitions.create(['opacity', 'width'], {
              easing: theme.transitions.easing.easeInOut,
              duration: theme.transitions.duration.complex,
            }),
            justifyContent: 'start',
            paddingLeft: 4,
            position: 'relative',
          }}
        >
          <Image unoptimized src={logo} width={logoSize} height={logoSize} alt="logo" />
          <Typography
            variant="body1"
            fontWeight={600}
            fontSize={18}
            color="white"
            noWrap
            sx={{
              transition: theme.transitions.create(['opacity', 'width'], {
                easing: theme.transitions.easing.easeInOut,
                duration: theme.transitions.duration.complex,
              }),
            }}
          >
            {commonT('app-name')}
          </Typography>
        </Box>
      ) : (
        <Toolbar sx={{ display: 'flex', justifyContent: 'space-between', height: '100%' }}>
          <LeftHeader />
          <Box sx={{ display: 'flex', alignItems: 'center', color: theme.palette.customColors.black, gap: '16px' }}>
            {user && <NotificationBell />}
            {user && <Profile user={user} />}
            {hasFailedUserInfo && !user && (
              <Button color='error' onClick={logout}>
                <Logout fontSize="small" sx={{ color: theme.palette.customColors.reject }} />
              </Button>
            )}
          </Box>
        </Toolbar>
      )}
    </AppBar>
  );
};
