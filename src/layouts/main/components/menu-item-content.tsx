import { Box, ListItemIcon, ListItemText } from '@mui/material';
import { FC } from 'react';
import { motion } from 'framer-motion';
import { MenuItem } from 'hooks/useMenuItems';
import { ActiveIndicator } from './active-indicator';

interface MenuItemContentProps {
  readonly item: MenuItem;
  readonly isOpen: boolean;
  readonly isSelected: boolean;
  readonly borderColor: string;
  readonly plText: string;
  readonly showBorderLeft: boolean;
  readonly backgroundActiveColor: string;
  readonly isHovered?: boolean;
}

export const MenuItemContent: FC<MenuItemContentProps> = ({
  item,
  isOpen,
  isSelected,
  borderColor,
  plText,
  showBorderLeft,
  backgroundActiveColor,
  isHovered = false,
}) => {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        fontSize: '16px',
        p: isOpen && item.isChildren ? '8px' : '0',
        boxSizing: 'border-box',
        position: isOpen && item.isChildren ? 'relative' : 'unset',
        width: '100%',
        backgroundColor: isOpen && item.isChildren && isSelected ? backgroundActiveColor : 'transparent',
        borderRadius: isOpen && item.isChildren && isSelected ? '8px' : 'unset',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      }}
    >
      {showBorderLeft && <ActiveIndicator isActive={isSelected} borderColor={borderColor} />}

      {item.icon && (
        <motion.div
          initial={{ scale: 1 }}
          animate={{ scale: isSelected ? 1.1 : 1 }}
          transition={{ type: 'spring', stiffness: 300, damping: 25 }}
          whileHover={{ scale: 1.05 }}
        >
          <ListItemIcon sx={{ minWidth: 0, color: 'inherit' }}>
            <motion.div
              initial={{ opacity: 1 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.2, ease: 'easeInOut' }}
            >
              {(isSelected || isHovered) && item.solidIcon ? item.solidIcon : item.icon}
            </motion.div>
          </ListItemIcon>
        </motion.div>
      )}

      <motion.div
        initial={{ opacity: isOpen ? 1 : 0 }}
        animate={{ opacity: isOpen ? 1 : 0 }}
        transition={{ duration: 0.3, ease: 'easeInOut' }}
        style={{ width: '100%' }}
      >
        <ListItemText
          primary={item.text}
          sx={{
            pl: plText,
            opacity: isOpen ? 1 : 0,
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            fontSize: '14px',
            transition: 'all 0.3s ease-in-out',
          }}
          slotProps={{
            primary: {
              sx: {
                fontSize: '14px',
                fontWeight: isSelected ? 600 : 400,
                transition: 'font-weight 0.2s ease-in-out',
              },
            },
          }}
        />
      </motion.div>
    </Box>
  );
};
