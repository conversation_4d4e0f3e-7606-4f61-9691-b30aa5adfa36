/* eslint-disable @typescript-eslint/no-require-imports */
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { Profile } from '../profile';
import { theme } from 'styles/theme';
import { User } from 'types/user';

// Mock dependencies
jest.mock('next-intl', () => ({
  useTranslations: jest.fn(),
}));

jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

jest.mock('store/useAuthStore', () => ({
  useAuthStore: jest.fn(),
}));

jest.mock('store/useGlobalStore', () => ({
  useGlobalStore: jest.fn(),
}));

jest.mock('services/internal.service', () => ({
  updateCookieLocaleService: jest.fn(),
}));

jest.mock('utils/cookie-client', () => ({
  setCookieLocale: jest.fn(),
}));

jest.mock('utils', () => ({
  getImageUrl: jest.fn(),
}));

jest.mock('routes/client-routes', () => ({
  clientRoutes: {
    profile: '/profile',
    contactUs: '/contact-us',
  },
}));

// Mock country flag icons
jest.mock('country-flag-icons/react/3x2', () => ({
  GB: ({ style }: { style: React.CSSProperties }) => <div data-testid="gb-flag" style={style} />,
  TH: ({ style }: { style: React.CSSProperties }) => <div data-testid="th-flag" style={style} />,
}));

// Suppress console errors for JSDOM navigation warnings
const originalConsoleError = console.error;
beforeAll(() => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  console.error = (...args: any[]) => {
    if (typeof args[0] === 'string' && args[0].includes('Not implemented: navigation')) {
      return;
    }
    originalConsoleError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalConsoleError;
});

describe('Profile Component', () => {
  const mockUser: User = {
    id: 'user-123',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    status: 'active',
    phoneNumber: '0123456789',
    avatar: {
      filenameDisk: 'avatar.jpg',
    },
    profile: {
      id: 1,
      status: 'active',
      role: 'admin',
      supplierId: {
        id: 'supplier-1',
        status: 'active',
        name: 'Test Supplier',
        description: 'Test Description',
        images: [],
        address: 'Test Address',
        locationLatitude: '13.7563',
        locationLongitude: '100.5018',
        type: ['packing-house'],
        globalLocationNumber: '1234567890123',
        city: 'Bangkok',
        country: 'Thailand',
        bindToWorkflowNode: [1, 2],
      },
      nationalId: '1234567890123',
      roleLabel: {
        en: 'Administrator',
        th: 'ผู้ดูแลระบบ',
      },
    },
  };

  const mockRouter = {
    push: jest.fn(),
  };

  const mockLogout = jest.fn();
  const mockLanguageT = jest.fn();
  const mockCommonT = jest.fn();

  const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <ThemeProvider theme={theme}>{children}</ThemeProvider>
  );

  beforeEach(() => {
    jest.clearAllMocks();

    // Setup mocks
    const { useTranslations } = require('next-intl');
    const { useRouter } = require('next/navigation');
    const { useAuthStore } = require('store/useAuthStore');
    const { useGlobalStore } = require('store/useGlobalStore');
    const { getImageUrl } = require('utils');

    useTranslations.mockImplementation((namespace: string) => {
      if (namespace === 'language') {
        return mockLanguageT;
      }
      if (namespace === 'common') {
        return mockCommonT;
      }
      return jest.fn();
    });

    useRouter.mockReturnValue(mockRouter);
    useAuthStore.mockReturnValue({ logout: mockLogout });
    useGlobalStore.mockReturnValue('th');
    getImageUrl.mockReturnValue('https://example.com/avatar.jpg');

    // Setup translation mocks
    mockLanguageT.mockImplementation((key: string) => {
      const translations: Record<string, string> = {
        thai: 'Thai',
        english: 'English',
      };
      return translations[key] || key;
    });

    mockCommonT.mockImplementation((key: string) => {
      const translations: Record<string, string> = {
        general: 'General',
        'my-profile': 'My Profile',
        'contact-us': 'Contact Us',
        language: 'Language',
        others: 'Others',
        logout: 'Logout',
      };
      return translations[key] || key;
    });
  });

  describe('Component Rendering', () => {
    it('renders profile component with user information', () => {
      render(
        <TestWrapper>
          <Profile user={mockUser} />
        </TestWrapper>
      );

      expect(screen.getByText('John')).toBeInTheDocument();
      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    it('renders without user information', () => {
      render(
        <TestWrapper>
          <Profile />
        </TestWrapper>
      );

      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    it('renders avatar with image URL when available', () => {
      render(
        <TestWrapper>
          <Profile user={mockUser} />
        </TestWrapper>
      );

      const avatar = screen.getByRole('img');
      expect(avatar).toHaveAttribute('src', 'https://example.com/avatar.jpg');
    });

    it('renders avatar with initials when no image URL', () => {
      const { getImageUrl } = require('utils');
      getImageUrl.mockReturnValue('');

      render(
        <TestWrapper>
          <Profile user={mockUser} />
        </TestWrapper>
      );

      expect(screen.getByText('D')).toBeInTheDocument(); // First letter of lastName
    });

    it('renders empty avatar when no user', () => {
      render(
        <TestWrapper>
          <Profile />
        </TestWrapper>
      );

      // Should render button without crashing
      expect(screen.getByRole('button')).toBeInTheDocument();
    });
  });

  describe('Menu Interactions', () => {
    it('opens menu when profile button is clicked', () => {
      render(
        <TestWrapper>
          <Profile user={mockUser} />
        </TestWrapper>
      );

      const profileButton = screen.getByRole('button');
      fireEvent.click(profileButton);

      expect(screen.getByText('General')).toBeInTheDocument();
      expect(screen.getByText('My Profile')).toBeInTheDocument();
      expect(screen.getByText('Contact Us')).toBeInTheDocument();
      expect(screen.getByText('Language')).toBeInTheDocument();
      expect(screen.getByText('Others')).toBeInTheDocument();
      expect(screen.getByText('Logout')).toBeInTheDocument();
    });

    it('closes menu when clicking menu item', () => {
      render(
        <TestWrapper>
          <Profile user={mockUser} />
        </TestWrapper>
      );

      const profileButton = screen.getByRole('button');
      fireEvent.click(profileButton);

      // Menu should be open
      expect(screen.getByText('General')).toBeInTheDocument();

      // Click on menu item to close - this should trigger navigation and close menu
      const menuItem = screen.getByText('My Profile');
      fireEvent.click(menuItem);

      // Verify navigation was called
      expect(mockRouter.push).toHaveBeenCalledWith('/profile');
    });
  });

  describe('Navigation', () => {
    it('navigates to profile page when My Profile is clicked', () => {
      render(
        <TestWrapper>
          <Profile user={mockUser} />
        </TestWrapper>
      );

      const profileButton = screen.getByRole('button');
      fireEvent.click(profileButton);

      const myProfileItem = screen.getByText('My Profile');
      fireEvent.click(myProfileItem);

      expect(mockRouter.push).toHaveBeenCalledWith('/profile');
    });

    it('navigates to contact us page when Contact Us is clicked', () => {
      render(
        <TestWrapper>
          <Profile user={mockUser} />
        </TestWrapper>
      );

      const profileButton = screen.getByRole('button');
      fireEvent.click(profileButton);

      const contactUsItem = screen.getByText('Contact Us');
      fireEvent.click(contactUsItem);

      expect(mockRouter.push).toHaveBeenCalledWith('/contact-us');
    });
  });

  describe('Language Selection', () => {
    it('renders language options with flags', () => {
      render(
        <TestWrapper>
          <Profile user={mockUser} />
        </TestWrapper>
      );

      const profileButton = screen.getByRole('button');
      fireEvent.click(profileButton);

      expect(screen.getByText('Thai (TH)')).toBeInTheDocument();
      expect(screen.getByText('English (EN)')).toBeInTheDocument();
      expect(screen.getByTestId('th-flag')).toBeInTheDocument();
      expect(screen.getByTestId('gb-flag')).toBeInTheDocument();
    });

    it('renders language selection options', () => {
      render(
        <TestWrapper>
          <Profile user={mockUser} />
        </TestWrapper>
      );

      const profileButton = screen.getByRole('button');
      fireEvent.click(profileButton);

      const englishOption = screen.getByText('English (EN)');

      // Should render language option
      expect(englishOption).toBeInTheDocument();
    });

    it('shows selected language as highlighted', () => {
      const { useGlobalStore } = require('store/useGlobalStore');
      useGlobalStore.mockReturnValue('en');

      render(
        <TestWrapper>
          <Profile user={mockUser} />
        </TestWrapper>
      );

      const profileButton = screen.getByRole('button');
      fireEvent.click(profileButton);

      // The selected language should have the selected attribute (MUI uses selected prop)
      const englishOption = screen.getByText('English (EN)').closest('[role="menuitem"]');
      expect(englishOption).toHaveClass('Mui-selected');
    });
  });

  describe('Logout Functionality', () => {
    it('calls logout when logout menu item is clicked', () => {
      render(
        <TestWrapper>
          <Profile user={mockUser} />
        </TestWrapper>
      );

      const profileButton = screen.getByRole('button');
      fireEvent.click(profileButton);

      const logoutItem = screen.getByText('Logout');
      fireEvent.click(logoutItem);

      expect(mockLogout).toHaveBeenCalled();
    });
  });

  describe('Locale Store Integration', () => {
    it('updates locale state when locale store changes', () => {
      const { useGlobalStore } = require('store/useGlobalStore');

      // Initially return 'th'
      useGlobalStore.mockReturnValue('th');

      const { rerender } = render(
        <TestWrapper>
          <Profile user={mockUser} />
        </TestWrapper>
      );

      // Change to 'en'
      useGlobalStore.mockReturnValue('en');

      rerender(
        <TestWrapper>
          <Profile user={mockUser} />
        </TestWrapper>
      );

      const profileButton = screen.getByRole('button');
      fireEvent.click(profileButton);

      // English should be selected
      const englishOption = screen.getByText('English (EN)').closest('[role="menuitem"]');
      expect(englishOption).toHaveClass('Mui-selected');
    });

    it('handles undefined locale store gracefully', () => {
      const { useGlobalStore } = require('store/useGlobalStore');
      useGlobalStore.mockReturnValue(undefined);

      render(
        <TestWrapper>
          <Profile user={mockUser} />
        </TestWrapper>
      );

      const profileButton = screen.getByRole('button');
      fireEvent.click(profileButton);

      // Should render without errors
      expect(screen.getByText('Thai (TH)')).toBeInTheDocument();
      expect(screen.getByText('English (EN)')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles missing translations gracefully', () => {
      mockLanguageT.mockReturnValue('missing-translation');
      mockCommonT.mockReturnValue('missing-translation');

      render(
        <TestWrapper>
          <Profile user={mockUser} />
        </TestWrapper>
      );

      const profileButton = screen.getByRole('button');
      fireEvent.click(profileButton);

      expect(screen.getAllByText('missing-translation').length).toBeGreaterThan(0);
    });

    it('handles component errors gracefully', () => {
      render(
        <TestWrapper>
          <Profile user={mockUser} />
        </TestWrapper>
      );

      // Should not crash the component
      expect(screen.getByRole('button')).toBeInTheDocument();

      // Open menu and verify it renders
      const profileButton = screen.getByRole('button');
      fireEvent.click(profileButton);
      expect(screen.getByText('General')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      render(
        <TestWrapper>
          <Profile user={mockUser} />
        </TestWrapper>
      );

      const profileButton = screen.getByRole('button');
      expect(profileButton).toHaveAttribute('aria-haspopup', 'true');

      fireEvent.click(profileButton);

      // After clicking, menu should be visible
      expect(screen.getByText('General')).toBeInTheDocument();
    });

    it('has proper menu structure', () => {
      render(
        <TestWrapper>
          <Profile user={mockUser} />
        </TestWrapper>
      );

      const profileButton = screen.getByRole('button');
      fireEvent.click(profileButton);

      // Menu should be visible with menu items
      const menuItems = screen.getAllByRole('menuitem');
      expect(menuItems.length).toBeGreaterThan(0);
    });
  });

  describe('Component State Management', () => {
    it('manages anchor element state correctly', () => {
      render(
        <TestWrapper>
          <Profile user={mockUser} />
        </TestWrapper>
      );

      const profileButton = screen.getByRole('button');

      // Initially menu should not be visible
      expect(screen.queryByText('General')).not.toBeInTheDocument();

      // Open menu
      fireEvent.click(profileButton);
      expect(screen.getByText('General')).toBeInTheDocument();

      // Close menu by clicking menu item
      const myProfileItem = screen.getByText('My Profile');
      fireEvent.click(myProfileItem);

      // Verify navigation was called
      expect(mockRouter.push).toHaveBeenCalledWith('/profile');
    });
  });
});
