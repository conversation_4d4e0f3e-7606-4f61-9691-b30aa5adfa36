'use client';
import { Backdrop, Box, useTheme } from '@mui/material';
import { FC, PropsWithChildren } from 'react';
import { headerHeight } from './constant';
import { CircularWithValueLabel } from 'components';


export const LoadingLayout: FC<PropsWithChildren & { loading: boolean; id?: string }> = ({
  loading,
  children,
  id = 'loading-layout',
}) => {
  const theme = useTheme();

  return (
    <Box
      component="div"
      id={id}
      sx={{
        width: '100%',
        flex: 1,
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
        boxSizing: 'border-box',
        height: `calc(100vh - ${headerHeight}px)`,
        overflow: 'auto',
      }}
    >
      <Box
        sx={{
          width: '100%',
          height: '100%',
          position: 'relative',
          overflow: loading ? 'hidden' : 'visible',
        }}
      >
        <Backdrop
          sx={{
            color: '#fff',
            backgroundColor: 'rgba(255, 255, 255, 0.8)', // White background with 80% opacity
            zIndex: theme.zIndex.drawer + 1,
            display: 'flex',
            flexDirection: 'column',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            boxShadow: 1,
            opacity: 0.8,
          }}
          open={loading}
        >
          <CircularWithValueLabel />
        </Backdrop>
        <Box
          sx={{
            width: '100%',
            height: '100%',
          }}
        >
          {children}
        </Box>
      </Box>
    </Box>
  );
};
