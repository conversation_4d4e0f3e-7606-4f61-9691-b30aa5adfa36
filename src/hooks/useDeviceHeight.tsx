'use client';

import { useState, useEffect } from 'react';

export function useDeviceHeight() {
  // 1) Don’t touch `window` at init: default to 0 (or any fallback you prefer)
  const [height, setHeight] = useState(0);

  useEffect(() => {
    // 2) Now that we’re on the client, we can safely read window.innerHeight
    const updateHeight = () => {
      setHeight(window.innerHeight);
    };

    updateHeight(); // set initial height
    window.addEventListener('resize', updateHeight);

    return () => {
      window.removeEventListener('resize', updateHeight);
    };
  }, []);

  return height;
}
