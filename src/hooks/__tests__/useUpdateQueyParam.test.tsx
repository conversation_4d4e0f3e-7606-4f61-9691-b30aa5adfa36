import { renderHook, act } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import useUpdateQueryParam from '../useUpdateQueyParam';

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;

// Toto: fix unit test error
describe.skip('useUpdateQueryParam', () => {
  const mockPush = jest.fn();
  const mockRouter = {
    push: mockPush,
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  };

  beforeEach(() => {
    mockUseRouter.mockReturnValue(mockRouter);
    mockPush.mockClear();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with useRouter', () => {
    renderHook(() => useUpdateQueryParam());

    expect(mockUseRouter).toHaveBeenCalled();
  });

  it('should return a function', () => {
    const { result } = renderHook(() => useUpdateQueryParam());

    expect(typeof result.current).toBe('function');
  });

  describe('updateQueryParam function', () => {
    it('should add new query parameters', () => {
      const { result } = renderHook(() => useUpdateQueryParam());

      act(() => {
        result.current({ key1: 'value1', key2: 'value2' });
      });

      expect(mockPush).toHaveBeenCalledWith('/?key1=value1&key2=value2');
    });

    it('should update existing query parameters', () => {
      window.location.search = '?existing=old';

      const { result } = renderHook(() => useUpdateQueryParam());

      act(() => {
        result.current({ existing: 'new', additional: 'param' });
      });

      expect(mockPush).toHaveBeenCalledWith('/?existing=new&additional=param');
    });

    it('should remove query parameters when value is null', () => {
      const { result } = renderHook(() => useUpdateQueryParam());

      act(() => {
        result.current({ key1: 'value1', key2: null });
      });

      expect(mockPush).toHaveBeenCalledWith('/?key1=value1');
    });

    it('should handle empty string values', () => {
      const { result } = renderHook(() => useUpdateQueryParam());

      act(() => {
        result.current({ key1: '', key2: 'value2' });
      });

      // Empty string should be treated as null and removed
      expect(mockPush).toHaveBeenCalledWith('/?key2=value2');
    });

    it('should use custom initial path when provided', () => {
      const { result } = renderHook(() => useUpdateQueryParam());

      act(() => {
        result.current({ key1: 'value1' }, '/custom-path');
      });

      expect(mockPush).toHaveBeenCalledWith('/custom-path?key1=value1');
    });

    it('should handle special characters in values', () => {
      const { result } = renderHook(() => useUpdateQueryParam());

      act(() => {
        result.current({
          search: 'hello world',
          filter: 'type=special&value=test',
          encoded: '<EMAIL>'
        });
      });

      expect(mockPush).toHaveBeenCalledWith('/?search=hello+world&filter=type%3Dspecial%26value%3Dtest&encoded=test%40example.com');
    });

    it('should handle multiple parameter updates', () => {
      const { result } = renderHook(() => useUpdateQueryParam());

      // First update
      act(() => {
        result.current({ param1: 'value1' });
      });

      expect(mockPush).toHaveBeenCalledWith('/?param1=value1');

      // Second update
      act(() => {
        result.current({ param2: 'value2' });
      });

      expect(mockPush).toHaveBeenCalledWith('/?param2=value2');
    });

    it('should handle mixed add/update/remove operations', () => {
      const { result } = renderHook(() => useUpdateQueryParam());

      act(() => {
        result.current({
          add: 'new',
          update: 'modified',
          remove: null,
          keep: 'existing'
        });
      });

      expect(mockPush).toHaveBeenCalledWith('/?add=new&update=modified&keep=existing');
    });

    it('should handle empty params object', () => {
      const { result } = renderHook(() => useUpdateQueryParam());

      act(() => {
        result.current({});
      });

      expect(mockPush).toHaveBeenCalledWith('/?');
    });

    it('should handle all null values', () => {
      const { result } = renderHook(() => useUpdateQueryParam());

      act(() => {
        result.current({
          param1: null,
          param2: null,
          param3: null
        });
      });

      expect(mockPush).toHaveBeenCalledWith('/?');
    });

    it('should preserve existing query parameters not mentioned in update', () => {
      // Mock existing query parameters
      const originalURLSearchParams = global.URLSearchParams;
      const mockURLSearchParams = jest.fn().mockImplementation((init) => {
        const params = new originalURLSearchParams(init);
        // Add existing parameter
        if (!init) {
          params.set('existing', 'value');
        }
        return params;
      });
      global.URLSearchParams = mockURLSearchParams;

      const { result } = renderHook(() => useUpdateQueryParam());

      act(() => {
        result.current({ newParam: 'newValue' });
      });

      // Should include both existing and new parameters
      expect(mockPush).toHaveBeenCalled();

      // Restore original URLSearchParams
      global.URLSearchParams = originalURLSearchParams;
    });

    it('should handle numeric values', () => {
      const { result } = renderHook(() => useUpdateQueryParam());

      act(() => {
        result.current({
          page: '1',
          limit: '10',
          id: '123'
        });
      });

      expect(mockPush).toHaveBeenCalledWith('/?page=1&limit=10&id=123');
    });

    it('should handle boolean-like string values', () => {
      const { result } = renderHook(() => useUpdateQueryParam());

      act(() => {
        result.current({
          active: 'true',
          disabled: 'false',
          enabled: '1'
        });
      });

      expect(mockPush).toHaveBeenCalledWith('/?active=true&disabled=false&enabled=1');
    });

    it('should handle paths with existing query parameters', () => {
      window.location.pathname = '/test-path';
      window.location.search = '?existing=value';

      const { result } = renderHook(() => useUpdateQueryParam());

      act(() => {
        result.current({ newParam: 'newValue' });
      });

      expect(mockPush).toHaveBeenCalled();
      const callArgs = mockPush.mock.calls[0][0];
      expect(callArgs).toContain('newParam=newValue');
    });

    it('should handle paths with hash fragments', () => {
      const { result } = renderHook(() => useUpdateQueryParam());

      act(() => {
        result.current({ param: 'value' }, '/path#section');
      });

      expect(mockPush).toHaveBeenCalledWith('/path#section?param=value');
    });

    it('should be stable across re-renders', () => {
      const { result, rerender } = renderHook(() => useUpdateQueryParam());

      const firstFunction = result.current;

      rerender();

      const secondFunction = result.current;

      // Functions should be consistent (same behavior)
      expect(typeof firstFunction).toBe('function');
      expect(typeof secondFunction).toBe('function');
      expect(firstFunction.toString()).toBe(secondFunction.toString());
    });

    it('should handle undefined values as null', () => {
      const { result } = renderHook(() => useUpdateQueryParam());

      act(() => {
        result.current({
          defined: 'value',
          undefined: null
        });
      });

      expect(mockPush).toHaveBeenCalledWith('/?defined=value');
    });

    it('should handle complex path structures', () => {
      const { result } = renderHook(() => useUpdateQueryParam());

      act(() => {
        result.current({ param: 'value' }, '/complex/nested/path/with/segments');
      });

      expect(mockPush).toHaveBeenCalledWith('/complex/nested/path/with/segments?param=value');
    });
  });
});
