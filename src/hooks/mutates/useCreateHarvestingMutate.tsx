import { useMutation, useQueryClient } from '@tanstack/react-query';
import { createHarvesting } from 'services/event.service';
import { useRouter } from 'next/navigation';
import { clientRoutes } from 'routes/client-routes';
import { queryKeys } from 'hooks/queries/_key';
import { CreateHarvesting } from 'types';
import { useToastStore } from 'store/useToastStore';
import { useTranslations } from 'next-intl';
import toastMessages from 'utils/toastMessages';

export function useCreateHarvestingMutate() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const setToast = useToastStore((state) => state.setToast);
  const receivingTranslation = useTranslations('receive');
  const commonTranslation = useTranslations('common');

  return useMutation({
    mutationFn: (args: CreateHarvesting) => createHarvesting(args),
    onSuccess: async (data, variables) => {
      await queryClient.invalidateQueries({
        queryKey: [queryKeys.EVENT, queryKeys.HARVEST, queryKeys.RECEIVING],
      });

      const message =
        variables.status === 'draft'
          ? receivingTranslation('save-as-draft-success')
          : receivingTranslation('create-success');

      setToast({
        message: message,
        type: 'success',
      });

      router.push(clientRoutes.eventIncoming);
    },
    onError: () => {
      toastMessages.error(commonTranslation('data-outdated-error'));
    },
  });
}
