'use client';

import { useQueries, useQuery } from '@tanstack/react-query';
import { get } from 'lodash-es';
import { getPackagingMasterDataService, fetchProvinceService } from 'services/resource.service';
import { useUserStore } from 'store/useUserStore';
import { GetMasterDataResponse, Province } from 'types';

export const useMasterDataQuery = () => {
  const user = useUserStore((state) => state.user);
  return useQuery({
    queryKey: ['getPackagingMasterData'],
    queryFn: () => getPackagingMasterDataService(),
    select: (response) => {
      return response.data;
    },
    enabled: !!user,
  });
};

export const useProvinceDataQuery = () => {
  const user = useUserStore((state) => state.user);
  return useQuery({
    queryKey: ['getProvinceData'],
    queryFn: () => fetchProvinceService(),
    select: (response) => {
      return response.data;
    },
    enabled: !!user,
  });
};

export const useAllMasterDataQuery = () => {
  const user = useUserStore((state) => state.user);
  return useQueries({
    queries: [
      {
        queryKey: ['getPackagingMasterData'],
        queryFn: () => getPackagingMasterDataService(),
        select: (response) => {
          return get(response, 'data', {}) as GetMasterDataResponse;
        },
        enabled: !!user,
      },
      {
        queryKey: ['getProvinceData'],
        queryFn: () => fetchProvinceService(),
        select: (response) => {
          return get(response, 'data', []) as Province[];
        },
        enabled: !!user,
      },
    ],
  });
};
