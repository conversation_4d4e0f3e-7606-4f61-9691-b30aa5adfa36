'use client';

import { useQuery } from '@tanstack/react-query';
import { getDetailQueryKey } from './_key';
import { getQrDetail } from 'services/qr.service';
import { QrDetail } from 'types';

export const useQrDetailQuery = (id: string, onSuccess?: (qrDetail: QrDetail) => void) => {
  return useQuery({
    queryKey: getDetailQueryKey('QR', id),
    queryFn: async () => {
      const res = await getQrDetail(id);
      onSuccess?.(res);
      return res;
    },
    enabled: Boolean(id),
  });
};
