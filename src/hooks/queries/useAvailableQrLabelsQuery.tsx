'use client';

import { useQuery } from '@tanstack/react-query';
import { queryKeys } from './_key';
import { getListEvents, GetListEventsParam } from 'services/event.service';
import { EventStatusEnum } from 'types';

export const getAvailableQrLabelsQueryKey = ({ page, search }: GetListEventsParam) => [
  queryKeys.AVAILABLE_QR,
  page,
  search,
];

export const useAvailableQrLabelsQuery = ({ page, search }: Pick<GetListEventsParam, 'page' | 'search'>) => {
  return useQuery({
    queryKey: getAvailableQrLabelsQueryKey({ page, search }),
    queryFn: () =>
      getListEvents({
        page,
        size: 3,
        search,
        eventType: 'qr',
        status: EventStatusEnum.AVAILABLE,
      }),
    staleTime: 1000 * 60 * 5, // 5m
    gcTime: 1000 * 60 * 5, // 5m
  });
};
