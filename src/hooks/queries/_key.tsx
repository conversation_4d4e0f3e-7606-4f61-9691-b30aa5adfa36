import { QueryKey } from '@tanstack/react-query';

export const queryKeys = Object.freeze({
  EVENT: 'event',
  SHIPMENT: 'shipments',
  HARVEST: 'harvesting-event-data',
  RECEIVING: 'receiving-event-data',
  QR: 'qr-management',
  AVAILABLE_QR: 'available-qr-to-select',
});

type Key = keyof typeof queryKeys;

export const getListingQueryKey = (key: Key): QueryKey => {
  return [queryKeys[key]];
};

export const getDetailQueryKey = (key: Key, id: string): QueryKey => {
  return [queryKeys[key], id];
};
