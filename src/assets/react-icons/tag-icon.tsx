import { SvgIcon, SvgIconProps } from '@mui/material';
import { FC } from 'react';

export const TagIcon: FC<SvgIconProps> = (props) => {
  return (
    <SvgIcon {...props}>
      <svg width={props.width} height={props.height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clipPath="url(#clip0_11133_18376)">
          <path
            d="M8.94336 0.391602H8.9502L14.5322 0.919922C14.8213 0.948467 15.0514 1.1768 15.0781 1.46582L15.6064 7.04785C15.6091 7.08806 15.6001 7.12729 15.5801 7.16016C15.5735 7.17098 15.5655 7.18116 15.5566 7.19043L7.19434 15.5527L7.13672 15.5908C7.11538 15.5995 7.09238 15.6044 7.06836 15.6045C7.02014 15.6045 6.97533 15.5867 6.94141 15.5527L0.445312 9.05664L0.40625 8.99902C0.397289 8.97742 0.393555 8.95405 0.393555 8.93066C0.393573 8.90758 0.39747 8.88463 0.40625 8.86328C0.415161 8.8418 0.428866 8.82211 0.445312 8.80566L8.80762 0.442383C8.84141 0.408683 8.88644 0.391602 8.94336 0.391602ZM2.2373 8.93164L7.06641 13.7607L14.2041 6.62305L13.7861 2.21191L9.375 1.79395L2.2373 8.93164ZM11 3.42676C11.1083 3.42676 11.2142 3.43799 11.3164 3.45898C11.4696 3.49046 11.6145 3.54451 11.748 3.61719C12.0151 3.76252 12.2356 3.98293 12.3809 4.25C12.405 4.2944 12.4274 4.33988 12.4473 4.38672C12.5071 4.52782 12.5464 4.67946 12.5625 4.83789C12.5679 4.8905 12.5713 4.94405 12.5713 4.99805C12.5713 5.05214 12.5679 5.1055 12.5625 5.1582C12.5518 5.2635 12.5306 5.3657 12.5 5.46387C12.4693 5.56243 12.4294 5.65685 12.3809 5.74609C12.1144 6.23552 11.5951 6.56836 11 6.56836C10.6755 6.56833 10.3737 6.46928 10.123 6.2998C9.95568 6.18666 9.81141 6.04235 9.69824 5.875C9.55697 5.66608 9.46439 5.42173 9.4375 5.1582C9.43212 5.10551 9.4297 5.05212 9.42969 4.99805C9.42969 4.94406 9.43215 4.89049 9.4375 4.83789C9.4965 4.25773 9.87288 3.7698 10.3896 3.55078C10.4837 3.51091 10.5823 3.47996 10.6846 3.45898C10.7865 3.43808 10.892 3.42677 11 3.42676ZM11 4.42578C10.8023 4.42583 10.628 4.52557 10.5254 4.67773C10.4843 4.73863 10.4546 4.80822 10.4395 4.88281C10.4319 4.91993 10.4277 4.95868 10.4277 4.99805C10.4278 5.03751 10.4319 5.07609 10.4395 5.11328C10.4546 5.18755 10.4845 5.25671 10.5254 5.31738C10.628 5.46971 10.8021 5.56928 11 5.56934C11.1979 5.56934 11.3719 5.46967 11.4746 5.31738C11.536 5.22632 11.5722 5.11634 11.5723 4.99805C11.5723 4.84004 11.5086 4.69612 11.4053 4.59277C11.3018 4.48929 11.1582 4.42578 11 4.42578Z"
            fill="currentColor"
            stroke="currentColor"
            strokeWidth="0.00111607"
          />
        </g>
        <defs>
          <clipPath id="clip0_11133_18376">
            <rect width="16" height="16" fill="white" />
          </clipPath>
        </defs>
      </svg>
    </SvgIcon>
  );
};
