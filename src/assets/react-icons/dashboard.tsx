import { SvgIcon, SvgIconProps } from '@mui/material';
import { FC } from 'react';

export const DashboardIcon: FC<SvgIconProps> = (props) => {
  return (
    <SvgIcon {...props}>
      <svg width={props.width} height={props.height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M12.3438 1.53711C14.361 1.53711 15.9425 1.93662 17.0029 2.99707C18.0634 4.05752 18.4629 5.63902 18.4629 7.65625V12.3438C18.4629 14.361 18.0634 15.9425 17.0029 17.0029C15.9425 18.0634 14.361 18.4629 12.3438 18.4629H7.65625C5.63902 18.4629 4.05752 18.0634 2.99707 17.0029C1.93662 15.9425 1.53711 14.361 1.53711 12.3438V7.65625C1.53711 5.63902 1.93662 4.05752 2.99707 2.99707C4.05752 1.93662 5.63902 1.53711 7.65625 1.53711H12.3438ZM7.65625 2.83789C5.76723 2.83789 4.61436 3.21963 3.91699 3.91699C3.21963 4.61436 2.83789 5.76723 2.83789 7.65625V12.3438C2.83789 14.2328 3.21963 15.3856 3.91699 16.083C4.61436 16.7804 5.76723 17.1621 7.65625 17.1621H12.3438C14.2328 17.1621 15.3856 16.7804 16.083 16.083C16.7804 15.3856 17.1621 14.2328 17.1621 12.3438V7.65625C17.1621 5.76723 16.7804 4.61436 16.083 3.91699C15.3856 3.21963 14.2328 2.83789 12.3438 2.83789H7.65625ZM7.62012 8.75488C8.68019 8.75488 9.54578 9.61964 9.5459 10.6797V13.0596C9.54564 14.1195 8.6801 14.9844 7.62012 14.9844C6.55165 14.9842 5.69557 14.1175 5.69531 13.0596V10.6797C5.69543 9.61975 6.5602 8.75506 7.62012 8.75488ZM12.3789 5.01562C13.4389 5.01562 14.3044 5.88051 14.3047 6.94043V13.0586C14.3047 14.1187 13.4391 14.9844 12.3789 14.9844C11.3104 14.984 10.4541 14.1167 10.4541 13.0586V6.94043C10.4544 5.88073 11.3192 5.01598 12.3789 5.01562ZM7.62012 9.88574C7.18493 9.88592 6.82629 10.2445 6.82617 10.6797V13.0596C6.82643 13.4965 7.18006 13.8533 7.62012 13.8535C8.05537 13.8535 8.4138 13.4948 8.41406 13.0596V10.6797C8.41394 10.2444 8.05545 9.88574 7.62012 9.88574ZM12.3789 6.14648C11.944 6.14684 11.5852 6.50546 11.585 6.94043V13.0586C11.585 13.4956 11.9388 13.8522 12.3789 13.8525C12.8143 13.8525 13.1729 13.494 13.1729 13.0586V6.94043C13.1726 6.50524 12.8142 6.14648 12.3789 6.14648Z"
          fill="currentColor"
        />
      </svg>
    </SvgIcon>
  );
};
