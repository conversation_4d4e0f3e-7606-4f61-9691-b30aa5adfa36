import { SvgIcon, SvgIconProps } from '@mui/material';
import { FC } from 'react';

export const DashboardSolidIcon: FC<SvgIconProps> = (props) => {
  return (
    <SvgIcon {...props}>
      <svg width={props.width} height={props.height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M11.9902 1.11133L12.3643 1.11621C14.2125 1.16412 15.6871 1.57887 16.7002 2.5918C17.7808 3.67241 18.1816 5.27838 18.1816 7.30273V11.9902C18.1816 14.0145 17.7808 15.6196 16.7002 16.7002C15.6196 17.7808 14.0145 18.1816 11.9902 18.1816H7.30273C5.27838 18.1816 3.67241 17.7808 2.5918 16.7002C1.57887 15.6871 1.16412 14.2125 1.11621 12.3643L1.11133 11.9902V7.30273C1.11133 5.27838 1.51118 3.67241 2.5918 2.5918C3.67241 1.51118 5.27838 1.11133 7.30273 1.11133H11.9902ZM7.2832 9.80859C6.74996 9.8087 6.31641 10.2421 6.31641 10.7754V13.4912C6.31641 14.0245 6.74996 14.4579 7.2832 14.458C7.82487 14.458 8.25879 14.0245 8.25879 13.4912V10.7754C8.25879 10.2421 7.82487 9.80859 7.2832 9.80859ZM12.7168 5.54199C12.1751 5.54199 11.7422 5.97546 11.7422 6.50879V13.4912C11.7422 14.0245 12.1751 14.458 12.7168 14.458C13.2501 14.458 13.6836 14.0245 13.6836 13.4912V6.50879C13.6836 5.97548 13.2501 5.54203 12.7168 5.54199Z"
          fill="currentColor"
        />
      </svg>
    </SvgIcon>
  );
};
