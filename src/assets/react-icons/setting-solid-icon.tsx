import { SvgIcon, SvgIconProps } from '@mui/material';
import { FC } from 'react';

export const SettingSolidIcon: FC<SvgIconProps> = (props) => {
  return (
    <SvgIcon {...props}>
      <svg width={props.width} height={props.height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M6.59473 3.58553C7.38011 3.12457 8.35788 3.36509 8.875 4.06502L8.97266 4.21151L8.97656 4.21834L9.04883 4.34237L9.0498 4.3453L9.13379 4.47713C9.32636 4.75131 9.49532 4.79061 9.56348 4.79061C9.64249 4.79054 9.8538 4.7378 10.083 4.34237L10.1553 4.21834L10.1592 4.21151L10.2559 4.06502C10.7726 3.3652 11.7499 3.12646 12.5352 3.58651L12.5361 3.58553L13.6719 4.23494L13.835 4.34041C14.5659 4.86374 14.8246 5.83848 14.459 6.65291L14.3711 6.82479C14.1427 7.21887 14.2014 7.42757 14.2402 7.49569C14.2791 7.56317 14.4293 7.71932 14.8848 7.71932C15.9239 7.71936 16.781 8.56671 16.7812 9.61678V10.7711C16.7812 11.8103 15.935 12.6685 14.8848 12.6685C14.4305 12.6685 14.2796 12.8234 14.2402 12.8912C14.2063 12.9502 14.1563 13.1177 14.2979 13.4234L14.3711 13.5631L14.373 13.566L14.46 13.7389C14.8492 14.6142 14.5269 15.6642 13.6719 16.1529L13.6709 16.1519L12.5361 16.8023L12.5352 16.8014C11.7498 17.2615 10.7726 17.0228 10.2559 16.3228L10.1592 16.1764L10.1553 16.1695L10.083 16.0455L10.0811 16.0426C9.85531 15.6491 9.64629 15.5973 9.56836 15.5972C9.49935 15.5972 9.3289 15.6377 9.13379 15.9127L9.04883 16.0455L8.97656 16.1695L8.97266 16.1764C8.48989 16.9878 7.43272 17.2933 6.59473 16.8014L5.45996 16.1519C4.54862 15.6311 4.24021 14.464 4.76074 13.5631L4.83398 13.4234C4.9755 13.1178 4.92558 12.9502 4.8916 12.8912C4.85725 12.832 4.7372 12.706 4.40332 12.6754L4.24707 12.6685C3.1968 12.6685 2.34961 11.8103 2.34961 10.7711V9.61678C2.34982 8.57769 3.19693 7.71932 4.24707 7.71932C4.70234 7.71928 4.85272 7.56316 4.8916 7.49569C4.92557 7.436 4.97498 7.26882 4.83398 6.96444L4.76074 6.82479C4.24041 5.92386 4.54859 4.75573 5.45996 4.23494L6.59473 3.58553ZM9.5 7.64119C8.15765 7.64119 7.06275 8.73641 7.0625 10.0787C7.0625 11.4212 8.1575 12.5162 9.5 12.5162C10.8425 12.5162 11.9375 11.4212 11.9375 10.0787C11.9372 8.73641 10.8423 7.64119 9.5 7.64119Z"
          fill="currentColor"
        />
      </svg>
    </SvgIcon>
  );
};
