import { SvgIcon, SvgIconProps } from '@mui/material';
import { FC } from 'react';

export const ReceiveIcon: FC<SvgIconProps> = (props) => {
  return (
    <SvgIcon {...props}>
      <svg xmlns="http://www.w3.org/2000/svg" width={props.width} height={props.height} viewBox="0 0 20 20" fill="none">
        <path
          d="M13.0633 1.18949C15.0205 1.24035 16.5562 1.67947 17.6053 2.72855C18.7241 3.84754 19.1492 5.5199 19.1492 7.66703V12.667C19.1492 14.8143 18.7243 16.4875 17.6053 17.6065C16.4862 18.7253 14.8131 19.1504 12.6658 19.1504H7.66583C5.51879 19.1504 3.8463 18.7253 2.72736 17.6065C1.67833 16.5575 1.23919 15.0215 1.18829 13.0645L1.18243 12.667V7.66703C1.18248 5.51992 1.60858 3.84754 2.72736 2.72855C3.8463 1.6096 5.51864 1.1837 7.66583 1.18363H12.6658L13.0633 1.18949ZM7.66583 2.48441C5.64692 2.48448 4.4022 2.8916 3.6463 3.6475C2.89056 4.40345 2.48326 5.64824 2.48322 7.66703V12.667L2.48712 13.0371C2.53475 14.8398 2.93786 15.9779 3.6463 16.6866C4.4022 17.4424 5.64698 17.8506 7.66583 17.8506H12.6658C14.6848 17.8506 15.9294 17.4423 16.6854 16.6866C17.4413 15.9306 17.8494 14.686 17.8494 12.667V7.66703C17.8494 5.64807 17.4412 4.40343 16.6854 3.6475C15.9767 2.93901 14.8388 2.53591 13.0359 2.48832L12.6658 2.48441H7.66583ZM10.1658 4.51371C10.5653 4.51266 10.993 4.5848 11.366 4.75297L11.5242 4.83207L14.1912 6.31547H14.1902C14.6207 6.55225 14.972 6.94238 15.2137 7.35355C15.4559 7.76582 15.6258 8.26266 15.6258 8.75101V11.584C15.6257 12.0746 15.4524 12.5717 15.2098 12.9825C14.9976 13.3416 14.7026 13.6845 14.3475 13.9229L14.1912 14.0186L11.5242 15.502L11.366 15.5811C10.9931 15.7493 10.5661 15.8213 10.1678 15.8213C9.71318 15.8213 9.22099 15.7271 8.81622 15.501V15.5L6.15118 14.0186V14.0176C5.72127 13.7808 5.37021 13.3913 5.12872 12.9805C4.88652 12.5683 4.71669 12.0723 4.71661 11.584V8.75883C4.71663 8.26814 4.88994 7.77119 5.13263 7.36039C5.37527 6.94978 5.72594 6.56057 6.15118 6.32426L8.81036 4.84574V4.84476C9.21641 4.61202 9.71006 4.51503 10.1658 4.51371ZM10.8172 10.8164V14.3985L10.8953 14.3643L13.5594 12.8829L13.6942 12.7911C13.831 12.682 13.9727 12.5208 14.0906 12.3213C14.2478 12.0552 14.3249 11.7849 14.325 11.584V8.78226L10.8172 10.8164ZM6.0174 11.584C6.01748 11.7871 6.0934 12.0578 6.24884 12.3223C6.36567 12.521 6.50689 12.6816 6.64435 12.7901L6.77911 12.8809L6.78302 12.8829L9.45001 14.3662H9.45099L9.51642 14.3955V10.8164L6.0174 8.79008V11.584ZM10.1697 5.81449C9.87391 5.81533 9.61726 5.88078 9.45685 5.97269L9.45001 5.9766L6.78302 7.46C6.7173 7.49651 6.64911 7.54885 6.5799 7.61234L10.1658 9.69047L13.7635 7.60355C13.6954 7.54172 13.6288 7.49011 13.5633 7.45414L13.5594 7.45219L10.8924 5.96879V5.96781C10.7245 5.87628 10.4633 5.81374 10.1697 5.81449Z"
          fill="currentColor"
        />
      </svg>
    </SvgIcon>
  );
};
