import { SvgIcon, SvgIconProps } from '@mui/material';
import { FC } from 'react';

export const SettingIcon: FC<SvgIconProps> = (props) => {
  return (
    <SvgIcon {...props}>
      <svg width={props.width} height={props.height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M9 11.8125C10.2081 11.8125 11.1875 10.8331 11.1875 9.625C11.1875 8.41688 10.2081 7.4375 9 7.4375C7.79188 7.4375 6.8125 8.41688 6.8125 9.625C6.8125 10.8331 7.79188 11.8125 9 11.8125Z"
          stroke="currentColor"
          strokeWidth="1.3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M1.70825 10.2666V8.9833C1.70825 8.22496 2.32804 7.59788 3.09367 7.59788C4.41346 7.59788 4.95304 6.66455 4.2895 5.51976C3.91034 4.86351 4.13638 4.01038 4.79992 3.63121L6.06138 2.90934C6.63742 2.56663 7.38117 2.7708 7.72388 3.34684L7.80408 3.48538C8.46033 4.63017 9.5395 4.63017 10.203 3.48538L10.2833 3.34684C10.626 2.7708 11.3697 2.56663 11.9458 2.90934L13.2072 3.63121C13.8708 4.01038 14.0968 4.86351 13.7176 5.51976C13.0541 6.66455 13.5937 7.59788 14.9135 7.59788C15.6718 7.59788 16.2989 8.21767 16.2989 8.9833V10.2666C16.2989 11.025 15.6791 11.652 14.9135 11.652C13.5937 11.652 13.0541 12.5854 13.7176 13.7302C14.0968 14.3937 13.8708 15.2395 13.2072 15.6187L11.9458 16.3406C11.3697 16.6833 10.626 16.4791 10.2833 15.9031L10.203 15.7645C9.54679 14.6198 8.46763 14.6198 7.80408 15.7645L7.72388 15.9031C7.38117 16.4791 6.63742 16.6833 6.06138 16.3406L4.79992 15.6187C4.13638 15.2395 3.91034 14.3864 4.2895 13.7302C4.95304 12.5854 4.41346 11.652 3.09367 11.652C2.32804 11.652 1.70825 11.025 1.70825 10.2666Z"
          stroke="currentColor"
          strokeWidth="1.3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </SvgIcon>
  );
};
