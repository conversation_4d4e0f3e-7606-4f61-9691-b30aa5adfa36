import dayjs from 'dayjs';
import { Receipt } from './master-data';
import { QrDetail } from './qr';
import { TranslateLabel } from './durian';

export interface CreateShipment {
  status: 'draft' | 'published' | 'completed';
  shipmentName: string;
  receivingProductIds?: string[];
  receipt?: Receipt;
  latitude: number;
  longitude: number;
  packages?: ShipmentPackage[];
  documentIds?: string[];
  shipmentPhotoIds?: string[];
  formStep?: number;
}

export interface ShipmentPackage {
  brandName: string;
  productType: string;
  varietyId: string;
  gradeId: string;
  weightKg: number;
  numberOfBoxes: number;
  qrCode: string;
  batchlot: string;
  varietyName?: string;
}


export type VarietyOptionValue = {
  id: string;
  label: TranslateLabel;
  isOther: boolean;
  name?: string;
  originalId: string;
};

export type ShipmentIdentityType = {
  name: string;
};

export enum FormShipmentStepEnum {
  SelectedProductIds,
  ShipmentIdentity,
  DurianInformationStep,
  ReceiptStep,
  UploadDocumentStep,
  QrReviewStep,
}

export type DurianInformationForm = {
  brand: string;
  boxType: string;
  variety: string;
  grade: string;
  netWeight: string;
  totalBoxes: string;
  id: string;
  packingDate: dayjs.Dayjs | number | null;
  batchlot?: string;
  qrUrl?: string;
  qrId?: string;
  qrDetail?: Pick<
    QrDetail,
    'nameOfExportingCompany' | 'orchardRegisterNumber' | 'packingDate' | 'exportTo' | 'productType'
  >;
  qrCodeId?: string;
  notMatchedQrLabel?: boolean;
  notMatchedQrLabels?: { field: string; value: boolean }[];
};

export type UploadFile = {
  name: string;
  id: string;
  file?: File;
  size: number;
  url?: string;
  type: string;
  hasUploaded?: boolean;
  isUploading?: boolean;
};
