export type PaginationRequest = {
  limit?: number;
  offset?: number;
};

export type PaginationResponse = {
  pagination: {
    total: number;
  };
};

export enum RejectReason {
  WEIGHT_GAIN = 'weight_gain',
  EARLY_HARVESTING = 'early_harvesting',
  GAP_USED = 'gap_used',
  GAP_VIOLATION = 'gap_violation',
}

export enum NotiVariant {
  INFO = 'info',
  WARNING = 'warning',
  CRITICAL = 'critical',
}

export interface NotificationType {
  id: string;
  title: string;
  description: string;
  variant: NotiVariant;
  createdTime: string;
  unread: boolean;
  batchlot: string;
  rejectReason?: RejectReason;
  extra?: {
    productId?: string;
  };
  meta?: {
    productId?: string;
  };
}

export type NotificationsRequest = PaginationRequest & {
  nextId?: string;
};

export type NotificationsResponse = {
  data: NotificationType[];
} & PaginationResponse;

export interface DeviceInfo {
  deviceToken: string;
  deviceType: 'WEB';
}
