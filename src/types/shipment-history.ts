import { DurianVariety, TranslateLabel } from './durian';

export interface ShipmentHistoryUser {
  id: string;
  name: string;
  avatar?: string;
}

export interface ShipmentHistoryPackaging {
  id: string;
  brandNameInfo: {
    id: string;
    label: TranslateLabel;
  };
  productTypeInfo: {
    id: string;
    label: TranslateLabel;
  };
  varietyGradeJoinId: number;
  weightKg: number;
  quantity: string;
  numberOfBoxes: number;
  sealNumber: string;
  varietyGrade: {
    gradeValue: string;
    gradeDisplayText: string;
    varietyValue: string;
    varietyDisplayText: string;
  };
  varieties: DurianVariety[];
  batchlot: string;
  varietyName?: string;
  packingDate: number | null;
}

export interface ShipmentHistoryChange {
  field: string;
  oldValue: string | ShipmentHistoryPackaging | null;
  newValue: string | ShipmentHistoryPackaging | null;
  changeType: 'added' | 'updated' | 'removed' | 'created' | 'sealed';
}

export interface ShipmentHistoryDocument {
  id: string;
  name: string;
  type: string;
  url?: string;
  size?: number;
}

export type SortOrder = 'newest' | 'oldest';

export interface ShipmentHistorySortOption {
  value: SortOrder;
  label: string;
  icon?: string;
}

export interface ShipmentHistory {
  shipmentId: string;
  entries: {
    id: string;
    timestamp: string; // ISO date string
    user: ShipmentHistoryUser;
    action: 'created' | 'updated' | 'sealed';
    changes?: ShipmentHistoryChange[];
    documents?: ShipmentHistoryDocument[];
  }[];
}
