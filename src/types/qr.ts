import { EventStatusEnum } from './event';

export type QrDetail = {
  id: string;
  dateCreated: number;
  dateUpdated: number | null;
  qrcodeId: string;
  nameOfExportingCompany: string;
  orchardRegisterNumber: string;
  productType: string;
  product: unknown; // TODO: update this type
  description: string;
  packingDate: number;
  packageAssigned: string | null;
  shipmentAssigned: string | null;
  packingHouseDoaNumber: string | null;
  status: EventStatusEnum.AVAILABLE | EventStatusEnum.ASSIGNED;
  exportTo: string | null;
  batchlot: string | null;
};

export type CreateAvailableQrPayload = Pick<
  QrDetail,
  'nameOfExportingCompany' | 'orchardRegisterNumber' | 'packingDate' | 'productType'
> &
  Partial<Pick<QrDetail, 'exportTo' | 'description'>>;

export type GenerateQrUrlPayload = {
  quantity: number; // number of Qr label want to gen, on qr management page should be 1
  nameOfExportingCompany: string[];
};

export type GeneratedQrUrl = {
  qrId: string;
  qrUrl: string;
  batchlot: string;
  nameOfExportingCompany: string;
};
