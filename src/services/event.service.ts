import {
  CreateHarvesting,
  EventType,
  PackingHouse,
  PackingHouseDetail,
  ReceiveUpdateRequest,
  ReceivingBatchLotRequest,
  RejectHarvestRequest,
  SealShipmentRequest,
} from 'types';
import { apiService } from './api/clientApi';
import { snakeCase } from 'lodash-es';

import { removeEmptyFields } from 'utils';
import { AppConfig } from 'configs/app-config';
import { UpdateHarvest } from 'hooks/mutates/useUpdateHarvestingMutate';

const externalNextRoute = AppConfig.EXTERNAL_NEXT_ROUTE;

export type GetListEventsParam = {
  page?: number;
  pageSize?: number;
  size?: number;
  search?: string;
  order?: 'asc' | 'desc';
  sortBy?: string; // 'dateCreated' | 'dateUpdated' | 'userUpdated' | 'name' | 'batchlot' | 'quantity' | 'totalVarietiesWeight'
  fromDate?: number;
  toDate?: number;
  recordedBy?: string | string[];
  eventType?: EventType;
  status?: string | string[]; // 'rejected' | 'received' | 'waiting' | 'sealed' | 'available' | 'assigned';
};

export const getListEvents = (params: GetListEventsParam): Promise<{ data: PackingHouse[]; total: number }> => {
  const { page, size, search, order, sortBy, status, fromDate, toDate, recordedBy, eventType } = params;

  // Handle recordedBy parameter - convert array to comma-separated string or single string to lowercase
  let processedRecordedBy: string | undefined;
  if (Array.isArray(recordedBy)) {
    processedRecordedBy = recordedBy.length > 0 ? recordedBy.map((r) => r.toLowerCase()).join(',') : undefined;
  } else {
    processedRecordedBy = recordedBy?.toLowerCase();
  }

  // Handle status parameter - convert array to comma-separated string or single string to lowercase
  let processedStatus: string | undefined;
  if (Array.isArray(status)) {
    processedStatus = status.length > 0 ? status.map((s) => s.toLowerCase()).join(',') : undefined;
  } else {
    processedStatus = status?.toLowerCase();
  }

  const rawParams = {
    page,
    size,
    keyword: (search ?? '').trim(),
    sort: snakeCase(sortBy),
    from_date: fromDate,
    to_date: toDate,
    order: snakeCase(order),
    user_role: processedRecordedBy,
    event_type: eventType,
    status: processedStatus,
  };

  const removeEmptyParams = removeEmptyFields(rawParams);

  return apiService.get(externalNextRoute + `/app-events/${params.eventType || 'packing-house'}`, {
    params: removeEmptyParams,
  });
};

export const getEventDetail = (id: string, isShipment: boolean = false): Promise<{ data: PackingHouseDetail }> => {
  return apiService.get(externalNextRoute + `/v1/search/events/${!isShipment ? 'packing-house' : 'shipment'}/${id}`);
};

export const getListEventsByIdsService = (
  args: {
    eventType?: 'shipping' | 'receiving';
    ids?: string[];
  } = {}
): Promise<{ data: PackingHouse[] }> => {
  return apiService.post(externalNextRoute + '/v1/search/events/packing-house', {
    eventType: args.eventType,
    ids: args.ids,
  });
};

export const receivingHarvest = (args: ReceivingBatchLotRequest): Promise<void> => {
  return apiService.post(externalNextRoute + '/v1/durian/events/receiving', args);
};

export const rejectHarvest = (args: RejectHarvestRequest): Promise<void> => {
  return apiService.post(externalNextRoute + `/v1/durian/events/harvesting/reject/${args.productId}`, {
    reason: args.reason,
  });
};

export const sealShipment = (args: SealShipmentRequest): Promise<void> => {
  return apiService.post(externalNextRoute + `/v1/durian/events/shipment/${args.productId}/seal`, {
    doaOfficerName: args?.doaOfficerName || '',
    sealNumbers: [args?.sealNumbers?.[0] || ''],
  });
};

export const updateReceive = (args: ReceiveUpdateRequest): Promise<void> => {
  return apiService.put(externalNextRoute + `/v1/durian/events/receiving/${args.productId}`, { ...args });
};

export const createHarvesting = (requestBody: CreateHarvesting): Promise<void> => {
  return apiService.post(externalNextRoute + '/v1/durian/events/harvesting', requestBody);
};

export const updateHarvesting = (requestBody: UpdateHarvest): Promise<void> => {
  return apiService.put(
    externalNextRoute + `/v1/durian/events/harvesting/${requestBody.productId}`,
    requestBody.formValues
  );
};

export const deleteHarvesting = (id: string): Promise<void> => {
  return apiService.delete(externalNextRoute + `/v1/durian/events/harvesting/${id}`);
};
