import { DeviceInfo, NotificationsRequest, NotificationsResponse } from 'types/notification';
import { apiService } from './api/clientApi';
import snakecaseKeys from 'snakecase-keys';
import { AppConfig } from 'configs/app-config';


const forwardedPrefix = AppConfig.EXTERNAL_NEXT_ROUTE + '/v1/notification';

export const saveDeviceInfo = async (deviceToken: string) => {
  const payload: DeviceInfo = {
    deviceToken,
    deviceType: 'WEB',
  };
  return apiService.put(`${forwardedPrefix}/auth/set-fcm-config`, payload);
};

export const resetNotiCount = async () => {
  return apiService.put(`${forwardedPrefix}/notification/app/reset-unread-num`);
};

export const updateSeen = async (id: string) => {
  return apiService.put(`${forwardedPrefix}/notification/app/${id}/mark-as-read`);
};

export const getNotifications = async (params: NotificationsRequest): Promise<NotificationsResponse> => {
  return apiService.get(`${forwardedPrefix}/notification/app`, {
    params: snakecaseKeys(params, { deep: true }),
  });
};

export const getNewNotiCount = async (): Promise<{ data: { unread: number } }> => {
  return apiService.get(`${forwardedPrefix}/notification/app/unread-num`);
};

export const markAsAllNotificationService = async (): Promise<void> => {
  return apiService.put(`${forwardedPrefix}/notification/app/mark-as-read-all`, {});
};

