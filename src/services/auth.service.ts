import { AppConfig } from 'configs/app-config';
import { apiService } from 'services/api/clientApi';

const externalNextRoute = AppConfig.EXTERNAL_NEXT_ROUTE;

export const sendOtpService = (args: {
  phoneNumber: string;
}): Promise<{
  expiresIn: number;
  otp: string;
}> => apiService.post(externalNextRoute + '/v1/user/send-otp', args);

export const getNumberAcceptPDPAStatus = (args: {
  phoneNumber: string;
}): Promise<{
  accepted: boolean;
}> => apiService.post(externalNextRoute + '/v1/user/pdpa/status', args);

export const updateNumberAcceptPDPAStatus = (args: { phoneNumber: string; accepted: boolean }): Promise<void> =>
  apiService.post(externalNextRoute + '/v1/user/pdpa/accept', args);

export const loginOtpService = async (args: {
  phoneNumber: string;
  otp: string;
}): Promise<{
  data: {
    accessToken: string;
  };
}> => {
  return await apiService.post(externalNextRoute + '/v1/user/login-otp', args);
};
