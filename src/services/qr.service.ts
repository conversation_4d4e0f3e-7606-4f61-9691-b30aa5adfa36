import { CreateAvailableQrPayload, GeneratedQrUrl, GenerateQrUrlPayload, QrDetail } from 'types';
import { apiService } from './api/clientApi';
import { AppConfig } from 'configs/app-config';

const externalNextRoute = AppConfig.EXTERNAL_NEXT_ROUTE;
const apiEndpoint = externalNextRoute + '/v1/qr-code';

export const getQrDetail = (id: string): Promise<QrDetail> => {
  return apiService.get(`${apiEndpoint}/${id}`);
};

export const createAvailableQr = (payload: CreateAvailableQrPayload): Promise<QrDetail> => {
  return apiService.post(`${apiEndpoint}/create`, payload);
};

export const deleteAvailableQr = (id: string): Promise<{ message: 'Success' }> => {
  return apiService.delete(`${apiEndpoint}/${id}`);
};

export const generateQrUrl = (payload: GenerateQrUrlPayload): Promise<{ data: GeneratedQrUrl[] }> => {
  return apiService.post(`${externalNextRoute}/v1/durian/events/packing/qr-code`, payload);
};
