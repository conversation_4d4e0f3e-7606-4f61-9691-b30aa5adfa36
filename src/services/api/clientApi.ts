import axios, { AxiosError } from 'axios';
import camelcaseKeys from 'camelcase-keys';
import { basePathProd } from 'configs/app-config';
import { capitalize, get } from 'lodash-es';
import { authRoutes } from 'routes/client-routes';
import { clearAuthService } from 'services/internal.service';
import snakecaseKeys from 'snakecase-keys';
import { resetAllStores } from 'store';
import { clearAllBrowserStorage } from 'utils';
import { getMessageByLocale } from 'utils/getMessage';
import { captureApiError } from 'utils/posthog';
import toastMessages from 'utils/toastMessages';

function isFormData(v: unknown): v is FormData {
  return typeof FormData !== 'undefined' && v instanceof FormData;
}

export const createApiInstance = (baseUrl?: string, cache?: boolean) => {
  const apiInstance = axios.create({
    baseURL: baseUrl ?? '/',
    headers: {
      'Cache-Control': cache ? ' public, max-age=604800' : 'no-cache',
      'x-source-channel': '/fe/packaging-house',
    },
    timeout: 60 * 1000,
  });

  apiInstance.interceptors.request.use((config) => {
    if (config.data && isFormData(config.data)) {
      const headers = config.headers ?? {};
      headers['Content-Type'] = 'multipart/form-data';
      config.headers = headers;
    } else if (config.data && typeof config.data === 'object') {
      config.data = snakecaseKeys(config.data, { deep: true });
    }
    return config;
  });

  apiInstance.interceptors.response.use(
    (response) => {
      if (response.data && typeof response.data === 'object') {
        response.data = camelcaseKeys(response.data, { deep: true });
      }
      return response.data;
    },
    async (error: AxiosError) => {
      const messageT = getMessageByLocale();

      if (error.response?.status === 401) {
        toastMessages.error(messageT('unauthorize.unauthorized'));

        resetAllStores();
        await clearAllBrowserStorage();
        await clearAuthService();
        if (location) {
          location.href = `${basePathProd}${authRoutes.login}`;
        }
      } else if (error.response?.status === 500) {
        const messageError = get(error, 'response.data.details.msg', messageT('common.common-error'));
        toastMessages.error(capitalize(messageError));
      }

      captureApiError(error);

      return Promise.reject(error);
    }
  );

  return apiInstance;
};

export const apiService = createApiInstance();
