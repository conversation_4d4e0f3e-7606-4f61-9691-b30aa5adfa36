'use server';

import axios, { AxiosError, AxiosInstance } from 'axios';
import { logger } from 'utils/logger';

export async function createServerApi(
  baseURL: string,
  initialHeaders: Record<string, string>,
  accessToken?: string,
  responseType?: 'text' | 'json',
  contentType?: 'application/json' | 'unset'
): Promise<AxiosInstance> {
  const headers: Record<string, string> = {
    ...initialHeaders,
    'x-source-channel': '/fe/packaging-house',
  };

  if (accessToken) {
    headers['authorization'] = `Bearer ${accessToken}`;
  }

  if (contentType === 'unset') {
    delete headers['Content-Type'];
  }

  const instance = axios.create({
    baseURL,
    headers,
    responseType: responseType ?? 'text',
    timeout: 60 * 1000,
  });

  instance.interceptors.request.use((config) => {
    return config;
  });

  instance.interceptors.response.use(
    (response) => {
      logger.debug('response', {
        status: response.status,
        data: JSON.stringify(response.data),
      });
      return response;
    },
    async (error: AxiosError) => {
      return Promise.reject(error);
    }
  );

  return instance;
}
