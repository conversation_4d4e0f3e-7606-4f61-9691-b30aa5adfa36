import { AppConfig } from 'configs/app-config';
import { createApiInstance } from 'services/api/clientApi';
import { Cutter, DurianVariety, Farm, GetMasterDataResponse, Plot, Province } from 'types';
import { cacheApiOptions } from 'utils/handler';

const appNextRoute = AppConfig.EXTERNAL_NEXT_ROUTE;

const apiService = createApiInstance('/');

export const getPackagingMasterDataService = (): Promise<{ data: GetMasterDataResponse }> => {
  return apiService.get(appNextRoute + '/v1/durian/events/packing/master-data');
};

export const fetchVarietiesService = (): Promise<{
  data: DurianVariety[];
}> =>
  apiService.get(appNextRoute + '/v1/durian/varieties', {
    headers: {
      'cache-control': 'public, max-age=60, stale-while-revalidate=30',
    },
  });

export const fetchFarmService = (
  params: string
): Promise<{
  data: Farm[];
}> => {
  if (params) {
    return apiService.get(appNextRoute + `/v1/search/farms?keyword=${params}`);
  }
  return apiService.get(appNextRoute + `/v1/durian/farm/suggestion`);
};

export const fetchCutterService = (
  params: string
): Promise<{
  data: Cutter[];
}> => {
  if (params) {
    return apiService.get(appNextRoute + `/v1/search/cutters?q=${params}`);
  }
  return apiService.get(appNextRoute + `/v1/search/cutter/suggestion`);
};

export const fetchFarmPlotService = (
  farmId: string
): Promise<{
  data: Plot[];
}> => apiService.get(appNextRoute + `/v1/durian/farm/${farmId}/plots`);

export const fetchProvinceService = (): Promise<{
  data: Province[];
}> => {
  const otpCache = cacheApiOptions['v1/masterdata/edge/location/license-plate-provinces'];

  if (otpCache) {
    return apiService.get(appNextRoute + '/v1/masterdata/edge/location/license-plate-provinces', {
      headers: {
        'cache-control': `public, max-age=${otpCache.maxAge}, stale-while-revalidate=${otpCache.staleWhileRevalidate}`,
      },
    });
  }

  return apiService.get(appNextRoute + '/v1/masterdata/edge/location/license-plate-provinces', {
    headers: {
      'cache-control': 'no-cache, no-store, must-revalidate',
    },
  });
};

export const fetchProvinceServiceByCode = (
  params: string
): Promise<{
  data: Province;
}> => apiService.get(appNextRoute + `/v1/masterdata/edge/location/license-plate-provinces/${params}`);
