#!/bin/bash
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Default values
DOCKERFILE="Dockerfile"
MODE="production"
CLEANUP=true
VERBOSE=false

# Parse command-line arguments
while [[ "$#" -gt 0 ]]; do
    case $1 in
        --local) DOCKERFILE="Dockerfile.local"; MODE="development"; shift ;;
        --no-cleanup) CLEANUP=false; shift ;;
        --verbose) VERBOSE=true; shift ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --local       Test the local development Dockerfile"
            echo "  --no-cleanup  Do not remove containers and images after testing"
            echo "  --verbose     Show more detailed output"
            echo "  --help        Show this help message"
            exit 0
            ;;
        *) echo "Unknown parameter: $1"; exit 1 ;;
    esac
done

log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

# Environment variables for testing
if [ "$MODE" == "production" ]; then
    IMAGE_NAME="dt-packaging-house-prod"
    CONTAINER_NAME="dt-packaging-house-prod-test"
    ENV_VARS=(
        "NEXT_PUBLIC_APP_VERSION=test-version"
    )
else
  IMAGE_NAME="dt-packaging-house-dev"
  CONTAINER_NAME="dt-packaging-house-dev-test"
  if [ -f .env ]; then
    log "Reading environment variables from .env file..."
    ENV_VARS=()
    while IFS= read -r line || [ -n "$line" ]; do
      # Skip empty lines and comments
      [[ -z "$line" || "$line" =~ ^# ]] && continue
      ENV_VARS+=("$line")
    done < .env
  else
    error ".env file not found!"
  fi
fi

# Clean up any existing test containers
cleanup_existing() {
    log "Cleaning up any existing test containers..."
    if docker ps -a | grep -q $CONTAINER_NAME; then
        docker stop $CONTAINER_NAME 2>/dev/null || true
        docker rm $CONTAINER_NAME 2>/dev/null || true
    fi
}

# Build the Docker image
build_image() {
    log "Building $MODE Docker image from $DOCKERFILE..."
    docker build -t $IMAGE_NAME -f $DOCKERFILE .

    if [ $? -ne 0 ]; then
        error "Docker build failed!"
    fi

    log "Image built successfully: $IMAGE_NAME"
}

# Run the container
run_container() {
    log "Running container in $MODE mode..."

    ENV_FLAGS=""
    for env_var in "${ENV_VARS[@]}"; do
        ENV_FLAGS="$ENV_FLAGS -e $env_var"
    done

    if [ "$VERBOSE" == "true" ]; then
        echo "Running: docker run -d --name $CONTAINER_NAME -p 3000:3000 $ENV_FLAGS $IMAGE_NAME"
    fi

    docker run -d --name $CONTAINER_NAME -p 3000:3000 $ENV_FLAGS $IMAGE_NAME

    if [ $? -ne 0 ]; then
        error "Failed to start container!"
    fi

    log "Container started: $CONTAINER_NAME"
}

# Verify the container is running
verify_container() {
    log "Waiting for container to initialize..."
    sleep 10

    if ! docker ps | grep -q $CONTAINER_NAME; then
        error "Container is not running!"
    fi

    log "Container is running. Checking logs..."
    docker logs $CONTAINER_NAME

    if [ "$MODE" == "production" ]; then
        log "Attempting to connect to the app..."
        RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 || echo "000")

        if [ "$RESPONSE" == "000" ]; then
            warn "Could not connect to the application. It may still be starting up."
        elif [ "$RESPONSE" -ge 200 ] && [ "$RESPONSE" -lt 500 ]; then
            log "Successfully connected to the application (HTTP $RESPONSE)."
        else
            warn "Received HTTP $RESPONSE from the application."
        fi
    fi

    log "Container verification completed."
}

# Clean up
cleanup() {
    if [ "$CLEANUP" == "true" ]; then
        log "Cleaning up..."
        docker stop $CONTAINER_NAME
        docker rm $CONTAINER_NAME

        if [ "$VERBOSE" == "true" ]; then
            log "Removed container $CONTAINER_NAME"
            log "To remove the image as well, run: docker rmi $IMAGE_NAME"
        fi
    else
        log "Skipping cleanup. Container $CONTAINER_NAME is still running."
        log "To stop and remove it manually:"
        echo "  docker stop $CONTAINER_NAME"
        echo "  docker rm $CONTAINER_NAME"
    fi
}

# Main execution
cleanup_existing
build_image
run_container
verify_container
cleanup

log "Test completed successfully!"
