/* eslint-disable @typescript-eslint/no-require-imports */
const fs = require('fs');
const path = require('path');

// Function to escape CSV fields
function escapeCSVField(field) {
  // If field contains comma, quote, or newline, wrap in quotes and escape internal quotes
  if (field.includes(',') || field.includes('"') || field.includes('\n')) {
    return `"${field.replace(/"/g, '""')}"`;
  }
  return field;
}

// Function to convert JSON mapping to CSV
function convertJSONToCSV() {
  try {
    // Read JSON file
    const jsonPath = path.join(__dirname, '..', 'translations', 'thai-english-mapping.json');

    if (!fs.existsSync(jsonPath)) {
      console.error('❌ JSON file not found:', jsonPath);
      console.log('💡 Please create the JSON file first');
      return;
    }

    const jsonContent = fs.readFileSync(jsonPath, 'utf8');
    const translations = JSON.parse(jsonContent);

    // CSV header
    let csvContent = 'English,Thai\n';

    // Process each translation
    Object.entries(translations).forEach(([english, thai]) => {
      const escapedEnglish = escapeCSVField(english);
      const escapedThai = escapeCSVField(thai);

      csvContent += `${escapedEnglish},${escapedThai}\n`;
    });

    return csvContent;
  } catch (error) {
    console.error('❌ Error reading JSON file:', error);
    throw error;
  }
}

// Function to generate CSV and save to file
function generateCSV() {
  try {
    const csvContent = convertJSONToCSV();
    const outputPath = path.join(__dirname, '..', 'translations', 'thai-english-mapping.csv');

    fs.writeFileSync(outputPath, csvContent, 'utf8');

    console.log('✅ CSV file generated successfully!');
    console.log(`📍 Location: ${outputPath}`);

    // Count translations
    const lines = csvContent.split('\n').filter(line => line.trim() !== '');
    console.log(`📊 Total translations: ${lines.length - 1} entries`); // -1 for header

    // Show some examples
    console.log('\n📝 Sample translations:');
    const sampleLines = lines.slice(1, 6); // Skip header, take first 5
    sampleLines.forEach(line => {
      const [english, thai] = line.split(',');
      console.log(`  ${english} → ${thai}`);
    });

    return outputPath;
  } catch (error) {
    console.error('❌ Error generating CSV:', error);
    throw error;
  }
}

// Function to generate Excel-compatible CSV with UTF-8 BOM
function generateExcelCSV() {
  try {
    const csvContent = convertJSONToCSV();

    // Add UTF-8 BOM for Excel compatibility
    const bomCSV = '\uFEFF' + csvContent;

    const outputPath = path.join(__dirname, '..', 'translations', 'thai-english-mapping-excel.csv');

    fs.writeFileSync(outputPath, bomCSV, 'utf8');

    console.log('✅ Excel-compatible CSV file generated successfully!');
    console.log(`📍 Location: ${outputPath}`);
    console.log('💡 This file includes UTF-8 BOM for proper Thai display in Excel');

    return outputPath;
  } catch (error) {
    console.error('❌ Error generating Excel CSV:', error);
    throw error;
  }
}

// Main function
function main() {
  const args = process.argv.slice(2);
  const generateExcel = args.includes('--excel');

  if (generateExcel) {
    generateExcelCSV();
  } else {
    generateCSV();
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { convertJSONToCSV, generateCSV, generateExcelCSV };
