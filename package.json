{"name": "dt-packaging-house-web", "version": "1.0.1", "private": true, "scripts": {"dev": "next dev  --turbopack", "postbuild": "cp -r .next/static .next/standalone/.next/", "build": "next build", "build:local": "NEXT_PUBLIC_LOCAL=true npm run build", "build:test": "tsc --noEmit && next build --debug", "start": "node .next/standalone/server.js", "lint": "next lint", "lint:fix": "next lint --fix", "dev:https": "next dev --experimental-https", "prepare": "husky", "build:no-lint": "yarn next build --no-lint", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}\"", "translate:th": "node scripts/convert-thai-language.cjs", "translate:json-to-csv": "node scripts/convert-json-to-csv.js", "translate:csv-to-json": "node scripts/convert-csv-to-json.js", "docker:compose:up": "docker-compose up -d", "docker:compose:down": "docker-compose down --rmi all --volumes --remove-orphans", "generate:changelog": "node scripts/generate-changelog.js --categorized", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --no-verbose --coverage --silent", "test:coverage:badger": "jest --no-verbose --coverage && coverage-badger -e 70 -g 50 -r coverage/clover.xml -d coverage-brand/", "test:report": "jest --ci --reporters=default --reporters=jest-junit"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^5.0.1", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@mui/x-data-grid": "^8.0.0", "@mui/x-date-pickers": "^8.0.0", "@tanstack/query-sync-storage-persister": "^5.74.6", "@tanstack/react-query": "^5.74.4", "@tanstack/react-query-persist-client": "^5.74.6", "axios": "^1.8.4", "camelcase-keys": "^9.1.3", "chance": "^1.1.12", "classnames": "^2.5.1", "country-flag-icons": "^1.5.19", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "firebase": "^11.7.1", "framer-motion": "^12.7.4", "html-to-image": "^1.11.13", "html2canvas": "^1.4.1", "husky": "^9.1.7", "immer": "^10.1.1", "jwt-decode": "^4.0.0", "libphonenumber-js": "^1.12.9", "lodash-es": "^4.17.21", "next": "15.3.0", "next-intl": "^4.1.0", "notistack": "^3.0.2", "posthog-js": "^1.249.2", "rc-virtual-list": "^3.18.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.55.0", "react-imask": "^7.6.1", "react-infinite-scroll-component": "^6.1.0", "react-number-format": "^5.4.4", "react-otp-input": "^3.1.1", "react-qr-code": "^2.0.15", "react-qrcode-logo": "^3.0.0", "react-virtualized-auto-sizer": "^1.0.26", "react-virtuoso": "^4.12.6", "react-window": "^1.8.11", "sanitize-html": "^2.17.0", "snakecase-keys": "^8.0.1", "swiper": "^11.2.6", "uuid": "^11.1.0", "zod": "^3.24.3", "zustand": "^5.0.3"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/eslintrc": "^3", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/chance": "^1", "@types/crypto-js": "^4", "@types/eslint-plugin-security": "^3", "@types/gtag.js": "^0.0.20", "@types/jest": "^30.0.0", "@types/lodash-es": "^4.17.12", "@types/node": "^24.0.3", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-window": "^1", "@types/sanitize-html": "^2", "@types/swiper": "^6.0.0", "babel-jest": "^30.0.1", "coverage-badger": "^1.0.1", "eslint": "^9", "eslint-config-next": "15.3.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-security": "^3.0.1", "fs-extra": "^11.3.0", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.1", "jest-environment-jsdom": "^30.0.1", "jest-transform-stub": "^2.0.0", "prettier": "^3.5.3", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5"}, "packageManager": "yarn@4.9.1"}