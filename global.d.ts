import enMessages from './locales/en.json';

type Messages = typeof enMessages;

type EnvVariables = {
  NEXT_PUBLIC_ASSET_DOMAIN?: string;
  NEXT_PUBLIC_PATH?: string;
  NEXT_PUBLIC_ASSET_HOST?: string;
  NEXT_PUBLIC_HOST?: string;
  NEXT_PUBLIC_ENV?: 'development' | 'staging' | 'production';
  NEXT_PUBLIC_WEB_VERSION?: string;
  NEXT_PUBLIC_POSTHOG_KEY?: string;
  NEXT_PUBLIC_POSTHOG_HOST?: string;
  NEXT_PUBLIC_PACKAGE_VERSION?: string;
  NEXT_PUBLIC_GTAG_ID?: string;
};

declare module 'next-intl' {
  interface AppConfig {
    Messages: typeof enMessages;
  }
}
declare global {
  interface Window {
    __ENV__?: EnvVariables;
  }
}
